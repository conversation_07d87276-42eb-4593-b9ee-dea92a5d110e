class CreateDesignCampaignDiscounts < ActiveRecord::Migration
  def change
    create_table :design_campaign_discounts do |t|
      t.references :design, index: true, foreign_key: true
      t.references :seller_campaign, index: true, foreign_key: true
      t.references :designer, index: true, foreign_key: true
      t.decimal :discount
      t.timestamps

    end
    add_index :design_campaign_discounts, [:design_id, :seller_campaign_id], unique: true, name: 'index_unique_design_campaign'
  end
end
