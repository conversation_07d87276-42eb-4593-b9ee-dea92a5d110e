class CreateOptionTypeValuesComboVariantsJoinTable < ActiveRecord::Migration
  def change
    create_join_table :combo_variants, :option_type_values, 
                      table_name: :option_type_values_combo_variants do |t|
      t.index [:combo_variant_id, :option_type_value_id], 
              name: 'idx_combo_var_otv'
      t.index [:option_type_value_id, :combo_variant_id], 
              name: 'idx_otv_combo_var'
    end
  end
end
