class CreatePlaylists < ActiveRecord::Migration
  def change
    create_table :playlists do |t|
      t.references :page, index: true, foreign_key: true
      t.string :name
      t.string :slug
      t.string :title
      t.text :description
      t.text :meta
      t.text :country_code, array: true, default: []
      t.integer :category_id
      t.integer :designer_id
      t.string :order_by
      t.string :search_term
      t.string :sort_by, default: 'new'
      t.integer :playlist_page_limit, default: 10
      t.text :search_query
      t.integer :position

      t.timestamps
    end

    add_index :playlists, :slug, unique: true
    add_index :playlists, :position
    add_index :playlists, :category_id
    add_index :playlists, :designer_id
  end
end
