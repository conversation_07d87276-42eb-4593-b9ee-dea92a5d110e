class CreateBoostedDesigns < ActiveRecord::Migration
  def change
    create_table :boosted_designs do |t|
      t.integer :design_id, index: true, foreign_key: true
      t.integer :category_id, index: true, foreign_key: true
      t.text :previous_states
      t.timestamp :boost_start_time
      t.timestamp :boost_end_time
      t.decimal :boost_fee, precision: 10, scale: 2
      t.integer :designer_id, index: true, foreign_key: true

      t.timestamps null: false
    end
  end
end
