class CreateComboVariants < ActiveRecord::Migration
  def change
    create_table :combo_variants do |t|
      t.integer  "design_id"
      t.integer  "quantity"
      t.integer  "position"
      t.datetime "created_at",                        null: false
      t.datetime "updated_at",                        null: false
      t.boolean  "show_variant",       default: true
      t.integer  "price"
      t.string   "design_code"
      t.boolean  "stitched",           default: true
      t.integer  "transfer_price"
    end

    add_index :combo_variants, :design_id, name: "index_combo_variants_on_design_id"
  end

end
