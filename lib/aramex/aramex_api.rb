class AramexApi

  #Aramex Credentional
  def aramex_client_info_hash
    aramex = Mirraw::Application.config.aramex
    client_info = {
      'UserName' => aramex[:user_name], 'Password' => aramex[:password],  'Version' => aramex[:version],
      'AccountNumber' => aramex[:account_number], 'AccountPin' => aramex[:account_pin], 'AccountEntity' => aramex[:account_entity],
      'AccountCountryCode' => aramex[:account_country_code]
    }
  end

  # Aramex Shipment Hash
  def aramex_shipment_hash(shipper_details, customer_details, mirraw_details, items, attachments, pay_type, total, mirraw_reference)
    aramex = Mirraw::Application.config.aramex
    shipper_details[:address][:country_code] ||= 'IN'

    aramex_shipment = {
      'Shipment' => {
        'Reference1' => '',
        'Reference2' => '',
        'Reference3' => '',
        'Shipper' => {
          'Reference1' => mirraw_reference,
          'Reference2' => '',
          'AccountNumber' => '',
          'PartyAddress' => aramex_address_hash(shipper_details[:address]),
          'Contact' => aramex_contact_hash(shipper_details[:contact]),
        },
        'Consignee' => {
          'Reference1' => '',
          'Reference2' => '',
          'AccountNumber' => '',
          'PartyAddress' => aramex_address_hash(customer_details[:address]),
          'Contact' => aramex_contact_hash(customer_details[:contact]),
        },
        'ThirdParty' => {
          'Reference1' => '',
          'Reference2' => '',
          'AccountNumber' => aramex[:account_number],
          'PartyAddress' => aramex_address_hash(mirraw_details[:address]),
          'Contact' => aramex_contact_hash(mirraw_details[:contact]),
        },
        'ShippingDateTime' => Time.now.iso8601,
        'DueDate' => Time.now.iso8601,
        'Comments' => '',
        'PickupLocation' => '',
        'OperationsInstructions' => '',
        'AccountingInstrcutions' => '',
        'Details' => aramex_details_hash(pay_type, total, items),
        'Attachments' => [{:FileName => '', :FileExtension => '', :FileContents => ''}],
        'TransportType' => '',
        'PickupGUID' => ''
      }
    }
  end

  # Aramex Adress
  def aramex_address_hash(address=nil)
    if address.present?
      multi_line_address = address[:street].present? ? Shipment.multi_line_address(address[:street], 50) : []
      address[:country_code] ||= 'IN'
      aramex_address = Hash.new
      aramex_address['Line1'] = multi_line_address[0].present? ? multi_line_address[0] : ''
      aramex_address['Line2'] = multi_line_address[1].present? ? multi_line_address[1] : ''
      aramex_address['Line3'] = multi_line_address[2].present? ? multi_line_address[2] : ''
      
      # Do not be a hero and insert params out here
      # Aramex tries to validate address against pincode,state, city.
      # Our customers aren't smartest people and neither is aramex
      # Both sides suck at correctness
      aramex_address['City'] = address[:city]
      aramex_address['StateOrProvinceCode'] = address[:state_code]

      aramex_address['PostCode'] = address[:pincode]
      aramex_address['CountryCode'] = address[:country_code]
    else
      aramex_address = Hash.new
      aramex_address['Line1'] = ''
      aramex_address['Line2'] = ''
      aramex_address['Line3'] = ''
      aramex_address['City'] = ''
      aramex_address['StateOrProvinceCode'] = ''
      aramex_address['PostCode'] = ''
      aramex_address['CountryCode'] = ''
    end

    aramex_address
  end

  # Aramex Details in Hash
  def aramex_details_hash(pay_type, total, items)
    aramex_details = Hash.new
    aramex_details['Dimensions'] = {'Length' => 10, 'Width' => 10, 'Height' => 10, 'Unit' => 'cm'}
    aramex_details['ActualWeight'] = {'Unit' => 'KG', 'Value' => items.count/10.0}
    aramex_details['ChargeableWeight'] = {'Unit' => 'KG', 'Value' => items.count/10.0}
    aramex_details['DescriptionOfGoods'] = items.first.design.categories.first.name
    aramex_details['GoodsOriginCountry'] = 'IN'
    aramex_details['NumberOfPieces'] = 0
    items.each do |item|
      aramex_details['NumberOfPieces'] += item.quantity
    end
    aramex_details['ProductGroup'] = 'DOM'
    if pay_type == COD
      aramex_details['ProductType'] = 'CDA'
    else
      aramex_details['ProductType'] = 'ONP'
    end
    aramex_details['PaymentType'] = '3'
    aramex_details['PaymentOptions'] = ''
    aramex_details['CustomsValueAmount'] = {'CurrencyCode' => 'INR', 'Value' => total}
    if pay_type == COD
      aramex_details['CashOnDeliveryAmount'] = {'CurrencyCode' => 'INR', 'Value' => total}
      aramex_details['Services'] = 'CODS'
    else
      aramex_details['Services'] = ''
    end
    aramex_details['Items'] = []
    items.each do |item|
      aramex_details['Items'] << [ 'ShipmentItem' => {
        'PackageType' => item.design.categories.first.name,
        'Quantity' => item.quantity,
        'Weight' => {'Unit' => 'KG','Value' => 0.1},
        'Comments' => '',
        'Reference' => ''
      }]
    end
    aramex_details
  end

  # Retrun Contact hash
  def aramex_contact_hash(contact = nil)
    aramex_contact = Hash.new
    aramex_contact['Department'] = ''
    aramex_contact['PersonName'] = contact.present? ? contact[:name] : ''
    aramex_contact['Title'] = ''
    aramex_contact['CompanyName'] = contact.present? ?contact[:name]:''
    aramex_contact['PhoneNumber1'] = contact.present? ?contact[:phone_number1]:''
    aramex_contact['PhoneNumber1Ext'] = ''
    aramex_contact['PhoneNumber2'] = contact.present? ?contact[:phone_number2]:''
    aramex_contact['PhoneNumber2Ext'] = ''
    aramex_contact['FaxNumber'] = ''
    aramex_contact['CellPhone'] = contact.present? ?contact[:phone_number1]:''
    aramex_contact['EmailAddress'] = contact.present? ?contact[:email]:''
    aramex_contact['Type'] = ''
    aramex_contact
  end

  # Aramex Domestic
  def aramex_shipment_api(shipper_details, customer_details, mirraw_details, items,  mirraw_reference, pay_type, total)
    attachments = nil
    shipment_creation_request_hash = {
      'ClientInfo' => aramex_client_info_hash,
      'Transaction' => {'Reference1' => mirraw_reference, 'Reference2' => '', 'Reference3' => '', 'Reference4' => '', 'Reference5' => ''},
      'Shipments' => [aramex_shipment_hash(shipper_details, customer_details, mirraw_details, items, attachments, pay_type, total, mirraw_reference)],
      'LabelInfo' => {'ReportID' => 9729, 'ReportType' => 'URL'}
    }
    shipment_hash = {:error => false, :error_text => ''}
    client = Savon.client(:wsdl => Rails.root + 'lib/aramex/shipping-services-api-wsdl.wsdl',:convert_request_keys_to => :none,:strip_namespaces => true, :pretty_print_xml => true)
    begin
      response = client.call(:create_shipments, :message => shipment_creation_request_hash)
      response_data = response.hash[:envelope][:body][:shipment_creation_response]
      if response_data[:has_errors]
        shipment_hash[:error] = true
        [response_data[:shipments][:processed_shipment][:notifications][:notification]].flatten.each do |notification|
          notification.each do |key, value|
            shipment_hash[:error_text] += value + ' '
          end
        end
      else
        shipment_hash[:awb] = response_data[:shipments][:processed_shipment][:id]
        print_label_shipment = {"ClientInfo" => shipment_creation_request_hash["ClientInfo"], "Transaction" => shipment_creation_request_hash["Transaction"], "ShipmentNumber" => shipment_hash[:awb], "LabelInfo" =>  shipment_creation_request_hash["LabelInfo"]}
        print_response = client.call(:print_label, :message => print_label_shipment)
        print_response_data = print_response.hash[:envelope][:body][:label_printing_response]
        shipment_hash[:label_url] = print_response_data[:shipment_label][:label_url]
        shipment_hash[:label_file_contents] = response_data[:shipments][:processed_shipment][:shipment_label][:label_file_contents]
      end
    rescue Savon::SOAPFault, Savon::HTTPError, Savon::InvalidResponseError => error
      ExceptionNotifier.notify_exception(
        Exception.new("Aramex Shipment Error -"),
        data: {payload: shipment_creation_request_hash, response: response, error: error.message}
      )
      shipment_hash[:error] = true
      shipment_hash[:error_text] = error.message
    end
    shipment_hash
  end

  #Aramex International
  def aramex_international_shipment_api(customer_details, mirraw_details, items, mirraw_reference, pay_type, total, weight, currency_code, invoice_number)
    attachments = nil
    shipment_creation_request_hash = {
      'ClientInfo' => aramex_client_info_hash,
      'Transaction' => {'Reference1' => mirraw_reference, 'Reference2' => '', 'Reference3' => '', 'Reference4' => '', 'Reference5' => ''},
      'Shipments' => [aramex_international_shipment_hash(customer_details, mirraw_details, items, attachments, pay_type, total, mirraw_reference, weight, currency_code, invoice_number)],
      'LabelInfo' => {'ReportID' => 9729, 'ReportType' => 'URL'}
    }
    OrderMailer.report_mailer("Aramex Api REQ RES","REQ : #{shipment_creation_request_hash}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
    shipment_hash = {:error => false, :error_text => ''}
    wsdl_format = ['development','staging','test'].include?(Rails.env) ? 'lib/aramex/dev-shipping-services-api-wsdl.wsdl' : 'lib/aramex/shipping-services-api-wsdl.wsdl'
    client = Savon.client(:wsdl => Rails.root + wsdl_format,:convert_request_keys_to => :none,:strip_namespaces => true, :pretty_print_xml => true)
    begin
      response = HTTParty.send(:post, "https://ws.aramex.net/ShippingAPI.V2/Shipping/Service_1_0.svc/json/CreateShipments", body: shipment_creation_request_hash.to_json, headers: {'Content-Type' => 'application/json'})
      #
      if (response_data = response.parsed_response.try(:deep_symbolize_keys)).present?
        if !response_data[:HasErrors]
          shipment_hash[:awb] =  response_data[:Shipments][0][:ID]
          print_label_shipment = {"ClientInfo" => shipment_creation_request_hash["ClientInfo"], "Transaction" => shipment_creation_request_hash["Transaction"], "ShipmentNumber" =>  response_data[:Shipments][0][:ID], "LabelInfo" =>  shipment_creation_request_hash["LabelInfo"]}
          print_response = client.call(:print_label, :message => print_label_shipment)
          print_response_data = print_response.hash[:envelope][:body][:label_printing_response]
          shipment_hash[:label_url] = print_response_data[:shipment_label][:label_url]
          shipment_hash[:label_file_contents] = response_data[:Shipments][0][:ShipmentLabel][:LabelFileContents]
        else
          ExceptionNotify.sidekiq_delay.notify_exceptions( 'Aramax API Errror',response, { params: shipment_creation_request_hash })
          shipment_hash[:error] = true
          response_data[:Notifications].each do |notification|
            shipment_hash[:error_text] += notification[:Message] + ' '
          end
        end
      else
        response = client.call(:create_shipments, :message => shipment_creation_request_hash)
        ExceptionNotify.sidekiq_delay.notify_exceptions( 'Aramax API Errror',response, { params: shipment_creation_request_hash })
        response_data = response.hash[:envelope][:body][:shipment_creation_response]
        if response_data[:has_errors]
          shipment_hash[:error] = true
          [response_data[:shipments][:processed_shipment][:notifications][:notification]].flatten.each do |notification|
            notification.each do |key, value|
              shipment_hash[:error_text] += value + ' '
            end
          end
        end
      end
    rescue Savon::SOAPFault, Savon::HTTPError, Savon::InvalidResponseError => error
      shipment_hash[:error] = true
      shipment_hash[:error_text] = error.message
    end
    shipment_hash
  end

  #Aramex International Details
  def aramex_international_details_hash(mirraw_reference,pay_type, total, items, weight, currency_code = 'INR', invoice_number)
    aramex_details = Hash.new
    aramex_details['Dimensions'] = {'Length' => 10, 'Width' => 10, 'Height' => 10, 'Unit' => 'cm'}
    aramex_details['ActualWeight'] = {'Unit' => 'KG', 'Value' => weight.to_f}
    aramex_details['ChargeableWeight'] = {'Unit' => 'KG', 'Value' => weight.to_f}
    aramex_details['DescriptionOfGoods'] = items.first[:name]
    aramex_details['GoodsOriginCountry'] = 'IN'
    aramex_details['NumberOfPieces'] = 1
    aramex_details['ProductGroup'] = 'EXP'
    aramex_details['ProductType'] = 'PPX'
    aramex_details['PaymentType'] = 'P'
    aramex_details['PaymentOptions'] = ''
    if pay_type == COD && currency_code=='AED' #curreny_code USD is required for COD international in aramex
      aramex_details['CustomsValueAmount'] = {'CurrencyCode' => currency_code, 'Value' => total}
      aramex_details['CashOnDeliveryAmount'] = {'CurrencyCode' => currency_code, 'Value' => total}
       aramex_details['Services'] = 'CODS'
    elsif pay_type == COD
      aramex_details['CustomsValueAmount'] = {'CurrencyCode' => "USD", 'Value' => total}
      aramex_details['CashOnDeliveryAmount'] = {'CurrencyCode' => "USD", 'Value' => total}
       aramex_details['Services'] = 'CODS'
    else
       aramex_details['CustomsValueAmount'] = {'CurrencyCode' => currency_code, 'Value' => (['development','staging','test'].include?(Rails.env) ? 0.5 : total)}
      aramex_details['Services'] = ''
    end
    aramex_details['Items'] = []
    items.each do |item|
      aramex_details['Items'] <<{
        'PackageType' => item[:name],
        'Quantity' => item[:quantity],
        'Weight' => {'Unit' => 'KG','Value' => weight.to_f},
        'Comments' => '',
        'Reference' => ''
      }
    end
    aramex_details.merge!(aramex_international_shipment_hash_additional_properties(mirraw_reference,invoice_number))
  end

  def aramex_international_shipment_hash_additional_properties(mirraw_reference,invoice_number)
    aramex_additional = Hash.new
    aramex_additional['AdditionalProperties'] = additional_data_maker(
    [['CustomsClearance','ShipperTaxIdVATEINNumber',MIRRAW_GST_NUMBER],
     ['CustomsClearance','InvoiceDate',DateTime.now.strftime('%m/%d/%Y'),],
     ['CustomsClearance','InvoiceNumber',invoice_number],
     ['CustomsClearance','ExporterType','UT']
    ])
    aramex_additional
  end

  def additional_data_maker(d = [])
    arr = []
    d.each do |row|
      arr << {
        'CategoryName' => row[0],
        'Name' => row[1],
        'Value' => row[2],
      }
    end
    arr
  end

  # Aramex International
  def aramex_international_shipment_hash(customer_details, mirraw_details, items, attachments, pay_type, total, mirraw_reference, weight, currency_code,invoice_number)
    aramex = Mirraw::Application.config.aramex
    aramex_shipment = {
      'Reference1' => '',
      'Reference2' => '',
      'Reference3' => '',
      'Shipper' => {
        'Reference1' => mirraw_reference,
        'Reference2' => '',
        'AccountNumber' => aramex[:account_number],
        'PartyAddress' => aramex_address_hash(mirraw_details[:address]),
        'Contact' => aramex_contact_hash(mirraw_details[:contact]),
      },
      'Consignee' => {
        'Reference1' => '',
        'Reference2' => '',
        'AccountNumber' => '',
        'PartyAddress' => aramex_address_hash(customer_details[:address]),
        'Contact' => aramex_contact_hash(customer_details[:contact]),
      },
      'ThirdParty' => {
        'Reference1' => '',
        'Reference2' => '',
        'AccountNumber' => '',
        'PartyAddress' => pay_type == COD ? aramex_address_hash() : aramex_address_hash(customer_details[:address]),
        'Contact' => pay_type == COD ? aramex_contact_hash() : aramex_contact_hash(customer_details[:contact]),
      },
      'ShippingDateTime' => "\/Date(#{Date.current.strftime('%Q')}+0530)\/",
      'DueDate' => "\/Date(#{Date.current.strftime('%Q')}+0530)\/",
      'Comments' => '',
      'PickupLocation' => '',
      'OperationsInstructions' => '',
      'AccountingInstrcutions' => '',
      'Details' => aramex_international_details_hash(mirraw_reference, pay_type, total, items, weight, currency_code,invoice_number),
      #'Attachments' => [],
      'TransportType' => '',
      'PickupGUID' => ''
    }
  end


  #Aramex Tracking

  def aramex_track_api(numbers)
    numbers = Array(numbers).flatten
    shipment_tracking_request_hash = {
      'ClientInfo' => aramex_client_info_hash,
      'Transaction' => {'Reference1' => '', 'Reference2' => '', 'Reference3' => '', 'Reference4' => '', 'Reference5' => ''},
      'Shipments' => {'string' => numbers},
      'GetLastTrackingUpdateOnly' => true,
    }
    shipment_hash = {:error => false, :error_text => ''}
    client = Savon.client(:wsdl => Rails.root + 'lib/aramex/shipments-tracking-api-wsdl.wsdl',:convert_request_keys_to => :none, :pretty_print_xml => true)
    begin
      response = client.call(:track_shipments, :message => shipment_tracking_request_hash)
      response_data = response.hash[:envelope][:body][:shipment_tracking_response]
      if response_data[:has_errors]
        shipment_hash[:error] = true
        [response_data[:notifications][:notification]].flatten.each do |notification|
          notification.each do |key, value|
            shipment_hash[:error_text] += value + ' '
          end
        end
      else
        shipment_hash[:results] = []
        shipment_hash[:invalid_tracking_numbers] = []
        response_data[:tracking_results].each do |index_key, value|
          if value.kind_of?(Array)
            value.each do |item|
              if item.kind_of?(Hash) && item[:value].present? && item[:value].kind_of?(Hash) && item[:value][:tracking_result].present?
                shipment_hash[:results] << item[:value][:tracking_result]
              end
            end
          elsif value.kind_of?(Hash)
            if value[:value].present? && value[:value].kind_of?(Hash) && value[:value][:tracking_result].present?
              shipment_hash[:results] << value[:value][:tracking_result]
            end
          end
        end
      end
    rescue Savon::SOAPFault, Savon::HTTPError, Savon::InvalidResponseError => error
      shipment_hash[:error] = true
      shipment_hash[:error_text] = error.message
     end
    shipment_hash
  end

  #Aramex Pickup Creation
  def self.aramex_create_pickup(designer,pickup_info,mirraw_reference)
    pickup_creation_request_hash = {
      'ClientInfo'=> AramexApi.new.aramex_client_info_hash,
      'Transaction'=> {"Reference1"=> mirraw_reference, "Reference2"=> "", "Reference3"=> "", "Reference4"=> "", "Reference5"=> ""},
      'Pickup'=> aramex_pickup_hash(designer,pickup_info)
    }
    pickup_resp = {}
    client = Savon.client(wsdl: Rails.root + 'lib/aramex/shipping-services-api-wsdl.wsdl',convert_request_keys_to: :none,strip_namespaces: true, pretty_print_xml: true,  log: true,logger: Rails.logger,log_level: :debug) 
    begin
      response = client.call(:create_pickup, message: pickup_creation_request_hash)
      response_data = response.hash[:envelope][:body][:pickup_creation_response]
      if response_data[:has_errors]
        pickup_resp[:error] = true
        [response_data[:notifications][:notification]].flatten.each do |notification|
          notification.each do |key, value|
            pickup_resp[:error_text] = "#{pickup_resp[:error_text]}, #{value}" 
          end
        end
      else
        pickup_resp[:error] = false
        pickup_resp[:pickup_id] = response_data[:processed_pickup][:id]
        pickup_resp[:guid] = response_data[:processed_pickup][:guid]
        pickup_resp[:reference1] = response_data[:processed_pickup][:reference1]
      end
    rescue Savon::SOAPFault, Savon::HTTPError, Savon::InvalidResponseError => error
      pickup_resp[:error] = true
      pickup_resp[:error_text] = error.message
    end
    pickup_resp
  end

  def self.aramex_pickup_hash(designer,pickup_info)
    pickup_details,address,contact = {},{},{}
    address[:street] = designer.street
    address[:city] = designer.city
    address[:state] = designer.state
    address[:country_code] = 'IN'
    address[:pincode] = designer.pincode
    contact[:name] = designer.name
    contact[:phone_number1] = designer.phone
    contact[:phone_number2] = designer.alt_phone
    contact[:email] =  designer.email
    pickup_details["PickupAddress"] = AramexApi.new.aramex_address_hash(address)
    pickup_details["PickupContact"] = AramexApi.new.aramex_contact_hash(contact)
    pickup_details["PickupLocation"] = 'Shop'
    pickup_details["PickupDate"] = pickup_info[:pickup_date]
    pickup_details["ReadyTime"] = pickup_info[:ready_time]
    pickup_details["LastPickupTime"] = pickup_info[:last_pickup_time]
    pickup_details["ClosingTime"] = pickup_info[:closing_time]
    pickup_details["Comments"] = "HELLO"
    pickup_details["Reference1"] = "Pickup For Designer Id #{designer.id}"
    pickup_details["Reference2"] = nil
    pickup_details["Vehicle"] = nil
    pickup_details["Shipments"] = nil
    pickup_details["PickupItems"] = {"PickupItemDetail" => aramex_pickup_items(pickup_info)}
    pickup_details["Status"] = "Ready"
    pickup_details
  end

  def self.aramex_pickup_items(pickup_info)
    pickup_item_details = {}
    pickup_item_details["ProductGroup"] = pickup_info[:product_group]
    pickup_item_details["ProductType"] = pickup_info[:product_type]
    pickup_item_details["NumberOfShipments"] = pickup_info[:number_of_shipments]
    pickup_item_details["PackageType"] = nil
    pickup_item_details["Payment"] = 'P'
    pickup_item_details["ShipmentWeight"] = pickup_info[:weight]
    pickup_item_details["ShipmentVolume"] = pickup_info[:volume]
    pickup_item_details["NumberOfPieces"] = 0
    pickup_item_details["CashAmount"] = nil
    pickup_item_details["ExtraCharges"] = nil
    pickup_item_details["ShipmentDimensions"] = {"Length"=>0,"Width"=>0,"Height"=>0,"Unit"=> 'cm'}
    pickup_item_details["Comments"] = nil
    pickup_item_details
  end

  def self.aramex_csb_integration(tracking_number,invoice_number, invoice_items, currency_code)
    aramex_details = Mirraw::Application.config.aramex
    response_for_csb_authenticaton = HTTParty.post(aramex_details[:csb_auth_token_url],:headers => { 'Content-Type' => 'application/x-www-form-urlencoded'},:body => { 'username' => aramex_details[:export_user_name], 'password' => aramex_details[:export_password] ,'grant_type' => aramex_details[:grant_type] , 'LoginUsername' => aramex_details[:login_user_name]}).to_h

    if response_for_csb_authenticaton['access_token'].present?
      token = 'bearer ' + response_for_csb_authenticaton['access_token']
      item_details = []
      total_shipment_price = 0
      invoice_items.each do |item|
        item_details << { 'CommodityType' => item[:designable_type],
                           'GoodsDescription' => item[:name],
                           'WhetherMEIS' => item[:meis_scheme] ? 'yes': 'no',
                           'HSCode' => item[:hsn_code],
                           'Quantity' => item[:quantity],
                           'InvoiceRatePerUnit' => item[:item_discounted_price],
                           'TotalFOBValue' => item[:quantity]*item[:item_discounted_price],
                           'TotalCESSPaid':0,
                           'ItemDetailsRef1':"",'ItemDetailsRef2':"",'ItemDetailsRef3':""}
        total_shipment_price += item_details.last['TotalFOBValue']
      end
      request_body_hash = { "Accountnumber": aramex_details[:account_number],
                "username": aramex_details[:export_user_name],
                "password": aramex_details[:export_password],
                "LoginUsername": aramex_details[:login_user_name],
                "HAWBNo": tracking_number,
                "CurrencyCode": currency_code,
                "ShipmentValue":  total_shipment_price,
                "WhetherSupplyforExportIsOnPaymentOfIGST": "no",
                "WhetherAgainstBondOrUT": "yes",
                "TermsOfTrade": "FOB",
                "InvoiceNumber": invoice_number,
                "InvoiceDate": DateTime.now.strftime('%d/%m/%Y'),
                "TotalIGSTIfAny": 0,
                "HAWBDataRef1": "", "HAWBDataRef2": "", "HAWBDataRef3": "",
                "ItemDetails": item_details
          }
      response_for_csb = HTTParty.post(aramex_details[:csb_url],:headers => {'Content-Type' => 'application/json', 'Authorization' => token }, :body => request_body_hash.to_json).to_h
      response_for_csb['Status'] == 1 ? (return true,nil) : (return false,response_for_csb['Message'])
    else
      return false,response_for_csb_authenticaton['error']
    end
  end

end