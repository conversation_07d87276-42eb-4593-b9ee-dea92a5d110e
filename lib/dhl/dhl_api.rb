require 'dhl/DHLDriver.rb'

class DhlApi  
  def quote(request)
    dhl_api = Mirraw::Application.config.dhl
    dhl_service = IDHLService.new
    request=request.merge({
      "isDutiable" => "Y",
      "pickupHours" => "07",
      "pickupMinutes" => "00",
      "globalProductCode" => "P",
      "localProductCode" => "P" ,
      "paymentAccountNumber" =>  dhl_api[:account_number],
    })
    response = dhl_service.postQuote(request).postQuoteResult
    return Hash.from_xml(response)
  end
  def createShipment(details,use_csb)
    dhl_api = Mirraw::Application.config.dhl
    dhl_service = IDHLService.new
    shipment_constant={
      "shipmentpurpose" => "CSBV",
      "shippingPaymentType" => "S" ,
      "shipperAccNumber" => dhl_api[:account_number] ,
      "billingAccNumber" => dhl_api[:account_number] ,
      # "dutyPaymentType" => "R" ,  
      "shipGlobalProductCode" => "P",
      "shipLocalProductCode" => "P" ,
      "shipperId" => dhl_api[:account_number] ,
      "siteId" => dhl_api[:site_id] ,
      "password" => dhl_api[:password] ,
      "isResponseRequired" => "N" ,
      "shipperBusinessPartyTypeCode" => '',
      "shipperRegistrationNumber" => '',
      "shipperRegistrationNumberTypeCode" => '',
      "shipperRegistrationNumberIssuerCountryCode" => '',
      "exporter_RegistrationNumber" => '',
      "exporter_RegistrationNumberTypeCode" => '',
      "exporter_RegistrationNumberIssuerCountryCode" => '',
      "exporter_BusinessPartyTypeCode" => '',
      "exporter_CompanyName" => "",
      "exporter_AddressLine1" => "",
      "exporter_AddressLine2" => "",
      "exporter_AddressLine3" => "",
      "exporter_City" => "",
      "exporter_Division" => "",
      "exporter_DivisionCode" => "",
      "exporter_PostalCode" => "",
      "exporter_CountryCode" => "",
      "exporter_CountryName" => "",
      "exporter_PersonName" => "",
      "exporter_PhoneNumber" => "",
      "exporter_Email" => "",
      "exporter_MobilePhoneNumber" => "",
      "registrationNumber" => '',
      "registrationNumberTypeCode" => '',
      "registrationNumberIssuerCountryCode" => '',
      "businessPartyTypeCode" => '',
      'signatureName'          => '',
      'signatureTitle'         => '',
      'licenseNumber'          => '',
      'expiryDate'             => '',
      'freightCharge'          => 0,
      'insuranceCharge'        => 0,
      'cessCharge'             => 0,
      'reverseCharge'          => 0,
      'payerGSTVAT'            => '',
      "billToCompanyName" => '',
      "billToContactName" => '',
      "billToAddressLine1" => '',
      "billToCity" => '',
      "billToPostcode" => '',
      "billToSuburb" => '',
      "billToState" => '',
      "billToCountryName" => '',
      "billToCountryCode" => '',
      "billToPhoneNumber" => '',
      "invoicevalueinword" => '',
      "labelReq" => "Y",
      "aCCOUNT_NO" => "**************",
      "nFEI_FLAG" => "NO",
      "gOV_NONGOV_TYPE" => "P",
      "websiteOrEcomOperatorName" => MIRRAW_DOMAIN,
      "plt_signature" => "Yes",
      "plt_signature_Base64" => PLT_SIGNATURE
    }
    # On new method change, one needs to define empty strings for even those optional params that we don't want to use.    
    request = details.merge(shipment_constant)
    response =
      if use_csb
        dhl_service.postShipment_CSBV(request).postShipment_CSBVResult
      else
        dhl_service.postShipment_CSBIV_Cargo(request).postShipment_CSBIV_CargoResult
      end
      response_label =  response.split(";")[0]
      response_label.delete! "\\"
      # Sucessful response: response is URL
      # UnSucessful response: response is XML with error message
    if response_label =~  URI::ABS_URI
      #for sucessful response
      # OrderMailer.report_mailer("DHL Api REQ RES","REQ : #{request} RES : #{response}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
      tracking_number=response_label.split("/")[-1].split(".")[0].split("_")[0]  #extract tracking number
      url=response_label
      return {:tracking_number => tracking_number, :url => url}
    else
      ExceptionNotify.sidekiq_delay.notify_exceptions('DHL API error', Hash.from_xml(response_label)["ConditionData"],{ params: request })
      raise  Hash.from_xml(response_label)["ConditionData"] || "Error has occured"
    end
  end
  def track(awbno)
    dhl_service = IDHLService.new
    request={
      "awbnumber"=>awbno
    }
    response=dhl_service.postTracking(request).postTrackingResult
    return Hash.from_xml(response)
    rescue => error
    return {"Error" => error.message}
  end
end