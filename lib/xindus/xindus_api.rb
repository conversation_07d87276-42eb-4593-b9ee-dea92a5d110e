class XindusApi 
  
  def login_api
    set_credentials
    Rails.cache.fetch('xindus_auth_token', expires_in: 90.days){
    url = @credentials["base_url"] + "partner-service/oapi/v1/login"
    phone_number = @credentials["phone_number"]
    phone_prefix = @credentials["phone_prefix"]
    password = @credentials["password"]
    email = @credentials["email"]
    body = {'email' => email , 'password' => password , 'phone' => phone_number , 'phone_prefix' => phone_prefix}
    headers = {'Content-Type' => 'application/json'}
    response = HTTParty.post(url, :body => body.to_json, :headers => headers)
    response = handle_exceptions(response, "Login API Error:")
    return response["data"].first["TOKEN"]
    }
  end 

  def create_shipment_api(request_payload)
    response = post_api(request_payload, "partner-service/api/v1/booking", "Create Shipment Error:")
    return response["data"].first["awb"]
  end

  def payment_api(awb_number)
    body = {"awb" => awb_number , "useCoinsIfAvailable" => "false"}
    response = post_api(body , "partner-service/api/v1/booking/payment", "Payment API Error:")   
  end

  def shipping_label_api(awb_number) 
    type = '4X6'
    body = {"awb" => awb_number , "type" => type}
    response = post_api(body , "partner-service/api/v1/booking/shipping-label?awb=#{awb_number}&type=#{type}" , "Get Label Error:")
    return response["data"].first
  end

  def shipment_invoice_api(awb_number)
    response = post_api({} , "partner-service/api/v1/booking/shipment-invoice?awb=#{awb_number}&confirm=true"  , "Get Invoice Error:")
    return response["data"].first["data"]
  end

  def post_api(request_payload , end_point , method_message)
    set_credentials
    auth_token = login_api
    url = @credentials["base_url"] + end_point
    headers = {'Content-Type' => 'application/json' , 'at' => auth_token}
    response = HTTParty.post(url, :body => request_payload.to_json, :headers => headers)
    response = handle_exceptions(response, method_message) 
  end 

  def tracking_api(awb_number)
    set_credentials
    auth_token = login_api
    url = @credentials["base_url"] + "partner-service/api/v1/booking/tracking?awb=#{awb_number}"
    headers = {'Content-Type' => 'application/json', 'at' => auth_token}
    response = HTTParty.get(url, :headers => headers)
    response = handle_exceptions(response, "Tracking API Error:")
    return response
  end

  def handle_exceptions(response, message)
    return response if !response.has_key?("errors") && response['status'] != 'error' 
    raise message + (response['errorDescription'] || "API Internal Server error")
  end

  def notify(klass, message, req, res, error)
    ExceptionNotifier.notify_exception(
      Exception.new("Xindus Shipment Error- #{message}"),
      data: {payload: req, response: res, error: error.inspect}
    )
  end 

  private 

  def set_credentials
    env = (Rails.env.production? || Rails.env.admin?) ? 'production' : 'stagging'
    @credentials = YAML.load_file("#{Rails.root}/config/xindus.yml")[env]
  end 


end