require 'json'
require 'ups/ups_token_service'
class UPSApi
  def initialize(order)
    @credentials = YAML.load_file("#{Rails.root}/config/ups.yml")
    @order = order
  end

  def get_token
    Rails.cache.fetch('ups_access_token', expires_in: 2.hours) do
      new_access_token = UpsTokenService.generate_access_token
    end
  end

  def create_shipment(shipment_request, query)
    version = "v2403"
    # if Rails.env.production?
    #   request_url = "https://onlinetools.ups.com/ship/#{version}/shipments"
    # else
    #   request_url = "https://wwwcie.ups.com/ship/#{version}/shipments"
    # end

    request_url = "https://onlinetools.ups.com/api/shipments/#{version}/ship"
    body = shipment_request.to_json
    access_token = get_token
    headers = {
      "transId" => @order.number.to_s, 
      "transactionSrc" => @order.pay_type, 
      "Authorization" => "Bearer #{access_token}",
      "Content-Type" => 'application/json'
    }
    @request_hash = shipment_request
    @api_response = HTTParty.post(request_url, :headers => headers , :body => body)
  end

  def track(inquiryNumber)
    request_url = "https://onlinetools.ups.com/api/track/v1/details/" + inquiryNumber
    access_token = get_token
    query = {
      "locale": "en_US",
      "returnSignature": "false",
      "returnMilestones": "false",
      "returnPOD": "false"
    }

    headers = {
      "transId" => @order.number.to_s, 
      "transactionSrc" => @order.pay_type, 
      "Authorization" => "Bearer #{access_token}",
      "Content-Type" => 'application/json'
    }
    response = HTTParty.get(request_url, :headers => headers, :params => query)
    [response.code,response.body]
  end
end


