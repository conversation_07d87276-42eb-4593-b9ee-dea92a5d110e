class OptionTypeValue < ActiveRecord::Base
  #attr_accessible :name, :option_type, :p_name, :position
  belongs_to :option_type
  has_and_belongs_to_many :variants
  has_and_belongs_to_many :combo_variants, 
                  join_table: :option_type_values_combo_variants

  def self.get_option_type_values_list
    Rails.cache.fetch('all_variant_sizes_with_name',expires_in: 24.hours) do
      list = {}
      s_clause = "option_type_values.id, LOWER(regexp_replace(option_types.name ||' '||option_type_values.name,'[\s.?]','_', 'g')) as opt_name, option_types.category_id, option_types.category_names"
      OptionTypeValue.select(s_clause).where('option_types.category_id is not null').joins(:option_type).each {|o| list[o[:opt_name]] = {:id => o.id, :category_id => [o[:category_id]], category_names: o[:category_names]}}
      categories = Category.where(id: list.collect{|k,v| v[:category_id]}.flatten.compact.uniq).group_by(&:id)
      list.each do |option_type, option_type_attr|
        leave_ids = categories[option_type_attr[:category_id].first].try(:first).try(:cached_self_and_descendants_id) || []
        option_type_attr.delete(:category_names).to_s.strip.split(',').each do |category_name|
          leave_ids.push(*Category.getids(category_name.strip))
        end
        option_type_attr[:category_id] += leave_ids.to_a.uniq.map(&:to_s)
      end
      list
    end
  end
end
