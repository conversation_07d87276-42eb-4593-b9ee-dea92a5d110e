# == Schema Information
#
# Table name: line_items
#
#  id                :integer         not null, primary key
#  design_id         :integer
#  cart_id           :integer
#  quantity          :integer         default(1)
#  created_at        :datetime
#  updated_at        :datetime
#  note              :text
#  designer_order_id :integer
#  snapshot_price    :integer
#
require 'item_search'
class LineItem < ActiveRecord::Base
  #attr_accessible :design, :snapshot_price, :scaling_factor, :vendor_selling_price
  belongs_to :design
  belongs_to :cart
  belongs_to :designer_order
  has_one :designer,through: :designer_order
  has_one :order, through: :designer_order
  has_one :order_delivery_nps_info,through: :order,source: :delivery_nps_info
  has_one :payment_order, through: :designer_order, class_name: 'Order', foreign_key: 'order_id' #, select: 'orders.id,orders.number,orders.created_at,orders.name,orders.country,orders.confirmed_at'
  has_many :shipment_invoice_item
  belongs_to :return 
  belongs_to :return_designer_order
  has_many :warehouse_line_item_joins
  has_many :rack_lists_warehouse_line_items, through: :warehouse_line_item_joins
  has_many :warehouse_orders, through: :warehouse_line_item_joins
  has_many :warehouse_line_items, through: :warehouse_line_item_joins
  has_many :line_item_addons, dependent: :destroy, autosave: true
  has_many :addon_type_values, through: :line_item_addons
  has_many :vendor_addon_items,  -> {where(snapshot_payable_to: 'designer')}, class_name: :LineItemAddon
  has_many :discount_line_items
  has_many :designer_issues
  has_many :reverse_commissions,as: :element
  has_many :rtv_shipment_line_items
  belongs_to  :rack_list, foreign_key: 'rack_list_code', primary_key: 'code'
  has_many :rtv_shipments, through: :rtv_shipment_line_items
  has_many :tailoring_info, as: :item
  belongs_to :variant
  belongs_to :combo_variant
  has_many :quality_check_values
  belongs_to :shipment
  belongs_to :shipment_bucket
  belongs_to :inbound_shipment, class_name: 'Shipment'
  belongs_to :fabric_measured_by_user, class_name: "Account", foreign_key: :fabric_measured_by
  belongs_to :qc_done_by_account, class_name: 'Account', foreign_key: :qc_done_by
  has_many :stitching_measurements
  belongs_to :stylist_receive
  belongs_to :categories_design, primary_key: :design_id, foreign_key: :design_id
  has_many :categories, through: :categories_design
  has_many :stitching_addons
  has_one :tailored_measurement, -> {unscope(:where).where('parent_measurement_id is not null and stitching_label_url is not null and measurement_group = ?', 'tailor')}, class_name: 'StitchingMeasurement'
  has_many :scans
  has_many :bucket_relations, as: :entity
  has_many :stitching_bucket_relations, -> {where('entity_type = ? and bucket_kind = ?', 'LineItem', 'StitchingBucket')}, class_name: 'BucketRelation', foreign_key: 'entity_id'
  has_many :packaging_bucket_relations, -> {where('entity_type = ? and bucket_kind = ?', 'LineItem', 'PackagingBucket')}, class_name: 'BucketRelation', foreign_key: 'entity_id'

  has_many :item_promotion_trackings
  has_one :latest_stitching_handover_scan, -> {where("lower(scans.scan_type) = 'handover to stitching'").order(id: :desc)}, class_name: :Scan
  has_one :latest_stitching_handover_receive_scan, -> {where("lower(scans.scan_type) = 'handover received by stylist'").order(id: :desc)}, class_name: :Scan
  has_many :ipending_related_scans, -> {where('lower(scans.scan_type) in (?)', ['handover to stitching', 'handover to stylist for working', 'handover to warehouse for rtv', 'handover to warehouse'])}, class_name: :Scan
  has_one :latest_replacement_dispatched_scan, -> {where("scans.scan_type = 'Replacement Dispatched From Vendor'").order(id: :desc)}, class_name: :Scan
  has_one :latest_rtv_handover_to_warehouse_scan, -> {where('lower(scans.scan_type) = ?', 'handover to warehouse for rtv').order(id: :desc)}, class_name: :Scan
  belongs_to :replaced_product, class_name: 'LineItem', foreign_key: 'replacement_item_id'
  has_many :events, as: :eventable
  has_many :stylist_events, -> {where('events.eventable_type = ? and events.note_type = ?', 'LineItem', 'stylist')}, class_name: 'Event', foreign_key: :eventable_id 
  has_many :vendor_events, -> {where('events.eventable_type = ? and events.note_type <> ?', 'LineItem', 'stylist')}, class_name: 'Event', foreign_key: :eventable_id 
  has_many :survey_answers, as: :surveyable
  has_many :process_dates, as: :processable
  has_many :communication_topics, as: :communicable
  has_many :open_communication_topics, -> {where('communicable_type = ? and status in (?)', 'LineItem', ['open', 'reopen'])}, class_name: 'CommunicationTopic', foreign_key: 'communicable_id'
  has_one :measurement_approved_process_date, -> {where('processable_type = ? and operation_process_id = ?', 'LineItem', OperationProcess::PROCESS_IDS['Measurement Approved'][0])}, class_name: 'ProcessDate', foreign_key: 'processable_id'
  has_one :tailor_receiving_process_date, -> {where('processable_type = ? and operation_process_id = ?', 'LineItem', OperationProcess::PROCESS_IDS['Tailor Receiving'][0])}, class_name: 'ProcessDate', foreign_key: 'processable_id'
  has_one :tailor_sent_process_date, -> {where('processable_type = ? and operation_process_id = ?', 'LineItem', OperationProcess::PROCESS_IDS['Tailor Sent'][0])}, class_name: 'ProcessDate', foreign_key: 'processable_id'
  has_many :searcher_items
  has_one :rfd_searcher_item, ->{ where(searcher_items: {kind: 'rfd'}) }, class_name: 'SearcherItem', foreign_key: :line_item_id
  has_one :rtv_searcher_item, ->{ where(searcher_items: {kind: 'rtv'}) }, class_name: 'SearcherItem', foreign_key: :line_item_id
  has_one :stitching_searcher_item, ->{ where(searcher_items: {kind: 'stitching'}) }, class_name: 'SearcherItem', foreign_key: :line_item_id
  has_many :searcher_entities, as: :searchable_entity
  has_one :stitching_searcher_entity, ->{ where(searcher_entities: {type: 'StitchingSearcherEntity'}) }, class_name: 'SearcherEntity', foreign_key: :searchable_entity_id
  has_many :rack_logs, as: :loggable

  has_many :logs, as: :entity
  
  scope :sane_items, -> { where("line_items.status IS NULL OR line_items.status = 'buyer_return'") }
  scope :dispatchable, -> {where("line_items.status IS NULL")}
  scope :sane_line_items, -> { where('designer_orders.state NOT IN (?)', ['new', 'pending', 'canceled', 'rto', 'vendor_canceled']).where('line_items.status IS NULL OR line_items.status <> ?', 'cancel') }
  scope :not_canceled_items, -> { where('designer_orders.state NOT IN (?)', ['canceled', 'rto', 'vendor_canceled']).where('line_items.status IS NULL OR line_items.status <> ?', 'cancel') }
  scope :sor_items, -> { where(available_in_warehouse: true) }
  scope :buyer_returned_items, -> { where(status: "buyer_return") }
  scope :find_by_id, -> (item_id) { find(item_id) }
  scope :prepared_items, -> {
    where(
      "(line_items.stitching_required = 'Y' AND line_items.stitching_done = 'Y') OR " \
      "(line_items.stitching_required IS NULL AND line_items.received = 'Y' AND line_items.qc_status = 'true') OR " \
      "(line_items.stitching_required = 'N' AND line_items.received = 'Y' AND line_items.qc_status = 'true')" 
    )
  }
  has_one :inward_detail
  #scope :sold_items, -> { joins(:designer_order).where.not(designer_orders: {pickup: nil}).where(line_items: {status: nil}) }
  # has_many :addon_type_values,:through => :line_item_addons
  # has_many :addon_type_value_groups,:through => :addon_type_values

  IPENDING_PROCESS_NAMES = ['pending', 'critical', 'replacement_pending', 'vendor_canceled', 'Stitching Done', 'QC Failed-Issue Unresolved', 'Issue Created', 'Tailor RTV', 'Processing', 'Rejected', 'Approved', 'Phone Call', 'Tailor Assign', 'Tailor Inscan', 'Alteration', 'Reassign', 'Tailor Received', 'Handover To Stitching', 'Handover To Warehouse', 'Non Stitching Process', 'Rack Assigned', 'Addon Tag Present']
  SAREE_STITCHING_ADDON_NAMES = ['Fall and Pico', 'Petticoat Stitching', 'Pre-Stitched Saree']
  FIXED_RACK_SCAN_CODES = ['rack_0']

  attr_reader :pair_product

  enum buy_get_free: { international: 1, domestic: 2, both: 3 }
  include BuyGetFreeEligibility
  include RackLoggable

  after_create :delete_item_count_for_design, :track_events

  after_save if: :qc_status_changed? do |item|
    if item.qc_status 
      AppEvent::ItemEvent.new(item.id, "QC Approved").trigger_clevertap_event_deliver_later
    else 
      AppEvent::ItemEvent.new(item.id, "QC Rejected").trigger_clevertap_event_deliver_later
    end 
  end
  
  validates_presence_of :design_id, :quantity
  validates_numericality_of :quantity, :greater_than_or_equal_to => 1

  paperclip_hash={
  storage: :s3,
  s3_credentials: AWS_ACCESS_KEYS,
  path: ":class/:id/:basename_:style.:extension",
  bucket: ENV['S3_BUCKET'],
  url: ":s3_alias_url",
  s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate },
  s3_host_alias: "s3-#{ENV['AWS_REGION']}.amazonaws.com/#{ENV['S3_BUCKET']}"}

  has_attached_file :return_image, paperclip_hash
  validates_attachment_file_name :return_image, :matches => [/png\Z/, /jpe?g\Z/, /gif\Z/]
  do_not_validate_attachment_file_type :return_image

  has_attached_file :return_image2, paperclip_hash
  validates_attachment_file_name :return_image2, :matches => [/png\Z/, /jpe?g\Z/, /gif\Z/]
  do_not_validate_attachment_file_type :return_image2
 
  def pair_product=(value)
    @pair_product = value.to_s == 'true'
  end

  def sub_total(return_type=RETURN_SCALED)
    self.price(return_type) * quantity
  end
  
  def thumbnail_image
    image :thumb
  end

  def self.not_canceled
    #LineItem states- 1.null(new) 2.cancel 3.buyer_return
    select{|lineitem| lineitem.status != 'cancel'}
  end

  def image size
    return design.master_img.photo.url(size) if design.association(:master_img).loaded? && design.master_img.present?
    return design.master_image.photo.url(size) if design.master_image
    return nil
  end

  def small_image
    image :small
  end
  
  def thumbnail_image_order_new
    return '//d1lycdyubshuoc.cloudfront.net/' + design.master_image.photo.path(:thumb) if design.master_image
    return nil
  end
  
  def designer_name
    design.designer.name
  end
  
  def name
    "line_item_#{id}"
  end

  def title
    design.title
  end
  
  def eta
    design.shipping_time(item.order.try(:country))
  end

  def get_sort_date(confirm_date, process_name, last_process_date=nil)
    if (association_name = (process_name.parameterize('_') + '_process_date')) && self.respond_to?(association_name.to_sym) && (process_lpst_date = self.send(association_name.to_sym).try(:lpst_date)) 
      process_lpst_date
    elsif last_process_date.present? || (process_name == 'Measurement Approved' && (last_process_date = latest_stitching_handover_scan.try(:scanned_at)).present?)
      (last_process_date + OperationProcess::PROCESS_IDS[process_name][1].to_i.day)
    else
      current_process_priority = OperationProcess::PROCESS_IDS[process_name][3]
      confirm_date ? (confirm_date + OperationProcess::PROCESS_IDS.collect{|process_name, data| data[1] if (data[3] <= current_process_priority)}.compact.sum.to_i.day) : Time.current
    end
  end

  def price(return_type=RETURN_SCALED)
    if (line_item_price = self.snapshot_price(return_type)).blank?
      self.snapshot_price = (variant || design).effective_price_for_country(self.designer_order.order.country_code)
      self.save!
    end
    line_item_price
  end

  def snapshot_price(return_type=RETURN_SCALED)
    if is_cart_dynamic_and_item_is_not_ordered?
      (variant || design).effective_price(return_type)
    elsif self[:snapshot_price].present?
      if return_type == RETURN_SCALED
        (self[:snapshot_price] * scaling_factor).to_i
      else
        self[:snapshot_price]
      end #+ self.line_item_addons.collect(&:snapshot_price).sum
    else
      nil
    end
  end

  def get_replacement_product
    if (item = self.replaced_product).present? && (item.status == 'cancel' || ['canceled','vendor_canceled'].include?(item.designer_order.state))
      item
    else
      self
    end
  end

  def get_child_replaced_product
    if self.replacement_item_id.present? && self.replaced_product.replacement_item_id.present?
      return self.replaced_product.get_child_replaced_product
    else
      return self.id
    end
  end

  def get_replaced_product(new_item_id)
    if self.replacement_item_id.present?
      if self.replacement_item_id != new_item_id
        return self.replaced_product.get_child_replaced_product
      else
        return nil
      end
    else
      return self.id
    end
  end

  def elligible_for_meis?(shipper_name= nil)
    (addon_type_values.collect(&:name) & ELLIGIBLE_MEIS_TYPE).present? && !MEIS_NON_ELIGIBLE_SHIPPER.include?(shipper_name.to_s.downcase)
  end

  def approx_weight(country_code = Design.country_code)    
    return  0 if design.cart_addon? 
    design.approx_weight(country_code) * quantity
  end

  def buy_get_free
    return if design.cart_addon?
    is_cart_dynamic_and_item_is_not_ordered? ? PromotionPipeLine.bmgnx_hash.presence && design.buy_get_free : self[:buy_get_free]
  end

  def scaling_factor
    is_cart_dynamic_and_item_is_not_ordered? ? design.get_scale : self[:scaling_factor]
  end

  def snapshot_currency_rate
    is_cart_dynamic_and_item_is_not_ordered? ? CONVERSION_RATES[snapshot_country_code] : self[:snapshot_currency_rate]
  end

  def snapshot_country_code
    is_cart_dynamic_and_item_is_not_ordered? ? Design.country_code : self[:snapshot_country_code]
  end

  def get_country_code
    self[:snapshot_country_code]
  end

  def is_cart_dynamic_and_item_is_not_ordered?
    not_ordered? && LINE_ITEM_SYNC_DESIGN
  end

  def not_ordered?
    designer_order_id.blank?
  end

  def assign_synced_attributes
    self.snapshot_country_code = snapshot_country_code
    self.snapshot_price = snapshot_price(RETURN_NORMAL)
    self.scaling_factor = scaling_factor
    self.snapshot_currency_rate = snapshot_currency_rate
    self.buy_get_free = buy_get_free
    line_item_addons.each(&:assign_synced_attributes)
  end

  def unscaled_price # for rails admin
    self.price(RETURN_NORMAL)
  end

  def find_hscode_gst(per_price_item,hsn_code = nil, to_save = true)
    hsn_code, gst_rate = design.get_hsn_code_gst_rate(per_price_item, hsn_code: hsn_code)
    self.update_attributes(purchase_gst_rate: gst_rate,purchase_hsn_code: hsn_code) if to_save
    return [hsn_code,gst_rate]
  end

  # repeated method name

  # def get_addons
  #   addons = self.line_item_addons
  #   items = Array.new
  #   addons.each do |addon|
  #     items << {:name => addon.addon_type_value.name, :price => addon.snapshot_price}
  #   end
  #   return items
  # end

  def add_addon(addon)
    addon_type_value_id = addon[:addon_type_value_id]
    addon_option_value_id = addon[:addon_option_value_id]
    if addon_type_value_id != 0
      atv = (AddonTypeValue.where(:design_id => self.design.id, :id => type_value_id))[0]
      if atv.id.present?
        line_item_addon = LineItemAddon.new(:line_item_id => self.id,:addon_type_value_id => atv.id,:snapshot_price => atv.price, scaling_factor: (VENDOR_ADDON_SCALE.to_f.abs.nonzero? || 1))
        line_item_addon.save!
      end
    end
  end

  def ga_data(other_data={})
    {
      price: snapshot_price, quantity: quantity
    }.merge!(design.ga_data(other_data))
  end
 
  def get_breadcrumb
    breadcrumb_obj = Breadcrumb.new(:design_show, category: self.design.categories[0], design: self.design)
    breadcrumb = breadcrumb_obj.breadcrumb
    breadcrumb.pop if breadcrumb.count > 2
    breadcrumb
  end

  def line_item_wise_ga_data(index, breadcrumbs_array = [], country_code = "US")
    design = self.design
    # price = design.price(1)
    # discount_price = self.snapshot_price(1)
    rate = (self.snapshot_currency_rate).to_f
    coupon_discounts = (self.cart.coupon_discounts/rate).to_f.round(2) if self.cart.present? && self.cart.coupon.present?
    item_total = (self.cart.items_total_without_addons(rate) - self.cart.bmgnx_discounts/rate).round(2) if self.cart.present?
    coupon_percent = coupon_discounts.present? && item_total !=0 ? ((coupon_discounts / item_total) * 100).to_f.round(2) : 0
    # if country_code != "IN"
      # price, discount_price = design.calculate_international_prices(country_code)
    # end
    price, discount_price = design.calculate_international_prices(country_code)
    cc = CurrencyConvert.find_by(country_code: country_code)
    market_rate = cc.present? ? cc.market_rate : 1
    total_addons_price = self.line_item_addons.flatten.sum{|addon| (addon.snapshot_price/rate).round(2)}
    total_addons_price = (total_addons_price * market_rate).round(2)
    bmgnx_hash = PromotionPipeLine.bmgnx_hash
    bmgnx_free_items = self.cart.get_free_items_bmgnx(bmgnx_hash) if bmgnx_hash.present? && self.cart.present? && self.cart.get_free_items_bmgnx.present?
    if bmgnx_free_items.present? && bmgnx_free_items.keys.include?(self.id)
      # bmgnx_message = bmgnx_hash[:x] != 100 ? "b1g1" : (bmgnx_free_items[self.id][1] == self.quantity ? "b1g1" : "b1g1")
      bmgnx_message = "B1G1"
    end
    discounted_price = discount_price - (discount_price * coupon_percent / 100)
    line_item_total_price = ((discounted_price + total_addons_price)).round(2)
    line_item_ga_hash = design.generate_design_hash(index, breadcrumbs_array, false).merge(
      price: line_item_total_price,
      quantity: self.quantity,
      discount: ((price - discount_price)).round(2),
      item_customization: (total_addons_price).round(2),
      item_offer: bmgnx_message || ""
    )
    line_item_ga_hash
  end 



  def add_addons(design,addons,standard_size)
    user_line_item_addons = Array.new
    
    is_different = false
    
    addons.each do |key, addon|
      designer_id = design.design_check
      addon_type_value =
          if designer_id.present?
            AddonTypeValue.where(:id => addon['addon_type_value_id'], :designer_id => designer_id).first
          else
            AddonTypeValue.cached_addon_type(addon['addon_type_value_id'])
          end
      if addon_type_value.present?
        line_item_addon = LineItemAddon.new(:addon_type_value_id => addon_type_value.id, :snapshot_price => addon_type_value.effective_price(self.design))
        if addon['addon_option_value_id'].present? || standard_size != 'false'
          if standard_size != 'false' && Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(design.designable_type) && ['Standard Stitching','Regular Blouse Stitching'].include?(addon_type_value[:name])
            line_item_addon.add_addon_option_values(addon['addon_option_value_id'].to_a, design,standard_size)
          else
            line_item_addon.add_addon_option_values(addon['addon_option_value_id'].to_a, design)
            line_item_addon.add_unstitched_size
          end
        end
        
        is_different ||= !self.line_item_addons.find { |lia| lia.is_duplicate?(line_item_addon) }
        user_line_item_addons << line_item_addon
      end
    end
    
    if is_different
      self.clear_addons
      self.line_item_addons << user_line_item_addons
    end
  end
  
  def clear_addons
    self.line_item_addons.destroy_all if self.line_item_addons.present?
  end

  def is_product_plus_size?
    self.line_item_addons.any?{|la| la.notes.to_s.downcase.include?('plus size')}
  end

  def get_addon_option_value_from_notes(addon_option_type)
    self.line_item_addons.select{|la| la.notes.to_s.downcase.include?(addon_option_type) && la.addon_type_value.name.downcase.include?('blouse')}.first.try(:notes).to_s.downcase.split(",").select{|note| note.include?(addon_option_type)}.first.to_s.split(":")[1].to_s.gsub("\n","")
  end

  def add_variant(variant)
    if variant.present?
      self.variant = variant
      notes = ''
      variant.option_type_values.each do |optv|
        notes = optv.option_type.name + ' ' + optv.name
      end
      self.add_note(notes)
    end
  end

  def note_without_bmgn
    if note.present?
      separator = ' ... '
      notes_without_bmgn = note.split(separator).reject do |note_iter|
        /\| Discount items \(B\d+G\d+\): \d+ At \d+ % off \|/ === note_iter
      end
      notes_without_bmgn.join(' ... ')
    else
      ''
    end
  end

  def add_note(note, ignore_if_exists: false)
    separator = '...'
    
    if self.note.blank?
      self.note = note
    elsif !self.note.include?(note) || !ignore_if_exists
      self.note += " #{separator} #{note}"
    end
  end

  def update_quantity_variant(quantity)
    if quantity <= self.variant.quantity
      self.quantity = quantity
    else
      self.quantity = self.variant.quantity
    end
  end

  def update_quantity(quantity)
    if quantity <= self.design.quantity
      self.quantity = quantity
    else
      self.quantity = self.design.quantity
    end
  end

  def update_item_quantity(type, by = 1)
    if (product = (self.variant || self.design)).present?
      product.assign_quantity(type, by, :quantity)
    end
  end
  
  def check_out_of_stock?
    if self.variant.present?
      return true if self.variant.quantity == 0
    else
      return true if self.design.quantity == 0
    end
    return false
  end

  def max_quantity_on_cart
    klass = self.variant.presence || self.design
    klass.sor_available? ? klass.in_stock_warehouse.to_i : klass.quantity
  end

  def warehouse_use_item(item, rack_list_wli=nil) #item can be design or variant
    notes = 'Warehouse SOR tag call'
    if self.designer_order.present? && ['canceled', 'vendor_canceled'].exclude?(self.designer_order.state) && self.status.blank? && item.in_stock_warehouse.to_i >= self.quantity
      notes +=' initiated'
      if self.assign_warehouse_rack(rack_list_wli)
        notes +='rack aasign'
        self.assign_attributes(available_in_warehouse: true)
        notes +='avail true'
        self.set_fresh_payout
        item.decrement!(:in_stock_warehouse, self.quantity)
        notes +='quantity reduced'
        # item.decrement!(:quantity, self.quantity)
        notes +=' successfully'
        if self.designer.vacation_mode_on?
          if self.design.variants.present?
            item.state = 'on_hold' if self.design.variants.all? { |variant| variant.in_stock_warehouse.to_i.zero? }
          elsif item.in_stock_warehouse.to_i.zero?
            item.state = 'on_hold' 
          end
        end
        if WarehouseOrder.check_reorder_status && item.in_stock_warehouse < (item.ordering_quantity * 25.0/100.0).ceil
          SidekiqDelayGenericJob.perform_async("WarehouseOrder", nil, "reorder", {"#{line_item.class}": line_item.id})
          #WarehouseOrder.sidekiq_delay.reorder(self)
        end
      end
    end
    self.designer_order.add_notes_without_callback(notes, 'System')
  end

  def set_fresh_payout
    self.assign_attributes(vendor_selling_price: (self.sor_available? ? self.warehouse_line_items.first.snapshot_price : (self.variant || self.design).get_vendor_selling_amount))
    vendor_price_changed = self.vendor_selling_price_changed?
    self.save

    vendor_payout, _, _, _ = self.designer_order.get_vendor_payout
    self.designer_order.payout = vendor_payout  -  self.designer_order.mirraw_shipping_cost.to_i if vendor_price_changed
    self.designer_order.save if vendor_price_changed
  end

  def update_warehouse_quantity(new_quantity,item)
    if new_quantity <= (self.quantity + item.in_stock_warehouse)
      rack_list_wli = self.get_current_rack_list_item
      if rack_list_wli.present? && rack_list_wli.quantity_present >= new_quantity
        rack_list_wli.increase_rack_quantity(self.quantity - new_quantity)
        item.increment(:in_stock_warehouse, self.quantity - new_quantity)
        item.increment(:quantity, self.quantity - new_quantity)
        item.save
        self.quantity = new_quantity
        self.save
      else
        self.unassign_warehouse_rack(rack_list_wli)
        item.in_stock_warehouse += self.quantity
        item.in_stock_warehouse -= new_quantity
        item.quantity += self.quantity
        item.quantity -= new_quantity
        item.save
        self.assign_warehouse_rack
        unless self.rack_list_code.present?
          self.available_in_warehouse = false
          item.in_stock_warehouse += new_quantity
          item.save
        end
        self.quantity = new_quantity
        self.save
      end
    end
  end

  def self.remove_sor_tag(item_id, mark_lost, account_id)
    line_item = LineItem.find item_id
    item = line_item.variant_id.present? ? line_item.variant  : line_item.design
    account = Account.find_by_id(account_id)
    line_item.warehouse_cancel_item(item, mark_lost, account)
    line_item.designer_order.add_notes_without_callback("SOR Tag Removed #{item_id}",'Admin', account)
  end

  def warehouse_cancel_item(item, mark_lost = false, account = nil,avl_in_warehouse = true)
    if available_in_warehouse
      self.unassign_warehouse_rack(nil, mark_lost, account)
      self.update_attributes(available_in_warehouse: false) if avl_in_warehouse
      self.set_fresh_payout
      self.update_attributes(received: nil,received_by: nil,received_on: nil)
      item.increment!(:in_stock_warehouse, self.quantity)
      WarehouseLineItemJoin.where(line_item_id: self.id).first.destroy if avl_in_warehouse
      # item.increment!(:quantity, self.quantity)
    end
  end

  def get_addons
    return self.line_item_addons
  end
  
  def paid_addons?
    if self.line_item_addons.blank?
      false
    else
      inhouse_prestitch = self.inhouse_pre_stitching
      self.line_item_addons.each do |addon|
        if addon.snapshot_price.present? && 
          (addon.snapshot_price > 0 && (addon.snapshot_payable_to.downcase == 'mirraw'|| inhouse_prestitch)) || ["standard_stitching", "custom_stitching"].include?(addon.addon_type_value.addon_type_value_group.try(:name))
          return true
        end
      end
      return false
    end
  end

  def already_stitched?
    available_in_warehouse && line_item_addons.find{|addon| addon.size_chart_id.present? && addon.size_chart_id != SizeChart::UNSTITCHED_SIZE_CHART.id}
  end

  def inhouse_pre_stitching
    if self.item_details['Pre-Stitch'].present?
      return true
    else
      if self.line_item_addons.any?{|lia| lia.check_inhouse_pre_stitching}
        store_item_details(detail_name: 'Pre-Stitch', detail_value: 'True')
        return true
      end
    end
    return false
  end

  # Input params - none
  #
  # Returns cost of line_item with addons

  def discount_price_with_addons(return_type=RETURN_SCALED)
    total = self.snapshot_discount_price.present? ? self.snapshot_discount_price : self.price(return_type)
    self.line_item_addons.collect do |lt_addon|
      total += lt_addon.snapshot_discount_price.present? ? lt_addon.snapshot_discount_price : lt_addon.snapshot_price
    end
    total
  end

  # Input params - none
  #
  # Returns cost line_item with addons without discount factor
  def price_with_addons(return_type=RETURN_SCALED)
    total = self.price(return_type)
    self.line_item_addons.each do |lt_addon|
      total += lt_addon.snapshot_price
    end
    total
  end

  def has_addon_type_value_group? name
    addon_type_value_group(name).present?
  end

  def addon_type_value_group(name)
    addons = []
    self.line_item_addons.each do |line_item_addon|
      addons << line_item_addon if name.include?(line_item_addon.try{|addon| addon.addon_type_value.addon_type_value_group.name}.to_s)
    end
    addons
  end

  def get_present_addon_types
    at_id = []
    line_item_addons.each do |line_item_addon|
      if line_item_addon.addon_type_value.present? &&
         line_item_addon.addon_type_value.addon_type_value_group.try(:name).try(:downcase) == 'open'
        at_id << line_item_addon.addon_type_value.addon_type.try(:id)
      end
    end
    at_id.compact
  end

  # input params - none
  #
  # Get details about forms required
  #
  # Returns Array
  def required_forms
    forms = { blouse: ['custom blouse stitching', 'regular blouse stitching'],
              suit: ['custom stitching', 'custom tailoring', 'standard stitching sizes',
              'standard stitching size', 'custom_stitching', 'standard_stitching_sizes','standard_stitching','standard stitching']}
    ids = addon_type_value_group(['custom_stitching','standard_stitching']).collect(&:id)
    if ids.count > 0
      forms.each do |key, value|
        if AddonTypeValue.joins(:line_item_addons).where(line_item_addons: {id: ids}).where('LOWER(name) IN (?)', value).count > 0
          forms[key] = true
        else
          forms[key] = false
        end
      end
    end
    forms
  end

  # input params - none
  #
  # Get urls for required forms
  #
  # Returns Hash
  def get_forms
    forms = self.required_forms
    forms_url = {}
    forms_url[:blouse] = true
    forms_url[:suit] = true
    forms.each do |key, value|
      if value == true && key == :suit && self.design.designable_type == "Lehenga"
        forms[:blouse] = forms_url[:blouse].present? ? forms_url[:blouse] : nil
        forms[:suit] = nil
      else
        forms[key] = (value == true && forms_url[key].present?) ? forms_url[key] : nil
      end    
    end
    forms
  end

  # input params - none
  #
  # Scope for getting stitching items
  def self.stitching
    self.joins(line_item_addons: [addon_type_value: :addon_type_value_group]).
      where('addon_type_value_groups.name IN (?) OR stitching_required = ?',
        ['custom_stitching','standard_stitching'], 'Y')
  end


  def qc_type_check(issue = nil)
    quality_issues = QualityCheck.get_quality_issues('quality')
    if issue.present?
      quality_check = quality_issues.find{|qc| qc[0] == issue}
      quality_check_values.to_a.find{|qcv| qcv.quality_check_id == quality_check.try(:last)}      
    else
      quality_check_values.to_a.select{|qcv| quality_issues.map(&:last).include?(qcv.quality_check_id)}
    end
  end

  def return_referral_amount
    price = self.snapshot_price * self.return_quantity
    order = self.designer_order.order
    total_price = order.designer_orders.sum('scaled_total')
    ((price.to_f/total_price.to_f)*order.referral_discount).round(2)
  end

  def get_return_amount(order_total,designer_order_total,discount,order_discount,order, order_total_only_bmgn_products = 0.0)
    total_discount,addon_price = 0,0
    item_price = (self.snapshot_price * (self.return_quantity.to_i > 0 ? self.return_quantity : 1)).to_f
    total_discount += ((item_price / designer_order_total) * discount.to_f) if discount.to_f > 0
    order_discount -= order.additional_discount.to_i if order_total_only_bmgn_products > 0.0 && !buy_get_free
    if order_discount.to_f > 0 && order_total.to_i > 0
      if (buy_get_free && order_total_only_bmgn_products > 0.0)
        total_discount += ((item_price/ order_total_only_bmgn_products.to_f) * order.additional_discount.to_f)
        total_discount += ((item_price/ order_total).to_f * (order.discount.to_i + (order.referral_discount.to_f * order.currency_rate.to_f)))
      else
        total_discount += ((item_price/ order_total.to_f) * order_discount.to_f)
      end
    end
    admin_discount  = self.discount_line_items.to_a.sum(&:price)
    item_price -= (total_discount + admin_discount.to_i).to_i
    if ((['cancel','cancel_complete'].include? order.try(:state)) || (['canceled','vendor_canceled'].include? self.designer_order.state) || self.status == 'cancel') && self.line_item_addons.present?
      item_price += self.line_item_addons.to_a.sum{|addon| addon.snapshot_price * (self.return_quantity.to_i > 0 ? self.return_quantity : 1)}
    end
    tax_amount = add_tax_to_return_item(order, item_price)
    item_price += tax_amount
    return item_price,total_discount,admin_discount, tax_amount
  end

  def add_tax_to_return_item(order, total)
    tax_rate = order.get_applied_tax_rate
    return (total * tax_rate / 100) 
  end

  def reverse_shipment_value
    name  = self.design.categories.first.name.gsub('-', ' ').camelize
    name += ' [sku: ' + self.design.design_code + ']' if self.design.design_code.present?
    if order.cod? && order.currency_rate_market_value.present? && designer_order.ship_to != "mirraw"
      price_per_item = order.international_cod_price(self.price_with_addons)
    else
      price_per_item = self.price_with_addons
    end
    items_price =  price_per_item * self.return_quantity
    { name: name, quantity: self.return_quantity, price: price_per_item, total_price: items_price,gst_rate: 0,hsn_code: '', designable_type: self.design.designable_type }
  end

  def item_stage_check
      if self.stitching_sent_on.present?
        return self.stitching_sent_on
      elsif self.measuremnet_received_on.present?
        return self.measuremnet_received_on
      elsif self.fabric_measured_on.present?
        return self.fabric_measured_on
      elsif self.qc_done_on.present?
        return self.qc_done_on
      elsif self.received_on.present?
        return self.received_on
      end
  end

  def to_amazon_hash
    {
      SKU: design_id,
      MerchantId: PayWithAmazon.merchant_id,
      Title: design.title.to_s,
      Description: design.description.to_s.truncate(80),
      Price: {Amount: snapshot_price,CurrencyCode: 'INR'},
      Quantity: quantity,
      FulfillmentNetwork: 'MERCHANT',
      HandlingTime:{MinDays:2,MaxDays:SHIPPING_TIME + 2},
      ItemCustomData: {LineItemId: id,VendorSellingPrice: vendor_selling_price,VariantId: variant_id,Note: note}
    }
  end

  def assign_warehouse_rack(rack_list_wli = nil)
    rack_list_wli ||= self.get_new_rack_list_item
    if rack_list_wli.present?
      rack_list_wli.decrease_rack_quantity(self.quantity)
      WarehouseLineItemJoin.create_item(rack_list_wli, self)
      self.rack_list_code = rack_list_wli.rack_list.code
      if self.variant_id.blank? && (size_addon = self.line_item_addons.where.not(size_chart_id: nil).first).present? && size_addon.size_chart.size_bucket_id != rack_list_wli.item.size_bucket_id
        size_addon.update_attributes(size_chart_id: SizeChart.where(size_bucket_id: rack_list_wli.item.size_bucket_id).first.id)
      end
      self.save
    else
      return false
    end
  end

  def get_new_rack_list_item
    if self.variant_id.blank? && (size_chart_id = self.line_item_addons.where.not(size_chart_id: nil).first.try(:size_chart_id)).present?
      size_bucket = SizeBucket.joins(:size_charts).where(size_charts: {id: size_chart_id}).first
    end
    if size_bucket.present?
      rack = RackListsWarehouseLineItem.joins(:rack_list,warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and design_id = ? ',self.quantity,design_id).where(warehouse_size_item: {size_bucket_id: size_bucket.id}).order('quantity_present asc').first
      rack = RackListsWarehouseLineItem.joins(:rack_list,warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and design_id = ? ',self.quantity,design_id).where(warehouse_size_item: {size_bucket_id: SizeChart::UNSTITCHED_SIZE_CHART.size_bucket_id}).order('quantity_present asc').first if rack.blank? && size_bucket.id != SizeChart::UNSTITCHED_SIZE_CHART.size_bucket_id
      wli = rack.item.class == WarehouseLineItem ? rack.item : rack.item.warehouse_line_item if rack.present?
      self.designer_order.add_notes_without_callback('SOR Failed.(unavailable at vendor)', 'System') if rack.present? && !wli.available_on_vendor_panel
      return rack if rack.present? && wli.available_on_vendor_panel
    else
      rack = RackListsWarehouseLineItem.joins(:rack_list,:warehouse_line_item).where('quantity_present >= ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL)) ',self.quantity,variant_id,design_id,design_id).order('quantity_present asc').first || RackListsWarehouseLineItem.joins(:rack_list, warehouse_size_item: :warehouse_line_item).where('quantity_present >= ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL)) ',self.quantity,variant_id,design_id,design_id).order('quantity_present asc').first
      wli = rack.item.class == WarehouseLineItem ? rack.item : rack.item.warehouse_line_item if rack.present?
      self.designer_order.add_notes_without_callback('SOR Failed.(unavailable at vendor)', 'System') if rack.present? && !wli.available_on_vendor_panel
      return rack if rack.present? && wli.available_on_vendor_panel
    end
  end
  
  def get_current_rack_list_item
    if (rlwli = self.warehouse_line_item_joins.try(:last).try(:rack_lists_warehouse_line_item)).present?
      return rlwli
    end
    if self.variant_id.blank? && (size_chart_id = self.line_item_addons.where.not(size_chart_id: nil).first.try(:size_chart_id)).present?
      size_bucket = SizeBucket.joins(:size_charts).where(size_charts: {id: size_chart_id}).first
    end
    if size_bucket.present?
      return RackListsWarehouseLineItem.joins(:rack_list,warehouse_size_item: :warehouse_line_item).where('rack_lists.code = ? and design_id = ?',rack_list_code,design_id).where(warehouse_size_item: {size_bucket_id: size_bucket.id}).first
    else
      return RackListsWarehouseLineItem.joins(:rack_list,:warehouse_line_item).where('rack_lists.code = ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL))',rack_list_code,variant_id,design_id,design_id).first ||  RackListsWarehouseLineItem.joins(:rack_list, warehouse_size_item: :warehouse_line_item).where('rack_lists.code = ? and ((variant_id = ? and design_id = ?) OR (design_id = ? and variant_id is NULL))',rack_list_code,variant_id,design_id,design_id).first
    end
  end

  def unassign_warehouse_rack(rack_list_wli =  nil, mark_lost =  false, account = nil)
    rack_list_wli = (rack_list_wli || self.get_current_rack_list_item )
    if rack_list_wli.present?
      # rack_list_wli.warehouse_line_item.decrement!(:quantity_used,self.quantity)
      rack_list_wli.increase_rack_quantity(self.quantity)
      if mark_lost & account
        quantity = rack_list_wli.quantity_present >= self.quantity ? rack_list_wli.quantity_present : self.quantity
        status, message = rack_list_wli.mark_lost(quantity)
        #create WOL
        if status
          rack_list_wli.reload
          rack_list_wli.warehouse_order_logs << WarehouseOrderLog.create(warehouse_order: rack_list_wli.try(:item).try(:warehouse_order) , quantity: quantity, action:'lost', account: account, rack_list: rack_list_wli.rack_list, line_item: self)
        end
        self.designer_order.add_notes_without_callback("#{message} for #{self.id}", 'System')
      end
      # WarehouseLineItemJoin.remove_item(rack_list_wli, self)
      self.rack_list_code =nil
      self.save
    end
  end

  def self.auto_do_fabric_operation_in_bulk(item_ids, current_account_id, account_name)
    item_ids.each{|item_id| mark_fabric_measurement_done_and_confirmed(item_id, current_account_id, account_name)}
  end

  def self.mark_fabric_measurement_done_and_confirmed(item_ids,current_account_id,account_name)
    LineItem.mark_fabric_measurement_done(item_ids,current_account_id,account_name)
    LineItem.mark_fabric_measurement_confirmed(item_ids,current_account_id,account_name)
  end

  def self.mark_fabric_measurement_done(item_ids,current_account_id,account_name)
    response = {error: 'Error adding fabric measurement details'}
    if item_ids.present?
      item = LineItem.where(id: item_ids).update_all(fabric_measured_by: current_account_id, fabric_measured_on: DateTime.now)
      if item.present? and item > 0
        line_item = LineItem.where(id: item_ids).first
        line_item.add_into_scan('Measurement Done', current_account_id)
        response = {fabric_measurement_done_status: 'Fabric Measurement Done: '+ account_name + '(' + DateTime.now.strftime('%a, %e %b') + ')'}
      end
    end
    return response
  end

  def self.mark_fabric_measurement_confirmed(item_ids,current_account_id,account_name)
    response = {error: 'Error adding stitching details'}
    if item_ids.present?
      item = LineItem.where(id: item_ids).update_all(measuremnet_confirmed: 'Y', measuremnet_received_by: current_account_id, measuremnet_received_on: DateTime.now)
      if item.present? and item > 0
        StitchingMeasurement.sidekiq_delay.generate_pdf(item_ids, 'stylist')
        StitchingMeasurement.sidekiq_delay.check_fnp(item_ids)
        line_item = LineItem.where(id: item_ids).first
        if line_item.is_ready_for_stitching && !line_item.is_sbr_product?
          process_date = line_item.get_process_breached_days(process_name: 'Tailor Sent')
          line_item.create_searcher_entity_for_item(search_type: 'StitchingSearcherEntity', process_date: process_date)
        end
        line_item.attempt_auto_tailor_assign if line_item.qc_done_on.present?
        line_item.add_into_scan('Measurement Confirmed', current_account_id) if line_item
        response = {measurement_confirmed_status: 'Stitching measurement confirmed: '+ account_name + '(' + DateTime.now.strftime('%a, %e %b') + ')'}
      end
    end
  end

  def is_sbr_product?
    item_details['fake_rack'] == 'SBR'
  end

  def get_process_breached_days(process_name: nil)
    get_process_lpst_date(process_name).to_date
  end

  def get_process_lpst_date(process_name)
    if (association_name = (process_name.parameterize('_') + '_process_date')) && 
      (process_date = self.respond_to?(association_name.to_sym)) &&
      (process_lpst_date = self.public_send(association_name.to_sym).try(:lpst_date))
      process_lpst_date
    else
      current_process_priority = OperationProcess::PROCESS_IDS[process_name][3]
      (order.confirmed_at + OperationProcess::PROCESS_IDS.collect{|process_name, data| data[1] if (data[3] <= current_process_priority)}.compact.sum.to_i.day)
    end
  end

  def copy_stitching_data(current_account, old_line_item_id)
    old_line_item = LineItem.find_by(id: old_line_item_id)
    self.mark_stitching_required(current_account) if old_line_item.stitching_required == 'Y'
    self.copy_addons_to_new_item(old_line_item)
  end

  def mark_stitching_required(current_account)
    if self.design.designable_type.present?
      self.assign_attributes(stitching_required: 'Y')      
      if self.save
        self.designer_order.add_notes_without_callback("Marked Stitching Required Design: #{self.design_id}", 'stitching', current_account)
        self.add_into_scan('Marked Stitching Required', current_account.id)
        SidekiqDelayGenericJob.set(queue: 'critical').perform_async("#{self.order.class}", self.order.id, "mark_order_as_stitching")
        #self.order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
        SidekiqDelayGenericJob.perform_async("#{self.order.class}", self.order.id, "update_expected_dates")
        #self.order.sidekiq_delay.update_expected_dates
        response = {:stitching_required_status => 'Marked Stitching Required', :qc_done => qc_done}
      end
    end
  end

  def copy_addons_to_new_item(old_line_item)
    if self.design.designable_type == old_line_item.design.designable_type
      old_line_item.line_item_addons.each do |addon|
        new_addon = addon.dup
        new_addon.line_item_id = self.id
        new_addon.save!      
      end
    end
  end

  def self.bulk_add_into_scan(id_type, ids, types, scanned_by, content=nil, scanned_at=nil)
    w_clause = id_type == 'DesignerOrder' ? {designer_order_id: ids} : {id: ids}
    types = types.is_a?(Array) ? types : [types]
    LineItem.where(w_clause).each{|item| item.add_into_scan(types, scanned_by, content, scanned_at)}
  end

  def add_into_scan(types, scanned_by, content=nil, scanned_at=nil)
    all_types = types.is_a?(Array) ? types : [types]
    all_types.each{|type| scans.create(scan_type: type, scanned_by: scanned_by, scanned_at: (scanned_at.presence || Time.current), content: content)}
  end

  def mark_qc(status, qc_done_by_account)
    self.update_columns(qc_done: 'Y', qc_done_by: qc_done_by_account.id, qc_status: status, qc_done_on: Time.current.utc)
    evt_name = qc_status? ? "QC Approved" : "QC Rejected"
    AppEvent::ItemEvent.new(self.id, evt_name).trigger_clevertap_event_deliver_later
    self.add_into_scan((qc_status? ? 'QC Passed' : 'QC Failed'), qc_done_by_account.id)
    self
  end

  def tailoring_material_list
    designable = self.design.designable_type
    DESIGNABLE_TAILORING_MATERIAL[['Saree', 'Lehenga'].include?(designable) ? designable : 'Other']
  end

  def create_issue_for_item(order, current_account, is_resolve, resolve_msg, issue_msg, rtv_qty, replacement)    
    if is_resolve == 'true'
      self.issue_status = 'N'
      self.issue_resolved_by = current_account.id
      self.issue_resolved_at = Time.zone.now
      self.issue_resolve_message = resolve_msg
      self.rtv_quantity = 0
      if (['resolve by replacement', 'resolve by repair'].include?(resolve_msg.to_s.downcase) || self.return.present?) && self.save
        order.add_notes_without_callback("#{self.design_id} Issue Resolved: #{resolve_msg}", 'replacement',current_account)
        order.remove_tags_skip_callback('issue') if order.line_items.all?{|line_item| line_item.issue_status == 'N' || line_item.issue_status.blank?}
        message = 'Issue Removed successfully'
        self.add_into_scan('Issue Resolved', current_account.id)
      else
        message = 'Issue Cannot Be Resolved. Return Not Yet initiated for line item'
      end
    else
      self.issue_status = 'Y'
      self.issue_created_by = current_account.id
      self.issue_created_at = Time.zone.now
      self.issue_message = issue_msg
      self.rtv_quantity = rtv_qty if replacement == 'Yes'
      if self.save
        order.add_tags_skip_callback('issue')
        order.add_notes_without_callback("#{self.design_id} Issue: #{issue_msg}", 'other', current_account)
        self.add_into_scan('Issue Created', current_account.id)
        if self.rtv_quantity.to_i > 0 && designer_order.can_replacement_required?
          designer_order.replacement_required!
          self.add_into_scan('Replacement Needed', current_account.id)
        end
        message = 'Issue Added successfully'
      else
        message = 'Issue cannot be created'
      end
    end    
    return message
  end

  def get_measurement_state(all_mes)
    states = all_mes.collect(&:state).uniq
    measurement_state = if states.include?('rejected')
      'rejected'
    elsif states.include?('phone_call')
      'phone_call'
    elsif states.include?('hold')
      'processing'
    elsif states.size == 1 && states.include?('approved')
      'approved'
    elsif all_mes.size < quantity
      'pending'
    else
      'processing'
    end
  end

  def get_prestitch_saree_size(type: :note)
    if (prestitch_note = get_prestitch_notes).present?
      prestitch_size = {}
      prestitch_note.strip.split(',').each do |list|
        set = list.split(':')
        key = set[0]
        key = key.remove('Select ',' Size').strip
        value = set[1]
        value = value.match(/\d+/).to_s
        prestitch_size[key] = value
      end
      case type
      when :note
        "Pre-Stitched Saree " + prestitch_size.map{|k, v| "#{k}(#{v})"}.join(", ")
      else
        prestitch_size
      end
    end
  end

  def get_item_status
    if (status = check_designer_order_status(self.designer_order)).present?
      [status[0], 'Operation']
    elsif self.stitching_done_on.present?
      ['Stitching Done', 'Warehouse']
    elsif self.issue_status == 'Y'
      ['QC Failed-Issue Unresolved', 'Operation']
    else  
      get_item_current_process_detail(self)
    end  
  end

  def sor_available?
    if self.designer_order_id.present?
      self.available_in_warehouse && self.rack_lists_warehouse_line_items.first.present? && self.rack_lists_warehouse_line_items.first.warehouse_item.warehouse_order.created_at > Date.parse('2020-02-10')
    else
      #This is to check sor availability in cart before order is placed
      (self.variant || self.design).sor_available?
    end
  end

  def check_if_dispatchable
    shipment_id.nil? && received == 'Y' && designer_order.state != 'canceled' && sent_to_invoice.present? && status.blank?
  end

  def is_buyer_returned?
    status == 'buyer_return'
  end

  def create_issue(issue)
    DesignerIssue.create_issue(self, issue)
  end

  def self.bulk_store_item_details(item_ids: [], detail_name: nil, detail_value: nil, action: :add)
    LineItem.where(id: item_ids).each {|item| item.store_item_details(detail_name: detail_name, detail_value: detail_value)}
  end

  def store_item_details(detail_name: nil, detail_value: nil, action: :add, update: true)        
    updated_item_details = update_current_item_details(detail_name, detail_value, item_details, action)
    if update 
      self.update_columns(item_details: updated_item_details) 
    else
      self.assign_attributes(item_details: updated_item_details)
      updated_item_details
    end
  end

  # def vendor_selling_price
  #   if available_in_warehouse && rack_lists_warehouse_line_items.present?
  #     return super if self.designer_order.transaction_rate.to_f.zero? && self.designer_order.designer.is_transfer_model?
  #     rack_lists_warehouse_line_items.first.warehouse_item.snapshot_price
  #   else
  #     super
  #   end
  # end

  def do_warehouse_bucket_check(order_obj)
    if order_obj.get_sane_line_items_count == 1
      bucket_check_for_single_item_order
    else
      bucket_check_for_multiple_items_order
    end
  end

  def quantity_exceeds?(design_id, item_quantity)
    total_quantity = WarehouseLineItem.where(design_id: design_id).sum(:quantity)
    return total_quantity > item_quantity
  end

  def get_addon_names
    @addon_names ||= (line_item_addons.map(&:addon_type_value).flatten.map(&:name))
  end  

  def is_ready_for_stitching
    @item_ready_for_stitching ||= (
      stitching_required == 'Y' && stitching_sent_on.blank? &&
      (
        (
          @measurement_check = (
            stitching_measurements.to_a.size >= quantity &&
            !stitching_measurements.any?{|sm| !sm.approved? }
          )
        ) || 
        (
          design.try(:designable_type) == 'Saree' && 
          (
            get_addon_names & 
            Order::ADDONS_FOR_STITCHING
          ).blank? &&
          (
            get_addon_names & 
            LineItem::SAREE_STITCHING_ADDON_NAMES
          ).present?
        )
      )
    )    
  end

  def assign_to_warehouse_bucket(bucket_code, bucket_type, fake_rack, product_type, order, done_by_id, from_wms = false)
    if (bucket_obj = bucket_type.constantize.where(code: bucket_code).first)
      bucket_relations.create(warehouse_bucket_id: bucket_obj.id, bucket_kind: bucket_type) unless from_wms
      add_into_scan("Assigned To #{bucket_type}", done_by_id)
      if fake_rack == 'true'
        store_item_details(detail_name: 'fake_rack', detail_value: RackList::FAKE_RACKS["fake_rack_#{bucket_type.underscore}"], update: product_type.blank?)
        store_item_details(detail_name: 'product_type', detail_value: product_type) if product_type.present?
        PackageManagementController.new.assign_item_code(designer_order)
      end
      if bucket_type == 'PackagingBucket'
        order.skip_before_filter = true
        order.items_ready_dispatch
      elsif bucket_type == 'StitchingBucket'
        self.attempt_auto_tailor_assign
        self.stitching_searcher_entity.try(:locate)
      end
    end
  end

  def is_tailor_assigned?
    tailoring_info.exists?
  end

  def get_item_rack_code(des_order_rack_code: nil)
    ((fake_rack_code = item_details['fake_rack']).present? ? fake_rack_code : des_order_rack_code)
  end

  def stitching_form_unfilled?
    quantity > stitching_measurements.length
  end

  def stitching_required?
    stitching_required == 'Y'
  end

  def send_stitching_form?
    stitching_required? && stitching_form_unfilled?
  end

  def self.update_rack_status(condition: {}, status: 'rack_in', rack_log: true, rack_id: nil)
    line_items = LineItem.where(condition).where('rack_status is null or rack_status <> ?', status)
    if rack_log
      rack_action = (status == 'rack_in' ? 'putting' : 'picking')
      LineItem.bulk_rack_log(loggable_objects: line_items, kind: rack_action, rack_id: rack_id, preload_array: [:designer_order], rack_fetch_cmd: 'designer_order.rack_list_id')
    end
    line_items.update_all(rack_status: status)
  end

  def create_communication_topic(topic: nil, message: nil, done_by_account: nil)
    topic_object = communication_topics.where(topic: topic).first_or_initialize
    initialize_topic(topic_object, done_by_account) if topic_object.new_record?
    topic_object.add_communication_message(message_content: message, account: done_by_account)
  end

  def create_searcher_entity_for_item(search_type: nil, process_date: nil)
    CreateSearchEntityJob.perform_async(self.class.to_s, self.id, search_type, process_date)
  end

  def send_return_status(less_stages = true)
    count = 0
    timestamp_hash = {}
    if (line_item_return = self.return).present?
      count = 1
      timestamp_hash[:created_at] = line_item_return.created_at 
      if line_item_return.state == 'pending_payment'
        count = less_stages ? 1 : 2
        timestamp_hash[:pending_on] = line_item_return.pending_on
      elsif line_item_return.state == 'payment_complete'
        count = less_stages ? 2 : 3
        timestamp_hash[:pending_on] = line_item_return.pending_on 
        timestamp_hash[:completed_on] = line_item_return.completed_on
      end
    end
    return count, timestamp_hash
  end

  def post_qc_pass(status, qc_done_by_account)
    order = self.order
    if self.issue_status == 'Y'
      self.create_issue_for_item(order, qc_done_by_account, 'true', 'Resolve By Replacement', '', nil, '')
    end
    Order.check_status_line_items(order, 'qc_done') if status == 'true'
    order.check_items
  end

  def attempt_auto_tailor_assign(delay_attempt=true)
    if AUTO_TAILOR_ASSIGN['enable'] && !is_tailor_assigned?
      # delay_attempt ? self.sidekiq_delay.auto_assign_to_tailor : self.auto_assign_to_tailor
      delay_attempt ? SidekiqDelayGenericJob.perform_async("#{self.class}", self.id, "auto_assign_to_tailor") : self.auto_assign_to_tailor
    end
  end

  def auto_assign_to_tailor
    product_stitching_type = get_product_stitching_type_name
    design_type = design.get_design_type
    assignable_tailor_stats = Tailor.get_auto_assignable_tailor(product_type: design_type, stitching_type: product_stitching_type)
    create_tailoring_info_entry(tailor_stat: assignable_tailor_stats) if assignable_tailor_stats
  end

  def create_tailoring_info_entry(tailor_stat: nil)
    stitchable_quantity = design.stitchable_quantity
    t_info_object = tailoring_info.new(
      order_id: designer_order.order_id, 
      tailor_info_done_by: 'system', 
      tailor_name: tailor_stat.tailor.name, 
      line_item_quantity: (quantity * stitchable_quantity), 
      tailoring_material: (stitchable_quantity > 1 ? "Combo #{tailor_stat.name}" : tailor_stat.name), 
      assigned_to_tailor_at: Time.current,      
      tailor_id: tailor_stat.tailor_id,
      tailor_daily_metric_id: tailor_stat.id
    )
    if t_info_object.save
      t_info_object.tailoring_bag_relations.create
      self.update_columns(stitching_sent: 'Y', stitching_sent_on: Time.current)
      add_into_scan('Assign To Tailor', nil, 'System Auto Tailor Assign')
      tailor_stat.update_columns(
        pending_count: (tailor_stat.pending_count + t_info_object.line_item_quantity)
      )
      SidekiqDelayGenericJob.perform_async("#{t_info_object.class}", t_info_object.id, "post_tailor_assign_callbacks")
      #t_info_object.sidekiq_delay.post_tailor_assign_callbacks
      LineItem.mark_fabric_measurement_done(id, nil, 'System') if fabric_measured_on.blank?
      LineItem.mark_fabric_measurement_confirmed(id, nil, 'System') if measuremnet_received_on.blank?
      LineItem.update_rack_status(condition: {id: id}, status: 'rack_out')
    end
  end

  def get_product_stitching_type_name(default_name: 'Standard')
    get_blouse_stitching_type(default_return_value: default_name).gsub('Section', '').strip
  end

  def self.update_scanned_input_param(input_params)
    start_with_input = input_params.select{|key, value| key.start_with?('input_') && value.start_with?('Unpack') }
    input_params_key = start_with_input.keys
    input_params_dup = start_with_input.values
    input_params_dup = input_params_dup.map {|i| i.split('-') }
    line_item_ids = input_params_dup.group_by{|i| i[1].to_i }
    line_items = LineItem.where(id: line_item_ids.keys).preload(designer_order: [:order, :rack_list])
    line_items_code_hash = line_items.map{|i| [i.id, i.get_rack_barcode] }.to_h
    line_item_ids.map do |item_id, code|
      code.map do |i|
        temp = line_items_code_hash[item_id.to_i].to_s
        temp += '-' + i[2..-1].try(:join, '-').to_s if i[2..-1].present?
        temp
      end
    end.flatten.each_with_index do |code, i|
      input_params[input_params_key[i]] = code
    end
  end


  def get_vendor_payout_for_item
    item_total = 0
    sor_item_total = 0
    if self.sor_available?
      sor_item_total += self.vendor_selling_price * self.quantity
    else
      item_total += self.vendor_selling_price.to_f * self.quantity
    end
    item_total += self.line_item_addons.select { |li| li.snapshot_payable_to == 'designer' }.sum { |addon| addon.sub_total }
  
    payout = if (item_total + sor_item_total) > 0
      if (v_discount = self.designer_order.discount).present?
        if item_total > 0
          item_total = (item_total - v_discount) * (100 - self.designer_order.transaction_rate) / 100
        else
          sor_item_total = (sor_item_total - v_discount) * (100 - self.designer_order.transaction_rate) / 100
        end
      else
        if item_total > 0
          item_total = item_total * (100 - self.designer_order.transaction_rate) / 100
        else
          sor_item_total = sor_item_total * (100 - self.designer_order.transaction_rate) / 100
        end
      end
      item_total + sor_item_total
    elsif design.designer.is_transfer_model?
      item_total + sor_item_total
    else
      discount_amt = (100 - self.designer_order.promotion_discount)
      self.designer_order.total * (100 - self.designer_order.transaction_rate) / discount_amt
    end
  
    return payout
      
  end

  def get_rack_barcode
    order = designer_order.order
    item_rack_code = get_item_rack_code(des_order_rack_code: designer_order.rack_list.try(:code))
    "#{item_rack_code}-#{order.number}-#{designer_order.rack_code}-#{design_id}"
  end

  def self.sb_line_items_has_jewellery(shipment_bucket_id)
      sb_obj = ShipmentBucket.find(shipment_bucket_id)
      return line_items_has_jewellery(sb_obj)
  end

  def self.line_items_has_jewellery(object)
    if (li = object.line_items).present?
      return li.any?{|li| li.designable_type == 'Jewellery' && li.status.blank?}
    end
    return false
  end

  include ScopeScoreEvent

  private

  def get_stitching_section_for_assigning
    @measurement_check ? get_blouse_stitching_type : 'FNP Section'
  end

  def get_blouse_stitching_type(default_return_value: 'Custom Section')
    if (get_addon_names & ['Custom Blouse Stitching', 'Custom Stitching']).present?
      'Custom Section'
    elsif (get_addon_names & ['Regular Blouse Stitching', 'Standard Stitching']).present?
      'Standard Section'
    else
      default_return_value
    end
  end

  def bucket_check_for_single_item_order
    if is_ready_for_stitching
      get_warehouse_bucket_response(StitchingBucket, product_type: get_stitching_section_for_assigning) 
    elsif stitching_required == 'Y'     #### data for creating rack bucket relation with single warehouse rack bucket. all bucket realtions will be associated with single rack bucket -> whose code is 'rack_0'.
      {error: false, bucket_codes: LineItem::FIXED_RACK_SCAN_CODES, message: 'Scan Rack Bucket', 
        color_code: '#5bc0de' , fake_rack: false, bucket_type: 'RackBucket', product_type: nil}
    else
      get_warehouse_bucket_response(PackagingBucket) 
    end
  end

  def bucket_check_for_multiple_items_order
    if is_ready_for_stitching
      get_warehouse_bucket_response(StitchingBucket, product_type: get_stitching_section_for_assigning)
    else        #### data for creating rack bucket relation with single warehouse rack bucket. all bucket realtions will be associated with single rack bucket -> whose code is 'rack_0'
      {error: false, bucket_codes: LineItem::FIXED_RACK_SCAN_CODES, message: 'Scan Rack Bucket',
        color_code: '#5bc0de' , fake_rack: false, bucket_type: 'RackBucket', product_type: nil}
    end
  end

  def get_warehouse_bucket_response(bucket_type, product_type: nil)
    {error: false, bucket_codes: bucket_type.all.map(&:code), message: "Scan #{bucket_type.to_s.titleize}", 
      color_code: bucket_type.last.try(:color_hex), fake_rack: true, bucket_type: bucket_type.to_s, product_type: product_type}
  end

  def initialize_topic(object, account)
    object.update_attributes(owned_by_id: account.try(:id), owned_by_name: (account.try(:name) || 'System'), owned_by_department: account.try(:role).try(:name))
  end

  def check_designer_order_status(des_ord)
    ['pending', 'critical', 'replacement_pending', 'vendor_canceled'] & [des_ord.state]
  end

  def update_current_item_details(data_name, data_value, current_details, action)
    if action == :delete
      current_details.delete(data_name.to_s)
    else
      current_details.store(data_name.to_s, data_value.to_s)
    end
    current_details
  end

  def get_item_current_process_detail(item)
    item_process_dates = {
      'Issue Created_Operation' => (item.issue_status == 'Y' ? item.issue_created_at : nil), 
      'Tailor RTV_Operation' => item.ipending_related_scans.select{|sc| sc.scan_type.downcase == 'handover to warehouse for rtv'}.map(&:scanned_at).max, 
      'Processing_Stitching' => item.ipending_related_scans.select{|sc| sc.scan_type.downcase == 'handover to stylist for working'}.map(&:scanned_at).max, 
      'Rejected_Stitching' => item.stitching_measurements.map(&:rejected_at).compact.max, 
      'Approved_Stitching' => item.stitching_measurements.map(&:approved_at).compact.max, 
      'Phone Call_Stitching' => item.stitching_measurements.map(&:phone_call_at).compact.max, 
      'Tailor Assign_Stitching' => item.tailoring_info.map(&:created_at).max, 
      'Tailor Inscan_Stitching' => item.tailoring_info.map(&:tailoring_inscan_bags).flatten.compact.map(&:created_at).max, 
      'Alteration_Stitching' => item.tailoring_info.map(&:alteration_added_at).compact.max, 
      'Reassign_Stitching' => item.tailoring_info.map(&:reassign_material_timestamp).compact.max, 
      'Tailor Received_Stitching' => item.tailoring_info.to_a.map{|t| t.material_received_status ? t.material_received_status_timestamp : nil}.compact.max, 
      'Handover To Stitching_Stitching' => item.ipending_related_scans.select{|sc| sc.scan_type.downcase == 'handover to stitching'}.map(&:scanned_at).max, 
      'Handover To Warehouse_Warehouse' => item.ipending_related_scans.select{|sc| sc.scan_type.downcase == 'handover to warehouse'}.map(&:scanned_at).max
    }
    if (last_date = item_process_dates.values.compact.max)
      return item_process_dates.key(last_date).try(:split, '_') 
    else
      [(item.stitching_required == 'Y' ? 'Rack Assigned' : 'Non Stitching Process'), 'Warehouse']
    end
  end

  def get_prestitch_notes
    line_item_addons.to_a.find{|lia| lia.addon_type_value.try(:name).try(:downcase) == 'pre-stitched saree'}.try(:notes)
  end

  def delete_item_count_for_design
    Rails.cache.delete("line_item_count_for_design_#{self.design.id}")
  end

  def track_events
    track_promotions = PromotionTracking.get_trackable_promotions(Design.country_code)
    if pair_product && (track_event_id = track_promotions['complete_the_look']).present?
      item_promotion_trackings.create(promotion_tracking_id: track_event_id)
    end
  end
end
