class Reqlist < ActiveRecord::Base
  
  after_create :notify_designer

  validates :email,
            presence: { message: "Request List: Please specify an Email Address" },
            format: { with: /\A((?:[a-z]+[0-9_\.-]*)+[a-z0-9_\.-]*)@((?:[a-z0-9]+[\.-]*)+\.[a-z]{2,4})\z/i,
            message: "Request List: Please correct your Email Address" }, :on => :create

  scope :get_pending_reqlist, -> {
    where("(notified_at IS NOT NULL AND notified_at < ? AND notification_count < 2)
          OR (created_at > ? AND notified IS NULL)", 1.day.ago, 1.month.ago)
  }

  scope :valid_reqlist_emails, -> {
    where("email IS NOT NULL AND email LIKE '%@%'")
  }

  def notify_designer
    email = Design.find(self.design).designer.email
    FollowMailer.sidekiq_delay.sendout_request_product_notification(email, self.design)
  end
  
end
