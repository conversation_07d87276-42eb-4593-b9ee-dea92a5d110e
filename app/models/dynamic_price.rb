class DynamicPrice < ActiveRecord::Base
  #attr_accessible :country_code, :country_id, :design_id, :scale

  belongs_to :design
  belongs_to :country

  before_save :add_country_code

  validates :country_id, presence: true
  validates :design_id, presence: true
  validates :scale, numericality: { greater_than_or_equal_to: 0.45 }

  def add_country_code
    self.country_code=country.iso3166_alpha2  	if !self.country_code.present?
  end

  def self.update_designs(filename,remove_option = nil)
    @remove_option = remove_option.nil? ? {dynamic_pricing: true,stop_indexing: true} : JSON.parse(remove_option).symbolize_keys
    full_path = 'https://s3-'+ ENV['AWS_REGION'] +'.amazonaws.com/' + ENV['S3_BUCKET'] + '/' + filename
    scale=1.0 if (@remove_option[:dynamic_pricing] == false && @remove_option['buy_get_free'] == nil)
    CSV(open(full_path), headers:true, :header_converters => :symbol) do |csv_file|
      csv_file.each_slice(1000) do |raws|
        dynamic_price_entries = load_dynamic_price_entries(raws,scale)
        if (DynamicPrice.check_file(dynamic_price_entries))
          DynamicPrice.update_and_insert(dynamic_price_entries)
        end
      end
    end
  end

  def self.check_for_present_designs(data)
    present_designs =[]
    present_designs = data[1].map { |h| h[:design_id] }
    present_designs = Design.where(id: present_designs).pluck(:id)
    data[1].delete_if { |h| present_designs.exclude?(h[:design_id])}
    return present_designs, data
  end

  def self.get_array_to_update_and_insert(dynamic_price_hash,get_design_id=nil)
    dynamic_price_array = []
    if get_design_id.present?
      dynamic_price_hash.each do |x|
        dynamic_price_array << x[:design_id]
      end
    else
      dynamic_price_hash.each do |x|
        dynamic_price_array << x.values
      end
    end
    dynamic_price_array
  end

  def self.check_file(dynamic_price_entries)
    actual_array = dynamic_price_entries.flat_map(&:values)
    array_without_nil = actual_array.compact
    (actual_array == array_without_nil) && array_without_nil.present? && (array_without_nil & [nil,0,0.0,'',' ']).empty?
  end

  def self.update_and_insert(dynamic_price_entries)
    remaining_designs = []
    designs_need_to_be_updated = []
    international_codes = SystemConstant.get('DYNAMIC_PRICE_UPLOAD_COUNTRIES').split(',')
    min_rate = CurrencyConvert.get_rate_for_dynamic_pricing
    country_id_hash = Country.get_all_country_id
    # country_id_hash.delete('IN') # this could be the reason for not having scaling for india
    dynamic_price_entries.group_by{|hash_sorted_by_country_code| hash_sorted_by_country_code[:country_code]}.each do |hash_sorted_by_country_code|
      design_ids = hash_sorted_by_country_code[1].map { |entry| entry[:design_id] }
      country_code = hash_sorted_by_country_code[0]
      unless international_codes.include?(country_code)
        if @remove_option[:buy_get_free].present?
          buy_get_free_value = country_code == 'IN' ? 1 : 2  #when domestic sheet uploaded will check designs uploaded are present for international b1g1 criteria previously and vice-versa  
          designs_to_update = Design.where(id: design_ids, buy_get_free: buy_get_free_value).pluck(:id) if buy_get_free_value.present?
          Design.where(id: designs_to_update).update_all(buy_get_free: 3) if designs_to_update.present? #update designs b1g1 for both 
        end
        data1, data2 = DynamicPrice.upload_and_update_dynamic_price(hash_sorted_by_country_code,min_rate,country_id_hash)
      else
        data1, data2 = DynamicPrice.upload_all_country_entries(hash_sorted_by_country_code,min_rate,country_id_hash)
      end
      remaining_designs.push(* data1)
      designs_need_to_be_updated.push(* data2)
    end
    DynamicPrice.insert_and_turn_flag_on(remaining_designs,designs_need_to_be_updated)
  end

  def self.upload_all_country_entries(hash_sorted_by_country_code,min_rate,country_id_hash)
    int_countries = country_id_hash
    if hash_sorted_by_country_code[0] == 'INT'
      int_countries = int_countries.to_a
    elsif (int_country_code = SystemConstant.get(hash_sorted_by_country_code[0])).present?
      int_country_code = int_country_code.split(',')
      int_countries = int_countries.collect{|k,v| [k,v] unless(int_country_code.exclude?k)}.compact
      return [],[] if int_countries.length != int_country_code.length
    end
    if int_countries.class == Array
      hash_sorted_by_country_code[1].uniq! {|h| h[:design_id]}
      int_design_scale = hash_sorted_by_country_code[1].collect{|d| [d[:design_id], d[:scale]]}
      data1,data2 = DynamicPrice.make_upload_hash(int_countries,int_design_scale,min_rate,country_id_hash)
      return data1,data2
    else
      return [],[]
    end
  end

  def self.insert_and_turn_flag_on(remaining_designs,designs_need_to_be_updated)
    fields_to_insert = %w{country_id design_id scale country_code}
    DynamicPrice.import fields_to_insert, remaining_designs, :validate => false
    flagged_design_ids = Design.where(id: designs_need_to_be_updated).where(buy_get_free: 3).pluck(:id) if @remove_option[:buy_get_free].present?
    if flagged_design_ids.present?
      Design.where(id:flagged_design_ids).update_all(@remove_option.slice(:dynamic_pricing))
      unprocessed_design_ids = designs_need_to_be_updated - flagged_design_ids
    else 
      unprocessed_design_ids = designs_need_to_be_updated
    end
    Design.where(id: unprocessed_design_ids).update_all(@remove_option.slice(:dynamic_pricing, :buy_get_free))
    Sunspot.index Design.where(id: designs_need_to_be_updated) if !(@remove_option[:stop_indexing])
  end

  def self.make_upload_hash(int_country_id_code,int_design_scale,min_rate,country_id_hash)
    remaining_designs = []
    designs_need_to_be_updated = []
    upload_hash = []
    int_country_id_code.each do |country|
      int_design_scale.each do |design_scale|
        upload_hash << {country_id: country[1],design_id: design_scale[0],scale: design_scale[1],country_code: country[0]}
      end
    end
    upload_hash.group_by{|sort_by_country_code| sort_by_country_code[:country_code]}.each do |sort_by_country_code|
      data1, data2 = DynamicPrice.upload_and_update_dynamic_price(sort_by_country_code,min_rate,country_id_hash)
      remaining_designs.push(* data1)
      designs_need_to_be_updated.push(* data2)
    end
    return remaining_designs, designs_need_to_be_updated
  end

  def self.upload_and_update_dynamic_price(hash_sorted_by_country_code,min_rate,country_id_hash)
    remaining_designs = []
    designs_need_to_be_updated = []
    hash_sorted_by_country_code[1].delete_if { |h| (h[:country_id] != country_id_hash[h[:country_code]]) || ((h[:scale] < ((min_rate[hash_sorted_by_country_code[0]] if h[:country_code] != 'IN' ) || MINIMUM_SCALING_FACTOR).to_f.round(2) + INTERNATIONAL_MARGIN))}
    hash_sorted_by_country_code[1].uniq! {|h| h[:design_id]}
    if hash_sorted_by_country_code[1].present?
      designs_present,design_to_insert = DynamicPrice.check_for_present_designs(hash_sorted_by_country_code) # designs for mass updation and design_to_insert for mass insertion
      if designs_present.present?
        designs_need_to_be_updated += designs_present
        design_to_insert[1].group_by{|hash_sorted_by_scale_and_country_code| hash_sorted_by_scale_and_country_code[:scale]}.each do |hash_sorted_by_scale_and_country_code|
          design_to_update = DynamicPrice.get_array_to_update_and_insert(hash_sorted_by_scale_and_country_code[1],true)
          dynamic_price = DynamicPrice.where(design_id: design_to_update,country_code: design_to_insert[0])
          designs_in_dynamic_price = dynamic_price.pluck(:design_id)
          dynamic_price.update_all(scale: hash_sorted_by_scale_and_country_code[0])
          hash_sorted_by_scale_and_country_code[1].delete_if { |h| designs_in_dynamic_price.include?(h[:design_id])}
          remaining_designs += DynamicPrice.get_array_to_update_and_insert(hash_sorted_by_scale_and_country_code[1])
        end
      end
    end
    return remaining_designs, designs_need_to_be_updated
  end

  def self.load_dynamic_price_entries(parsed_file,scale)
    parsed_file.collect do |line|
      {country_id: line[:country_id].to_i, design_id: line[:design_id].to_i, scale: scale || line[:scale].to_f, country_code: line[:country_code].to_s.upcase.gsub(/\n/,'')}
    end
  end
end
