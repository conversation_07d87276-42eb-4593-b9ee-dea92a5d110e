class DesignerBoostConfig < ActiveRecord::Base
  belongs_to :designer

  validates :designer_id, presence: true, uniqueness: true
  validates :boost_fee, numericality: { greater_than_or_equal_to: 0 }
  validates :duration, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validate :duration_must_be_multiple_of_12

  def name
    "Designer ##{designer_id}"
  end

  private

  def duration_must_be_multiple_of_12
    errors.add(:duration, "must be a multiple of 12") if duration.present? && duration % 12 != 0
  end
end
