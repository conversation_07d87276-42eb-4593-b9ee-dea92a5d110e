class DelayedFile < ActiveRecord::Base
  belongs_to :account
  attr_accessor :content_type, :extension
  has_attached_file :file,
  s3_headers: lambda { |attachment|
    {
      'Content-Type' => "#{attachment.content_type}",
      'Content-Disposition' => "attachment; filename=#{attachment.name}.#{attachment.extension}",
    }
  },
  url: ":s3_domain_url"
  do_not_validate_attachment_file_type :file
  validates :account_id, uniqueness: { scope: :name }
  validates_presence_of :account_id, :name
  state_machine :state, :initial => :new do
    event :process do
      transition [:new, :failed, :complete] => :processing
    end
    event :complete do
      transition processing: :complete
    end
    event :failed do
      transition processing: :failed
    end
  end

  def self.create_delayed_file(params)
    account = Account.find(params.with_indifferent_access["account"]["Account"])
    current_delay_file = account.delayed_files.where(name: params[:name])
    if current_delay_file.present?
      current_delay_file.destroy_all
    end
    delayed_file = account.delayed_files.where(name: params[:name].strip).first_or_create
    if delayed_file.can_process?
      delayed_file.process!
      SidekiqDelayGenericJob.perform_async(delayed_file.class.to_s, delayed_file.id ,"delay_download", params.merge(sidekiq_request_params: true, has_object?: true))
      #delayed_file.sidekiq_delay.delay_download(params)
    end
    delayed_file
  end

  def delay_download(params)
    delay_object = params[:object] || params[:class].constantize
    delay_method = params[:method].to_sym
    args = params[:params]
    complete if add_file(delay_object.send(delay_method, *args), params[:file_type])
  rescue => e
    self.error = e.message
    failed
    ExceptionNotify.sidekiq_delay.notify_exceptions(e ,e.message,{ delayed_file_id: self.id })
  end

  private

    def add_file(file_obj, ext)
      file_obj = StringIO.new(file_obj)
      @content_type      = "application/#{ext}"
      @extension         = ext
      self.file = file_obj
      self.save!
    end
end
