class ClickpostReturnToVendor

  def initialize(designer_order=nil,weight=nil,rtv_items=nil,account_id=nil,ref_no=nil)
    @designer_order = designer_order
    @weight = weight
    @rtv_items = rtv_items
    @account_id = account_id
    @ref_no = ref_no
    @click_post_obj = ClickPostAutomation.new
  end
  def fetch_recommended_shipper(designer_order,warehouse_order)
    rvp_available, times_retried = {}, 0
    order = designer_order.order if designer_order.present?
    pickup_pincode = order.present? ? order.get_warehouse_shipping_address[5] : warehouse_order.get_warehouse_address[5]
    drop_pincode = designer_order.present? ? designer_order.designer.pincode : warehouse_order.designer.pincode
    reference_number = designer_order.present? ? designer_order.id : warehouse_order.id
    invoice_value = designer_order.present? ? designer_order.total : warehouse_order.total
    order_type = 'PREPAID'
    payload = [{
        pickup_pincode: pickup_pincode,
        drop_pincode: drop_pincode,
        delivery_type: 'FORWARD',
        order_type: order_type,
        invoice_value: invoice_value,
        reference_number: reference_number
      }]
    begin
      response = @click_post_obj.get_response_for(:recommendation_path, payload).parsed_response.try(:deep_symbolize_keys)
      rvp_id = designer_order.present? ? designer_order.id : warehouse_order.id
      if response[:meta][:status] == 200 && (result = response[:result].try(:first)).present? && result[:preference_array].present?
        rvp_available[rvp_id] = result[:preference_array].sort_by{|i| i[:priority]}.map{|x| x[:cp_id]}.uniq
      end
    rescue Net::ReadTimeout, Net::OpenTimeout, Timeout::Error => e
      if !reverse && response.blank? && times_retried < 3
        times_retried += 1
        retry
      end
    end
    rvp_available
  end

  def get_rtv_items
    total_price = {}
    rtv_items      = []
    @rtv_items.each do |item_id|
      item = LineItem.find_by_id(item_id.to_i)
      quantity = item.quantity
      addon_price = item.vendor_addon_items.to_a.sum{|addon| addon.snapshot_price(RETURN_NORMAL)}
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      name         = item.design.categories.first.name.gsub('-', ' ').camelize
      name         += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name         += ' [size: ' + item.variant.option_type_values.collect(&:name).last + ']' if (variant = item.variant).present? && variant.option_type_values.present?
      name         += addon_text
      name         += '[DNS]' if item.available_in_warehouse
      item_price   =  item.snapshot_price(RETURN_NORMAL) + addon_price
      item_price   = (item_price * (item.designer_order.get_payout_ratio)).round(2)
      items_price  = item_price * quantity
      sku = (item.variant_id.present? ? item.variant_id : item.design.design_code.present? ? item.design.design_code : item.design_id).to_s
      hsn_code,gst_rate = item.find_hscode_gst(item_price)
      total_price[item.designer_order_id] = item.designer_order.total.to_i
      taxable_value = (item_price/(1+gst_rate.to_f/100)).round(2)
      gst_tax = (item_price - taxable_value).round(2)
      rtv_items << { :name => name, :quantity => quantity, :price => item_price, :total_price => items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: item.design.designable_type, dos_id: item.designer_order_id,sku: sku,taxable_value: taxable_value,gst_tax: gst_tax}
    end
    return rtv_items
  end
  
  def sanitize_address(address)
    address.gsub(/\r/,' ').gsub(/\n/,' ').gsub(',',' ')
  end
  
  def pickup_time_formatted
    Time.now.hour < 13 ? (DateTime.now + 4.hours).strftime('%Y-%m-%dT%H:%M:00Z') : Date.tomorrow.in_time_zone.change(hour: 11).strftime('%Y-%m-%dT%H:%M:00Z')
  end
  
  def calculate_invoice_value(rtv_items)
    rtv_items.sum { |item| item[:price] }
  end

  def get_pickup_info(order,warehouse_order=nil)
    _, pickup_phone, pickup_address, _, pickup_city, pickup_pincode, pickup_state, _ = order.present? ? order.get_warehouse_shipping_address : warehouse_order.get_warehouse_address
    {
      pickup_state: pickup_state,
      pickup_address: sanitize_address(pickup_address),
      email: '<EMAIL>',
      pickup_time: pickup_time_formatted,
      pickup_pincode: pickup_pincode,
      pickup_city: pickup_city,
      pickup_name: 'Anup Nair',
      pickup_country: 'IN',
      pickup_phone: pickup_phone
    }
  end
  
  def get_drop_info(designer)
    drop_info = designer.get_designer_address
  
    {
      drop_address: sanitize_address(drop_info[:address]),
      drop_phone: drop_info[:phone],
      drop_country: drop_info[:country_code],
      drop_state: drop_info[:state],
      drop_pincode: drop_info[:pincode],
      drop_city: drop_info[:city],
      drop_name: drop_info[:name]
    }
  end

  def get_warehouse_items(warehouse_order)
    rtv_items = []
  
    warehouse_order.warehouse_line_items.each do |wl_item|
      design = Design.find(wl_item.design_id)
      variant = Variant.find(wl_item.variant_id) if wl_item.variant_id.present?
  
      name = design.categories.first.name.gsub('-', ' ').camelize
      name += ' [sku: ' + design.design_code + ']' if design.design_code.present?
      name += ' [size: ' + variant.option_type_values.collect(&:name).last + ']' if variant.present?
  
      quantity_hash = @rtv_items.detect { |item| item.keys.first.to_i == wl_item.id }
      quantity = quantity_hash ? quantity_hash.values.first.to_i : 0
  
      item_price = wl_item.snapshot_price.to_i * quantity
  
      rtv_items << {
        name: name,
        quantity: quantity,
        price: item_price,
        designable_type: design.designable_type,
        sku: design.design_code
      }
    end
  
    return rtv_items
  end  
  
  def get_shipment_details(shipper,order,designer_order,warehouse_order)
    rtv_items = @designer_order.present? ? get_rtv_items : get_warehouse_items(warehouse_order)
    all_items = []
    rtv_items.each do |item|
      all_items.push({sku: item[:sku], price: item[:price].round(2),quantity: item[:quantity], description: item[:name]})
    end
  
    {
      height: 12,
      order_type: "PREPAID",
      invoice_value: calculate_invoice_value(rtv_items),
      invoice_number: @designer_order.present? ? order.get_invoice_number : warehouse_order.designer.get_invoice_number,
      invoice_date: Date.today.strftime('%Y-%m-%d'),
      reference_number: @ref_no,
      length: 10,
      breadth: 10,
      weight: @weight.to_f.round,
      items: all_items,
      cod_value: 0,
      courier_partner: shipper.clickpost_shipper_id
    }
  end
  
  def get_gst_info(order)
    total_taxable_value = get_rtv_items.sum { |item| item[:taxable_value] }
    gst_total_tax = get_rtv_items.sum { |item| item[:gst_tax] }
  
    {
      seller_gstin: MIRRAW_GST_NUMBER,
      taxable_value: total_taxable_value,
      is_seller_registered_under_gst: true,
      place_of_supply: order.get_warehouse_shipping_address[6],
      enterprise_gstin: MIRRAW_GST_NUMBER,
      gst_total_tax: gst_total_tax,
      invoice_reference: order.get_invoice_number
    }
  end
  def get_return_info(order,warehouse_order)
    _, pickup_phone, pickup_address, _, pickup_city, pickup_pincode, pickup_state, _ = @designer_order.present? ? order.get_warehouse_shipping_address : warehouse_order.get_warehouse_address
    {
      pincode: pickup_pincode,
      address: pickup_address,
      state: pickup_state,
      phone: pickup_phone,
      name: 'Anup Nair',
      city: pickup_state,
      country: 'IN'
    }
  end

  def get_account_code_for_shipper_rtv(shipper_name, designer_order)

    case shipper_name.to_s.downcase
    when 'xpress bees'
      account_code = 'Xpressbees Domestic'
    when 'rapid delivery'
      account_code = 'Rapid Domestic'
    when 'delhivery'
      account_type = DISABLE_ADMIN_FUCTIONALITY['delivery_account_selection'] ? 'surface' : get_delhivery_account_type(designer_order)
      account_code = DELHIVERY_CP_ACC_NAME[account_type]
    when 'smartr'
      account_code = 'Smartr B2B'
    else
      account_code = nil
    end
  
    return account_code
  end  
  

  def get_additional_info(shipper, order,warehouse_order)
    additional_info = {
      label: true,
      return_info: get_return_info(order,warehouse_order),
      delivery_type: "FORWARD",
      async: false,
      gst_number: MIRRAW_GST_NUMBER
    }
  
    account_code_name = get_account_code_for_shipper_rtv(shipper.name, order)
    additional_info.merge!(account_code: account_code_name) if account_code_name.present?
  
    additional_info
  end

  def get_rtv_payload(shipper,warehouse_order=nil)
    if @designer_order.present?
      designer_order = DesignerOrder.find @designer_order if @designer_order.present?
      order = designer_order.order if designer_order.present?
    end
    designer = @designer_order.present? ? designer_order.designer : warehouse_order.designer
    pickup_info = get_pickup_info(order,warehouse_order)
    drop_info = get_drop_info(designer)
    shipment_details = get_shipment_details(shipper,order,designer_order,warehouse_order)
    gst_info = @designer_order.present? ? get_gst_info(order) : nil
    additional_info = get_additional_info(shipper, order, warehouse_order)
  
    payload = {
      pickup_info: pickup_info,
      drop_info: drop_info,
      shipment_details: shipment_details,
      additional: additional_info
    }
    payload[:gst_info] = gst_info if @designer_order.present?
  
    payload
  end

  def get_invoice_url(designer_order,rtv_shipment)
    invoice_url = Rails.application.routes.url_helpers.designer_designer_order_path(
      designer_id: designer_order.designer.id,
      id: designer_order.id,
      rtv: rtv_shipment.number,
      format: "pdf"
    )
  end

  def create_clickpost_rtv_shipment(shipper)
    designer_order = DesignerOrder.find @designer_order
    order = designer_order.order
    payload= get_rtv_payload(shipper)
    begin
      response = @click_post_obj.get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
      if response[:meta][:status] ==200
        rtv_shipment = RtvShipment.create(number: response[:result][:waybill], shipper_name: shipper.name,done_by: 'current_account.id', weight: @weight.to_f, shipment_type: 'rtv',label_url: response[:result][:label],done_by: @account_id)
        RtvShipmentLineItem.create(@rtv_items.map { |item| { line_item_id: item.to_i, rtv_shipment_id: rtv_shipment.id } })
        rtv_shipment.invoice_url = MIRRAW_DOMAIN+get_invoice_url(designer_order,rtv_shipment)
        rtv_shipment.save
      elsif response[:meta][:status] ==323
        rtv_shipment =  RtvShipment.where(number: response[:result][:waybill]).first
        order.add_notes_without_callback "Clickpost RTV Shipment Error - #{response[:meta][:message]}", 'shipment'
      else
        order.add_notes_without_callback "Clickpost RTV Shipment Error - #{response[:meta][:message]}", 'shipment'
      end
    rescue => e
      ExceptionNotifier.notify_exception(
        Exception.new("Clickpost RTV Shipment error-#{designer_order.id}"),
        data: {payload: payload}
      )
    end
  end

  def create_clickpost_rtv_shipment_for_sor(shipper,warehouse_order,rtv_shipment)
    designer = warehouse_order.designer
    w_line_items = warehouse_order.warehouse_line_items
    if designer.present?
      begin
        payload= get_rtv_payload(shipper,warehouse_order)
        response = @click_post_obj.get_response_for(:create_shipment_path, payload).parsed_response.try(:deep_symbolize_keys)
        if response[:meta][:status] ==200
          rtv_shipment.update_attributes(number: response[:result][:waybill],shipper_name: "#{shipper.name}",done_by: @account_id,label_url: response[:result][:label],weight: @weight.to_f, shipment_success: true)
          w_line_items.update_all(rtv_shipment_error: nil)
          update_rtv_quantity(warehouse_order)
        else
          rtv_shipment.destroy
          w_line_items.update_all(rtv_shipment_error: "#{response[:meta][:message]}")
        end
      rescue => e
        rtv_shipment.destroy
        w_line_items.update_all(rtv_shipment_error: e.message)
      end
    else
      w_line_items.update_all(rtv_shipment_error: 'Designer does not exist.')
      rtv_shipment.destroy
    end
  end

  def update_rtv_quantity(warehouse_order)
    items = warehouse_order.warehouse_line_items.where(id: @rtv_items.first.keys)
    items.each do |wli|
      quantity = @rtv_items.first[wli.id.to_s]
      wli.increment!(:rtv_quantity, quantity)
      if (w_size_items = wli.warehouse_size_items).present?
        w_size_items.each do |w_size_item|
          w_size_item.rack_lists_warehouse_line_items.each do |rlwli|
            if rlwli.quantity_present >= quantity && quantity > 0
              rlwli.decrement!(:quantity_present, quantity)
              break
            elsif rlwli.quantity_present > 0 && quantity > 0
              quantity -= rlwli.quantity_present
              rlwli.decrement!(:quantity_present, rlwli.quantity_present)
              break if quantity <= 0
            end
          end
        end
      else
        wli.rack_lists_warehouse_line_items.each do |rlwli|
          if rlwli.quantity_present >= quantity && quantity > 0
            rlwli.decrement!(:quantity_present, quantity)
            break
          elsif rlwli.quantity_present > 0 && quantity > 0
            quantity -= rlwli.quantity_present
            rlwli.decrement!(:quantity_present, rlwli.quantity_present)
            break if quantity <= 0
          end
        end
      end
    wli.change_in_stock_and_quantity(wli.variant || wli.design,quantity, 'decrement!')
    end
  end
end