module BulkUpload
  String.send(:define_method,:if_transform) do |value,replace_with,else_replace=""|
    self.downcase == value ? replace_with : else_replace
  end

  # :name specifies the attribute name
  # :col specifies the respective column to be used
  # :value contains array of symbol/string if symbol attributes are accesed other wise the value itself
  # :p_name is public name used in excel sheet
  # :mapping maps the current value from given data hash. eg property_value name to id
  # :dynamic_mapping maps the current value of variant with option type according to category so as to minimize code
  # :operation performs operation on values arguments array is supposed to be passed 
  # :instance_operation performs on instance e.g. design, variants
  # :validation custom validation to be defined in proc
  # :message custom message for custom validation failure
  # :set_variable sets custom varibale which can be used by value_if and present
  # :value_if process the value only if a perticular variable is set using set variable
  # :present present validation can have either. if symbol is given checks custom variable values other wise only check if present attribute is set or not
  # :skip specifies the columns to be skipped in failed designs view page
  # :child specifies the columns to be considered for Variant lie quantity, price, design code
  # :format specifies the columns to be included in mirraw format but to skip in amazon format

  @hash=YAML.load_file(Rails.root.to_s+"/config/bulk_upload_config.yml")

  @meta_data={}
  def self.meta_data
    @meta_data
  end

  def self.write_attr(key,object,attribute)
    attribute_code=[]
    value_if=attribute[:value_if]
    attribute_code << "if variable[:#{value_if}].present?" if value_if.present?
    self.meta_data[key][:child_cols][attribute[:p_name].to_sym]=attribute[:col] if attribute[:child].present?
    self.meta_data[key][:skip_columns] << attribute[:p_name] if attribute[:skip].present?
    if attribute[:myntra_p_name].present?
      self.meta_data[key][:myntra_header] ||= {} # Ensure it's initialized as a hash
      self.meta_data[key][:myntra_header][attribute[:myntra_p_name]] = attribute[:p_name]
    end
    if (column = attribute[:col]).present?
      attribute_code << "current_value = row[#{column}]"
      self.meta_data[key][:header][column]||=attribute[:p_name]
      if attribute[:format].present?
        self.meta_data[key][:variant_positions][attribute[:p_name]] = column
      else
        self.meta_data[key][:amazon_header][column]||=attribute[:p_name]
      end
    elsif (value = attribute[:value]).present?
      unless value.size==1
        attribute_code << "current_value_array = []"
        value.each do |attr_name|
          if attr_name.is_a?(Symbol)
            attribute_code << "current_value_array << #{object}.#{attr_name} if #{object}.respond_to?(:#{attr_name})"
          else
            attribute_code << "current_value_array << '#{attr_name}'"
          end
        end 
        attribute_code << "current_value = current_value_array.join(' ')"
      else
        attr_name=value[0]
        if attr_name.is_a?(Symbol)
          attribute_code << "current_value = #{object}.send(:#{attr_name}) if #{object}.respond_to?(:#{attr_name})"
        else
          attribute_code << "current_value = '#{attr_name}'"
        end
      end
    end
    if (mapping = attribute[:mapping]).present?
      attribute_code << "current_value = data[:#{mapping}][current_value.tr(\"\u00A0\",' ').strip.underscore.tr(' .','_').to_sym] if current_value.present?"
      self.meta_data[key][object]||=Set.new
      self.meta_data[key][object].add(attribute[:mapping])
    elsif (mapping = attribute[:dynamic_mapping]).present?
      attribute_code << "if #{object}.respond_to?(:size_option_name)"
      attribute_code << "size_name = #{object}.size_option_name + '_size'"
      attribute_code << "current_value = data[size_name.to_sym][current_value.tr(\"\u00A0\",' ').strip.underscore.tr(' .','_').to_sym] if current_value.present?"
      attribute_code << "end"
      self.meta_data[key][object]||=Set.new
      mapping.split(',').each{|m| self.meta_data[key][object].add(m)}
    end
    attribute_code << "current_value_present = current_value.present?"
    if (present = attribute[:present]).present?
      if present.is_a?(Symbol)
        attribute_code << "error << '#{attribute[:p_name] || attribute[:name].to_s.camelize} must be present or #{attribute[:p_name] || attribute[:name].to_s.camelize} not found' unless current_value_present || !variable[:#{present}].present?"
      else
        self.meta_data[key][:mandatory_columns][object] ||=Set.new
        self.meta_data[key][:mandatory_columns][object].add((attribute[:mapping].present? ? attribute[:mapping] : attribute[:name]))
        attribute_code << "error << '#{attribute[:p_name] || attribute[:name].to_s.camelize} must be present or #{attribute[:p_name] || attribute[:name].to_s.camelize} not found' unless current_value_present"
      end
    end
    attribute_code << "if current_value_present"
    attribute_code << "current_value = current_value.send(*#{attribute[:operation]})" if attribute[:operation].present?
    attribute_code << "error << \"#{attribute[:message] || ("Invalid " + (attribute[:p_name] || attribute[:name].to_s.camelize))}\" unless #{attribute[:validation]}.call(current_value)" if attribute[:validation].present?
    attribute_code << "#{object}.send(:custom_write_attribute,:#{attribute[:name]},current_value)"
    attribute_code << "#{object}.send(*#{attribute[:instance_operation]})" if attribute[:instance_operation].present?
    attribute_code << "end"

    if object == "design" && attribute[:name] == :video_link
      attribute_code << "if current_value.present?"
      attribute_code << "#{object}.video_link = current_value"
      attribute_code << "end"
    end

    if(variable = attribute[:set_variable]).present?
      attribute_code << "variable[:#{variable[:name]}] = #{variable[:value]}.call(current_value)"
      attribute_code << "puts variable"
    end
    attribute_code << "end" if value_if.present?
    return attribute_code.join("\n ")
  end

  method_code=[]
  @hash.each do |key,version_def|
    self.meta_data[key]={}
    self.meta_data[key][:header],self.meta_data[key][:amazon_header],self.meta_data[key][:child_cols],self.meta_data[key][:variant_positions],self.meta_data[key][:skip_columns],self.meta_data[key][:mandatory_columns],self.meta_data[key][:myntra_header]=[],[],{},{},[],{},{}
    method_code<<"def self.#{key.to_s.underscore}(design,row,data={})"
    method_code<<"data.default={}\n error=[]\n variable={}" 
    version_def.each do |attribute|
      if attribute.has_key?(:name)
        # values which are stored directly to design 
        method_code << BulkUpload.write_attr(key,"design",attribute)
      elsif attribute.has_key?(:extra)
        # extra information like url of for the version
        self.meta_data[key][:extra]=attribute[:extra]
      else
        # values stored in some association
        attribute.each do |association_key,association_value|
          method_code<<"#{association_key} = design.#{association_key}.build"
          association_value.each do |association_instance|
            method_code<<"#{association_key} = design.#{association_key}.build if #{association_key}.changed?" # build new only if last one was assigned some value
            method_code<<"#{association_key}.define_singleton_method(:custom_write_attribute) do |atr,value|"
            method_code<<"#{association_key}.class_eval {attr_accessor atr} unless #{association_key}.has_attribute?(atr)"
            method_code<<"#{association_key}.send(atr.to_s+'=',value)"
            method_code<<"end"
            association_instance.each do |association_attribute|
              if association_attribute.has_key?(:dynamic_mapping)
                method_code<<"#{association_key}.send(:custom_write_attribute,:size_option_name,design.size_option_name) if design.respond_to?(:size_option_name)"
              end
              method_code << BulkUpload.write_attr(key,association_key,association_attribute)
            end
          end
          method_code<<"design.#{association_key}.delete(#{association_key}) unless #{association_key}.changed?" # delete the last build if was not assigned any value
        end
      end
    end
    method_code<<"variant_codes = []"
    method_code<<"if design.variants.present?"
    method_code<<"design.variants.each do |v|"
    method_code<<"if v[:design_code].blank? || variant_codes.include?(v[:design_code])"
    method_code<<"error << 'Variant SKU Code cannot be blank or repetative'"
    method_code<<"else"
    method_code<<"variant_codes << v[:design_code]"
    method_code<<"end"
    method_code<<"v.price = design[:price].to_i if v[:price].to_i < #{MIN_PRICE_PER_PRODUCT}"
    method_code<<"end"
    method_code<<"end"
    method_code<<"design.set_errors(error)"
    method_code<<"return variant_codes"
    method_code<<"end"
    # p method_code.join("\n ") if key.to_s.underscore == 'kurti'
    eval(method_code.join("\n "))
    method_code=[]
  end
end