module BuyGetFreeEligibility
  # Check if BMGN applies for international
  def bmgn_international?
    international?
  end

  # Check if BMGN applies for domestic
  def bmgn_domestic?
    domestic?
  end

  # Check if BMGN applies for both international and domestic
  def bmgn_both?
    both?
  end

  # Eligibility criteria for BMGN: international or both
  def eligible_for_bmgn_international?
    international? || both?
  end

  # Eligibility criteria for BMGN: domestic or both
  def eligible_for_bmgn_domestic?
    domestic? || both?
  end

  # Eligibility criteria for BMGN: country_wise
  def check_country_wise_bmgn_availability(country_code)
    country_code.downcase.eql?("in") ? eligible_for_bmgn_domestic? : eligible_for_bmgn_international?
  end
end
