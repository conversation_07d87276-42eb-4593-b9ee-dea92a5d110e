module ClevertapNotification
  class << self
    include Sidekiq::Extensions::Klass
    def push_notification(message, campaign_key, target, target_type = 'Identity')
      push(message, campaign_key, target, target_type)
    end

    def modify_and_push_notification(messages, campaign_key, app_source = 'ios' ,target_type = 'Identity')
      if messages.present? && app_source.exclude?('designer')
        messages.each do |message|
          modified_message = modify(message)
          targets = get_targets(message['target'], app_source)
          push(modified_message, campaign_key, targets, target_type, app_source)
        end
      end
    end

    private

    def get_targets(target, app_source)
      return [target] if Account.find_by_id(target).present?
      current_date = Date.today
      from_date = (current_date - 1).strftime('%Y%m%d').to_i
      to_date = current_date.strftime('%Y%m%d').to_i
      params = {
        event_name: 'App Launched',
        common_profile_properties: {
          profile_fields: [
            {
              name: 'deviceid',
              operator: 'equals',
              value: target
            }
          ]
        },
        from: from_date,
        to: to_date
      }.to_json
      res = get_clevertap_ids(params, app_source)
      res.present? ? res : [target] 
    end

    def get_clevertap_ids(params, app_source)
      # Get CleverTap Ids Based OF Device ID
      response = request(params, app_source, 'https://api.clevertap.com/1/profiles.json?events=false')
      return [''] unless response['status'] == 'success'
      url = "https://api.clevertap.com/1/profiles.json?cursor=#{response['cursor']}"
      res = request({}, app_source, url)
      object_ids = res['records'].flat_map do |record|
        record['all_identities'].present? ? record['all_identities'] : []  
      end.compact
    end

    def modify(message)
      msg = message['alert'].dup
      msg['platform_specific'] = {
        'ios' => message['android']['extra'].merge(
          'mutable-content' => 'true',
          'mediaType' => 'image',
          'default_sound' => true,
          'mediaUrl' => message['android']['extra']['PushImageHandler']
        ),
        'android' => message['android']['extra'].merge(
          'wzrk_cid' => 'mirrawpush',
          'default_sound' => true,
          'message' => message['alert']['body']
        )
      }
      msg
    end

    def push(message, campaign_key, target, target_type, app_source)
      params = {
        to: {
          target_type => target
        },
        tag_group: campaign_key,
        respect_frequency_caps: true,
        content: message
      }.to_json
      request(params, app_source, 'https://api.clevertap.com/1/send/push.json')
    end

    def request(params, app_source, url)
      options = form_request_options(params, app_source, url)
      begin
        response = execute_request(options)
        JSON.parse(response.body)
      rescue RestClient::Exception => e
        raise "#{e.inspect} ::::: #{options}"
      end
    end

    def form_request_options(params, app_source, url)
      if app_source.include?('android')
        app_data = get_app_credentials(app_source)
        account_id = app_data['id']
        passcode = app_data['code']
      else
        account_id = ENV.fetch('CLEVERTAP_ACCOUNT_ID')
        passcode = ENV.fetch('CLEVERTAP_PASSCODE')
      end
      {
        headers: {
          'Content-Type' => 'application/json',
          'X-CleverTap-Account-Id' => account_id,
          'X-CleverTap-Passcode' => passcode
        },
        method: :post,
        url: url,
        payload: params
      }
    end

    def get_app_credentials(app_source)
      true ? (CLEVERTAP_APP_ID_LIST['production'][app_source] || CLEVERTAP_APP_ID_LIST['production']['android']) : (CLEVERTAP_APP_ID_LIST['staging'][app_source] || CLEVERTAP_APP_ID_LIST['staging']['android'])
    end

    def execute_request(options)
      RestClient::Request.execute(options)
    end

  end
end
