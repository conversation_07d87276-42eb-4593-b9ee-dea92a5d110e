module Priceable
  def self.included(base)
    base.class_eval do
      include Promotions::Design
    end
  end

  def discount_percent
    return design.discount_percent if is_variant?
    seller_campaign = self.designer.seller_campaigns.select { |campaign| campaign.active? }
    campaign_discount = seller_campaign.present? ? self.design_campaign_discounts.find_by(designer_id: self.designer_id, seller_campaign_id: seller_campaign.first.id).try(:discount).to_f : 0
    (100 - ((100 - self[:discount_percent].to_f) * (100 - self.get_vendor_additional_discount.to_f) / 100 * (100 - campaign_discount ) / 100))
  end

  def discount_price(return_type=RETURN_NORMAL)
    return design.discount_price if self[:price].to_i == 0 && is_variant?
    ((self[:price].to_i * (100 - discount_percent) / 100).to_i * (return_type==RETURN_SCALED ? get_scale : 1.0)).to_i
  end

  def get_scale(country_code=nil)
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && bmgnx_hash[:scale].to_i >= 1 && discount_price < bmgnx_hash[:filter].to_i
      return bmgnx_hash[:scale].to_f
    elsif (qpm_scale = QuantityDiscountPromotion.scale.to_f) > 0
      return qpm_scale
    end
    return design.get_scale if is_variant?
    @dynamic_price=nil unless country_code == nil #Removes dynamic price instance from design if country_code is passed

    country_code ||= Design.country_code || 'US'
    country_code = 'US' if SWITCH_DP_TO_INTERNATIONAL && country_code != 'IN'

    if DYNAMIC_PRICE_ENABLED && country_code!='IN' && self.dynamic_pricing?
      unless @dynamic_price.present?
        sanity_condition = SWITCH_DP_TO_INTERNATIONAL || ((Design.country_code || 'US') == country_code)
        if  sanity_condition &&  self.dynamic_price_for_current_country.loaded? # check if dynamic price is preloaded or not
          @dynamic_price = self.dynamic_price_for_current_country.first # take benefit of preloading
        elsif self.dynamic_prices.loaded?
          @dynamic_price_hash ||= create_hash_for_dynamic_price
          return (@dynamic_price_hash[country_code.to_sym] || 1).to_f
        else
          @dynamic_price = self.dynamic_prices.where(country_code: country_code).first   # if data is not preloaded sent a query to database
        end
      end
      return @dynamic_price.scale.to_f if @dynamic_price.try(:country_code)==country_code
    elsif DYNAMIC_PRICE_ENABLED && self.dynamic_pricing? && (country_code == 'IN' || Design.country_code == 'IN')
      @dynamic_price = self.dynamic_price_for_domestic.first
      return @dynamic_price.scale.to_f if @dynamic_price.try(:country_code)== 'IN'
    end
    1.0
  end

  def price(return_type=RETURN_SCALED)
    return self[:price].to_i if is_variant? && new_record?
    return design.price(return_type) if self[:price].to_i == 0 && is_variant?
    (self[:price].to_i * (return_type==RETURN_SCALED ? get_scale : 1.0)).to_i
  end
  
  def base_price(return_type=RETURN_SCALED)
    (self[:price].to_i * (return_type==RETURN_SCALED ? get_scale(@country_code): 1.0)).to_i
  end

  def get_vendor_additional_discount
    des = is_variant? ? self.design.designer : self.designer
    self.sor_available? ? 0 : des.vendor_additional_discount_percent.to_i
  end

  def get_display_price_for_transfer_model
    designer = is_variant? ? self.design.designer : self.designer
    rate = designer.transfer_model_rate.to_i.nonzero? || FIXED_TRANSACTION_RATE
    sell_price = self.transfer_price.to_f / (1 - (rate/100.0) * (1 + IGST/100.0))
    (sell_price/(1 - self.discount_percent/100.0)).round
  end

  def get_vendor_selling_amount
    is_transfer_model? ? self.transfer_price : self.discount_price
  end

  def get_vendor_payout_amount
    designer = is_variant? ? self.design.designer : self.designer
    is_transfer_model? ? self.transfer_price : (self.discount_price * (100 - designer[:transaction_rate]) / 100.0).round
  end

  def price_lock_for_designer
    # if PRICE_LOCK_ENABLE == "true"
    #   #if self.designable_type == 'Saree'
    #   unless self.new_record?
    #     if self.price_changed? || ((self.class == Design) && self.discount_percent_changed?)
    #       #sale_available? && (self.price_changed? || self.discount_price_changed? || self.discount_percent_changed?)
    #       errors[:base] << "You are not allow to change price in promotion period."
    #     end
    #   end
    #   #end
    # end
  end

  private
  def is_variant?
    self.class == Variant
  end
end