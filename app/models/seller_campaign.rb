class SellerCampaign < ActiveRecord::Base
    validates :name, presence: true
    validates :description, presence: true
    validates :start_date, presence: true
    validates :end_date, presence: true
    has_many :designer_campaign_participations
    has_many :designers, through: :designer_campaign_participations
    has_many :design_campaign_discounts
    validate :end_date_after_start_date
    scope :active, -> { where('start_date <= ? AND end_date >= ?',Time.now, Time.now) }
    validate :no_overlapping_campaign
    

    def no_overlapping_campaign
      if SellerCampaign.where.not(id: id)
                       .where("start_date <= ? AND end_date >= ?", end_date, start_date)
                       .exists?
        errors.add(:base, "Another campaign already exists during this period.")
      end
    end

    def active?
      Time.current.between?(start_date, end_date)
    end

    private
  
    def end_date_after_start_date
      if end_date < start_date
        errors.add(:end_date, "must be greater than or equal to the start date")
      end
    end

end
