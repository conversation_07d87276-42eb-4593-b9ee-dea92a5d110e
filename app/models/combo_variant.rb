# app/models/combo_variant.rb
class ComboVariant < ActiveRecord::Base
    has_and_belongs_to_many :option_type_values, 
                          join_table: :option_type_values_combo_variants

    has_many :option_types, through: :option_type_values
    has_many :option_type_values_combo_variants
    belongs_to :design
    has_many :line_items, dependent: :nullify
    validates :quantity, :numericality => { greater_than_or_equal_to: 0, only_integer: true}
    validates_numericality_of :price, greater_than_or_equal_to: MIN_PRICE_PER_PRODUCT, only_integer: true
    validates :design_code, presence: { message: "can't be blank" }

    def set_option_type_value_id(id=nil)
        id||=self.option_type_value_id #self.attributes[:option_type_value_id]
        if id.present? && self.quantity.present?
          self.option_type_values_combo_variants.build(option_type_value_id: id)
        else
          self.class_eval {attr_accessor :option_type_value_id}
          self.option_type_value_id = nil
        end
        return nil
    end
end