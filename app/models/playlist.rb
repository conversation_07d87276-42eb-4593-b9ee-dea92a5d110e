class Playlist < ActiveRecord::Base
  belongs_to :page
  belongs_to :category
  validates :slug, uniqueness: { scope: :page_id }

  serialize :country_ids, Array

  SORT_BY_OPTIONS = [
    'top_rated',
    'l2h',
    'h2l',
    'new',
    'discount',
    'popularity',
    'bstslr',
    'default',
    'trending',
    'trending-designs',
    'popular',
    'recommended',
    'recent-30'
  ].freeze

  def country_code_string
    country_code.join(', ')
  end
  
  def country_code_string=(value)
    self.country_code = value.split(',').map(&:strip)
  end
  

  def country_codes
    self[:country_code] || []
  end

  def country_codes=(codes)
    self[:country_code] = codes.reject(&:blank?)
  end  
end
