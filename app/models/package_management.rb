class PackageManagement < ActiveRecord::Base
  
  def self.generate_outscan_report(outscan_condition,email,batch_code = nil)
    filename = batch_code ? "#{batch_code}_Outscan_Report" : "Outscan_Report"
    s_clause = 'shipments.id, orders.number as order_number, shipments.number as tracking_num, orders.country as orders_country,shippers.name as shipper_name, shipments.out_scan_date, orders.out_scan_notes, shipments.out_scan_batch'
    headers = ["Order Number","Tracking Number","Country","Courier Company","Out Scan Timestamp","Out Scan Notes","Out Scan Batch"]
    file = CSV.generate do |csv|
      csv << headers
      Shipment.forward.select(s_clause).joins(:order, :shipper).where(outscan_condition).where('designer_order_id is null').find_each(batch_size: 200) do |shipment|
        csv << [shipment.order_number, shipment.tracking_num, shipment.orders_country, shipment.shipper_name, shipment.out_scan_date, shipment.out_scan_notes, shipment.out_scan_batch]
      end
    end
    emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer(filename,'Please Find Attachment.',emails,{"#{filename}.csv"=> file}).deliver
  end

  def self.mark_package_out_scanned(order,tracking_number,date,for_csv_file,account_email,batch_number)
    order_number = order.number
    if order.present?
      shipment = Shipment.select('id,number,out_scan_date,shipper_id').preload(:shipper, :line_items).where(created_at:6.months.ago..DateTime.now).where(number: tracking_number, order_id: order.try(:id)).where('designer_order_id is null').last
      track_error = ''
      scan_message = ''
      out_scan_notes = order.out_scan_notes
      if shipment.blank?
        track_error = "Tracking Number Mismatch -> Scanned Number :#{tracking_number} : Batch #{batch_number},"
        out_scan_notes = "#{out_scan_notes}#{track_error}"
        tracking_error = true
        scan_message = 'Outscan Tracking Mismatch'
      end
      if shipment.try(:out_scan_date).present?
        repeat_error = "Dispatch Repeat -> Latest Dispatch : #{date} : Batch #{batch_number},"
        out_scan_notes = "#{out_scan_notes}#{repeat_error}" 
        note_content = "Dispatch Repeat -> Latest Dispatch : #{date} : Batch #{batch_number}"
        scan_message = 'Repeat Outscan'
        Order.where(id: order.id).update_all(out_scan_notes: out_scan_notes,out_scan_batch: batch_number)
        shipment.update_columns(out_scan_batch: batch_number, out_scan_date: date)  
      else
        if track_error.present?
          note_content = track_error
          Order.where(id: order.id).update_all(out_scan_notes: out_scan_notes,out_scan_batch: batch_number)
        else
          note_content = "Out Of Mirraw Warehouse #{date} : Batch #{batch_number}"
          scan_message = 'Outscan Success'
          send_notifies_for_partial_dispatch(order,shipment) if order.partial_dispatch?
          Order.where(id: order.id).update_all(out_of_mirraw_warehouse: date, out_scan_notes:out_scan_notes,out_scan_batch: batch_number)
          shipment.update_columns(out_scan_batch: batch_number, out_scan_date: date)  
        end
      end
      order.add_notes_without_callback(note_content, 'dispatch', account_email) unless ["added tag dnd", "added tag dndos", "added tag dndcs"].any?{|notes| order.notes.downcase.include? notes}
      order.add_notes_without_callback(note_content, 'dispatch', account_email) if ["removed tag dnd", "removed tag dndos", "removed tag dndcs"].any?{|notes| order.notes.downcase.include? notes}
      shipment.try(:line_items).to_a.each{|item| item.add_into_scan(scan_message, account_email.id)}      
      error = "#{track_error}#{repeat_error}"
      return order,shipment,error,tracking_error unless for_csv_file
    end
  end

  def self.send_notifies_for_partial_dispatch(order,shipment)
    SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "mail_partial_dispatch_tracking_info_to_international_buyer", {"#{order.class}": order.id},{"#{shipment.class}":shipment.id})
    SidekiqDelayGenericJob.perform_async(order.class, order.id, "send_confirmation_sms_international", 'partial_dispatch') if order.billing_international? && order.tracking_number.present?
  end

  def self.csv_mark_package_out_scanned(filename,account_email)
    full_path = AwsOperations.get_aws_file_path(filename)
    orders_numbers ={}
    order_numbers_array =[]
    CSV.new(open(full_path), {:headers => true, :header_converters => :symbol}).each do |line|
      if (line[0].present? && line[1].present? && line[2].present? && line[3].present?)
        orders_numbers[line[0]] = [line[1],line[2],line[3]] 
        order_numbers_array << line[0]
      end    
    end
    orders = Order.where(number: order_numbers_array)
    time = DateTime.now.to_s(:time)
    orders.each do |order|
      out_scan_date = DateTime.parse("#{orders_numbers[order.number][1]} #{time}")
      mark_package_out_scanned(order,orders_numbers[order.number][0],out_scan_date,true,account_email,orders_numbers[order.number][2])
    end
  end

  def self.assign_rack_and_code(designer_order_ids,current_account)
    designer_orders = DesignerOrder.where(id: designer_order_ids).preload(:order,:rack_list,line_items: [:line_item_addons, :design])
    rack_code_pdf = ''
    if designer_orders.present?
      stitching_designer_orders = []
      jewellery_designer_orders = []
      designer_orders.each do |dos|
        des_order_type = dos.check_stitching_or_jewellery_des_order
        if des_order_type == 'stitching'
          stitching_designer_orders << dos
        elsif des_order_type == 'jewellery'
          jewellery_designer_orders << dos
        end
      end
      designer_orders = designer_orders - (stitching_designer_orders + jewellery_designer_orders)
      available_racks = RackList.where('space_filled < max_capacity').where('description is null OR description IN (?)',['stitching','jewellery']).order('priority asc,space_filled desc,code asc')
      available_stitching_racks = available_racks.select{|r| r.description == 'stitching'}
      available_jewellery_racks = available_racks.select{|r| r.description == 'jewellery'}
      available_racks = available_racks - (available_stitching_racks + available_jewellery_racks)
      rack_detail_hash = []
      rack_detail_hash += self.assign_stitch_and_non_stitch_racks(designer_orders,available_racks)
      rack_detail_hash += self.assign_stitch_and_non_stitch_racks(stitching_designer_orders,available_stitching_racks)
      rack_detail_hash += self.assign_stitch_and_non_stitch_racks(jewellery_designer_orders,available_jewellery_racks)      
      if rack_detail_hash.present?
        pdf_content =  ActionController::Base.new().render_to_string(
          template: '/shipments/rack_code_stickers',
          layout: false,
          locals: {:@rack_detail_hash => rack_detail_hash}
        )
        rack_code_pdf = WickedPdf.new.pdf_from_string(pdf_content,{orientation: 'Landscape'})
      end
      LineItem.bulk_add_into_scan('DesignerOrder', designer_order_ids, 'Package Inscan', current_account.id)
      LineItem.bulk_add_into_scan('DesignerOrder', designer_order_ids, 'Rack Assign', current_account.id)
    end
    ShipmentMailer.mail_rack_code_stickers(current_account.email,rack_code_pdf).deliver
  end

  def self.assign_stitch_and_non_stitch_racks(designer_orders,racks)
    rack_hash=[]
    designer_orders.each do |d_order|
      all_line_items = d_order.line_items.to_a
      total_quantity = all_line_items.sum(&:quantity)
      rack = racks.find{|r| (r.max_capacity - r.space_filled) >= total_quantity}
      if rack.present?
        pm = PackageManagementController.new()
        pm.assign_item_code(d_order)
        if d_order.rack_list_id.present?
          new_rack_code = d_order.rack_list.code
        else
          d_order.update_column(:rack_list_id,rack.id)
          new_rack_code = rack.code
          new_quantity = rack.space_filled + total_quantity
          rack.update_column(:space_filled,new_quantity)
          rack[:space_filled] = new_quantity
        end
        order = d_order.order
        rack_hash.push(*d_order.get_rack_hash_by_design(new_rack_code))
        if rack.space_filled == rack.max_capacity
          racks = racks - [rack]
        end
      end
    end
    return rack_hash
  end

  def self.mark_csv_tailoring_inscan(tailor_data, all_tailor_products, all_measurement_scans, line_item_scans, current_account)    
    material_counts, product_material = Hash.new(0), {}
    if all_tailor_products.present?
      all_tailor_products.each_slice(100) do |tailor_products|
        received_time = Time.zone.parse("#{Time.now.strftime('%Y/%m/%d %H:%M:%S')}")          
        found_measurements = StitchingMeasurement.preload(:tailoring_info).joins(:tailoring_info).where(id:tailor_products).where('product_received_from_tailor_on is null').where('tailor_id = ?', tailor_data[1])
        found_measurements.each do |mes| 
          mes.tailoring_info.each do |t_info|
            if t_info.tailor_id == tailor_data[1]
              material_counts[t_info.tailoring_material] += t_info.line_item_quantity.to_i 
              product_material[mes.id] = [t_info.tailoring_material, t_info.line_item_quantity.to_i]              
            end
          end
        end
        found_measurements.update_all(product_received_from_tailor_on:received_time,product_received_from_tailor_by:current_account.name)       
      end        
      failed_inscan_products = StitchingMeasurement.where('id IN (?) AND product_received_from_tailor_on IS NULL', all_tailor_products).pluck(:id).map(&:to_s)      
      LineItem.bulk_add_into_scan('LineItem', line_item_scans.values_at(*(all_measurement_scans.keys - failed_inscan_products)), 'Tailoring Inscan Done', current_account.id)
      PackageManagement.get_tailoring_inscan_report(failed_inscan_products, all_measurement_scans, current_account.email, material_counts, tailor_data[0], product_material)
    end
  end

  def self.get_out_scan_batch_number(shipper_id, batch_type=nil)
    shipper = Shipper.find shipper_id
    start_time = Date.today.beginning_of_day
    end_time = Date.today.end_of_day
    new_batch_count = Shipment.forward.where('shipper_id = ? and designer_order_id is null and out_scan_date BETWEEN  ? AND ?', shipper_id, start_time, end_time).pluck(:out_scan_batch).uniq.count + 1    
    batch_type_name = (shipper.try(:name) == 'Atlantic' && batch_type == 'UK') ? '_UK' : ''
    new_batch_number = "#{shipper.try(:name).upcase}#{batch_type_name}_OUTSCAN_#{Date.today.strftime("%d_%m_%y")}_##{new_batch_count}"
  end

  def self.mail_dhl_ecom_out_scan_sheet(tracking_numbers, current_account, shipper_id)
    dhl_ecom_urls = Mirraw::Application.config.dhl_ecom
    batch_number = get_out_scan_batch_number(shipper_id)
    out_scan_timestamp = DateTime.now
    file = CSV.generate do |csv|
      csv << ['Order Number', 'Tracking Number', 'Out Scan Date', 'Batch Number', 'Courier Company']
      tracking_numbers.each do |t_number|
        csv << [t_number.split('_')[0].to_s.gsub(dhl_ecom_urls[:credentials]['prefix'], ''), t_number,out_scan_timestamp, batch_number, 'DHL ECOM']
      end
    end
    filename  = 'packages/outscan/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
    AwsOperations.create_aws_file(filename, file,false)
    emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("DHL ECOM OUT SCAN SHEET #{Date.today}", 'Please Find Attachment.', emails, {"Out Scan #{batch_number}.csv"=> file}).deliver
    PackageManagement.csv_mark_package_out_scanned(filename, current_account)    
  end

  def self.mail_rtv_outscan_report(start_date, end_date, shipper_name, to_email)
    w_shipper_name_check = shipper_name.present? ? (shipper_name == 'Other' ? "shipper_name NOT IN ('Rapid Delivery','Delhivery', 'Xpress Bees')" : "Shipper_name = '#{shipper_name}'") : ''
    records_found = false
    file = CSV.generate do |csv|
      csv << ['Awb Number', 'Order Number', 'Shipper Name', 'Out Of Mirraw Warehouse Date', 'Shipment weight']
      RtvShipment.preload(rtv_shipment_line_items: :payment_order).where(w_shipper_name_check).where('out_of_mirraw_warehouse between ? and ?', start_date.beginning_of_day, end_date.end_of_day).find_each(batch_size: 100) do |rtv_shipment|
        records_found = true
        csv << [rtv_shipment.number, rtv_shipment.rtv_shipment_line_items.last.try(:payment_order).try(:number), rtv_shipment.shipper_name, rtv_shipment.out_of_mirraw_warehouse.to_date, rtv_shipment.weight]
      end  
    end
    if records_found
      emails = {'to_email'=> to_email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
      OrderMailer.report_mailer("Rtv Outscan Report #{shipper_name} #{Date.today}", 'Please Find Attachment.', emails, {"Rtv Outscan Report #{shipper_name} #{Date.today}.csv"=> file}).deliver
    end
  end

  def self.mark_handover_to_stitching(input_details, total_products_hash, current_account, receiving_param, handover_to, scan_action, handover_for, receive_pending_products)   
    scan_type_message = get_scan_type_message(receiving_param, handover_for, scan_action, handover_to)    
    incorrect_des_ids, handover_to_stitching_item_ids = Hash.new, []
    input_details.each do |key,val|
      all_scanned_des_ids = val.dup
      incorrect_des_ids[key] = (all_scanned_des_ids - val.delete_if{|i| i.to_s.length > 10})
      LineItem.joins(:order).preload(order: [:tags, :stylist]).where(design_id:val).where('orders.state NOT IN (?) AND status IS NULL AND orders.number = ?',['cancel','new'],key).each do |item|
        order = item.order
        note_object = (scan_action == 'stylist_receive' ? order : item.designer_order)
        stylist = order.stylist
        if (is_stitching_not_needed = item.stitching_required.blank?) || order.tags.to_a.map(&:name).include?('addon') 
          (total_products_hash["#{order.number}-#{item.design_id}"] ||= []) << [(is_stitching_not_needed ? 'Stitching not needed' : 'Addon Tag Present'), stylist.try(:name)]
        else
          item.add_into_scan(scan_type_message, current_account.id)
          note_object.add_notes_without_callback("#{item.design_id} #{scan_type_message} ", 'stitching', current_account)
          (total_products_hash["#{order.number}-#{item.design_id}"] ||= []) << ['Successful', stylist.try(:name)]
          (handover_to_stitching_item_ids << item.id) if scan_type_message == 'Handover To Stitching'
        end
      end
    end
    LineItem.update_rack_status(condition: {id: handover_to_stitching_item_ids}, status: 'rack_out')    
    failed_products_count = 0
    receive_pending_count = receive_pending_products.count
    file = CSV.generate do |csv|
      csv << ['Scanned Barcode', 'Status', 'Stylist']
      total_products_hash.each do |key, val| 
        csv << [val[0], val[1].try(:[], 0).presence || 'Order or Product in Wrong State.', val[1].try(:[], 1)]
        failed_products_count += 1 if val[2].try(:[], 0) != 'Successful'
      end
      incorrect_des_ids.each do |ord_num, des_ids|
        des_ids.each do |id|
          csv << ["#{ord_num}-#{id}", 'Wrong Barcode Scanned Or Content Wrong.', '']
          failed_products_count += 1
        end
      end
      receive_pending_products.each do |product_barcode|        
        csv << [product_barcode, 'Failed Due To Tailoring Receive Pending', '']
      end
    end
    emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("#{scan_type_message} Report #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.  Total Scanned : #{total_products_hash.keys.count + receive_pending_count}, Successful : #{(total_products_hash.keys.count - failed_products_count).abs}, Failed : #{failed_products_count + receive_pending_count} ", emails, {"#{scan_type_message} Report #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}.csv"=> file}).deliver
  end

  def self.generate_inward_bag(tracking_numbers, current_account, purchase_order=nil, received_qty=nil, designer_order_ids=nil)
    is_inscan_by_ids = designer_order_ids.present?
    if (tracking_numbers.present? || is_inscan_by_ids)
      total_scanned_count = tracking_numbers.values.flatten.count      
      file = CSV.generate do |csv|
        csv << ['Inscanned Awb Numbers']
        tracking_numbers.values.flatten.each do |num|
          csv << [num]
        end
        designer_order_ids.each {|id| csv << [id[1]]} 
      end
      emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email' => ACCESSIBLE_EMAIL_ID['bag_report_cc_emails']}
      OrderMailer.report_mailer("Courier Inscan Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.", emails, {"Courier Inscan Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}.csv"=> file}).deliver
      exact_found_tracking_numbers, new_bag, inscanned_des_ord_ids, stitching_count  = [], nil, [], 0
      original_tracking_nums = tracking_numbers['valid'].to_a.uniq
      system_awb_numbers, rto_inscanned_awbs = {}, []
      if tracking_numbers['valid'].present? || is_inscan_by_ids
        tracking_numbers['valid'] = original_tracking_nums.map(&:downcase)
        total_scanned_count = tracking_numbers.values.flatten.count
        inscanned_designer_orders = DesignerOrder.preload(:line_items).joins(:order, :line_items).where('ship_to = ? and designer_orders.confirmed_at >= ?', 'mirraw', INSCAN_DO_MONTHS.to_i.month.ago).where("(LOWER(tracking_num) IN (?) AND package_received_on is NULL AND inward_bag_id is null) OR (LOWER(recent_tracking_number) IN (?)) AND orders.state not in (?) AND (line_items.qc_status is null or line_items.qc_status = ?)", tracking_numbers['valid'], tracking_numbers['valid'], ['dispatched', 'cancel'], false).uniq
        if inscanned_designer_orders.present?
          inscanned_des_ord_ids << inscanned_designer_orders.map(&:id)
          new_bag = generate_inward_bag_entry(purchase_order.present?)
          exact_found_tracking_numbers = (tracking_numbers['valid'] & (inscanned_designer_orders.map(&:tracking_num).compact.map(&:downcase) + inscanned_designer_orders.map(&:recent_tracking_number).compact.map(&:downcase))).uniq
          data_to_be_imported, stitching_count = mark_designer_order_package_inscanned(inscanned_designer_orders, new_bag, system_awb_numbers, inscanned_des_ord_ids, current_account.name, purchase_order, exact_found_tracking_numbers)
          DesignerOrder.import data_to_be_imported, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:inward_bag_id, :package_received_on, :package_received_by, :package_status, :purchase_order_id]}
        end

        if exact_found_tracking_numbers.size < tracking_numbers['valid'].size
          remaining_track_nums = (tracking_numbers['valid'] - exact_found_tracking_numbers)
          remaining_track_nums.each do |num|
            if (des_ords = DesignerOrder.preload(:line_items).joins(:order, :line_items).where('ship_to = ? and designer_orders.confirmed_at >= ?', 'mirraw', INSCAN_DO_MONTHS.to_i.month.ago).where('(lower(tracking_num) like ? AND package_received_on is NULL AND inward_bag_id is null) OR (lower(recent_tracking_number) like ?) and orders.state not in (?) AND (line_items.qc_status is null or line_items.qc_status = ?)', "%#{num}%", "%#{num}%", ['dispatched', 'cancel'], false)).present?
              inscanned_des_ord_ids << des_ords.map(&:id)
              stitching_count += 1 if des_ords.map(&:line_items).flatten.any?{|item| item.status.blank? && item.stitching_required == 'Y'}
              exact_found_tracking_numbers << num
              (system_awb_numbers[num] ||= {})['critical_remark'] = des_ords.any?{|d| d.critical? } ? 'Contains Crtitcal Order' : ''            
              new_bag = generate_inward_bag_entry(purchase_order.present?) if new_bag.blank?            
              des_ords.each do |d_o|                
                found_awb_number = d_o.package_received_on.present? ? d_o.recent_tracking_number : d_o.tracking_num
                (system_awb_numbers[num]['matched_awb'] ||= []) << found_awb_number
                package_status = d_o.line_items.any?{|item| item.qc_status == false} ? 'replacement' : 'inscanned'
                d_o.update_columns(inward_bag_id: new_bag.id, package_received_on: Time.current, package_received_by: current_account.name, package_status: package_status, purchase_order_id: purchase_order)
              end
            end
          end
        end
        if is_inscan_by_ids
          to_be_imported = []          
          inscanned_des_orders_by_id = DesignerOrder.preload(:line_items).joins(:order).where("designer_orders.id IN (?) AND orders.state not in (?)", designer_order_ids.map(&:first), ['dispatched', 'cancel']).find_in_batches(batch_size: 100) do |des_orders|            
            new_bag = generate_inward_bag_entry(purchase_order.present?) if new_bag.blank?                                    
            batch_to_be_imported, stitching_count = mark_designer_order_package_inscanned(des_orders, new_bag, system_awb_numbers, inscanned_des_ord_ids, current_account.name, purchase_order, [], false)              
            to_be_imported << batch_to_be_imported
          end
          DesignerOrder.import to_be_imported.flatten, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:inward_bag_id, :package_received_on, :package_received_by, :package_status, :purchase_order_id]}
        end
        inscanned_des_ord_ids = inscanned_des_ord_ids.flatten.compact.uniq
        if inscanned_des_ord_ids.present? && (stitching_count / inscanned_des_ord_ids.count.to_f)*100 >= STITCHING_BAG_THRESHOLD_PERCENT
          new_bag.update_column(:number, (new_bag.number + '_STITCH'))
        end
        if inscanned_des_ord_ids.present?
          if AUTO_RACK_ASSIGN
            assign_rack_and_code(inscanned_des_ord_ids, current_account) 
          else
            LineItem.bulk_add_into_scan('DesignerOrder', inscanned_des_ord_ids, ['Package Inscan', "Assigned To Bag Number #{new_bag.try(:number)}"], current_account.id)          
          end
        end
        rto_inscanned_awbs, buyer_return_awbs = check_for_rto_cases(original_tracking_nums, current_account)
      end
      file  = Tempfile.new(['package_management','.csv'])
      inscan_to_be_imported = []
      CSV.open(file.path, 'wb') do |csv|      
        csv << ['Tracking Number', 'Status', 'Bag Number', 'Remark']
        tracking_numbers['valid'].to_a.each do |number|
          if rto_inscanned_awbs.exclude?(number)
            inscan_status = ((is_found = exact_found_tracking_numbers.any?{|num| num.include?(number)}) ? 'Successful' : 'Failed')
            remark = (is_found ? system_awb_numbers[number].try(:[], 'critical_remark').to_s : 'Tracking Number Not Found or Already Scanned.')
            if is_found
              system_awb_numbers[number].try(:[], 'matched_awb').to_a.uniq.each do |awb|
                inscan_to_be_imported << CourierInscan.new(awb_number: number, status: inscan_status, inward_bag_id: (is_found ? new_bag.try(:id) : nil), remark: remark, system_awb_number: awb)
              end
            else
              inscan_to_be_imported << CourierInscan.new(awb_number: number, status: inscan_status, inward_bag_id: (is_found ? new_bag.try(:id) : nil), remark: remark, system_awb_number: nil)
            end
            csv << [number, inscan_status, (is_found ? new_bag.try(:number) : ''), remark]
          else
            rto_inscan_name = (buyer_return_awbs.include?(number) ? 'Buyer Return Inscan' : 'RTO Inscan')
            inscan_to_be_imported << CourierInscan.new(awb_number: number, status: 'Successful', remark: rto_inscan_name)
            csv << [number, 'Successful', '', rto_inscan_name]
          end
        end
        tracking_numbers['invalid'].to_a.each do |inv_number|
          csv << [inv_number, 'Failed', '', "Tracking Number Length is Smaller than #{DISABLE_ADMIN_FUCTIONALITY['min_tracking_number_length'].to_i}"]
          inscan_to_be_imported << CourierInscan.new(awb_number: inv_number, status: 'Failed', inward_bag_id: nil, remark: "Tracking Number Length is Smaller than #{DISABLE_ADMIN_FUCTIONALITY['min_tracking_number_length'].to_i}", system_awb_number: nil)
        end
        designer_order_ids.each do |des_order_id|
          inscan_status, bag_obj, remark = (inscanned_des_ord_ids.include?(des_order_id[0].to_i) ? ['Successful', new_bag, ''] : ['Failed', nil, 'Not Fount or Incorrect barcode scanned.'])
          csv << [des_order_id[1], inscan_status, bag_obj.try(:number), remark]
          inscan_to_be_imported << CourierInscan.new(awb_number: des_order_id[1], status: inscan_status, inward_bag_id: bag_obj.try(:id), remark: remark, system_awb_number: nil)
        end
      end
      CourierInscan.import inscan_to_be_imported, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:awb_number, :status, :inward_bag_id, :remark, :system_awb_number]}

      filename = 'packages/bag_inscan/'+ "#{Time.current.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
      AwsOperations.create_aws_file(filename,file)
      file_url = AwsOperations.get_aws_file_path(filename)
      emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email' => ACCESSIBLE_EMAIL_ID['bag_report_cc_emails']}
      new_bag.update_attributes(bag_report_url: file_url, inscanned_count: received_qty , successful_count: received_qty, purchase_order_id: purchase_order) if new_bag.present?
      attachments = {"Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}.csv"=> File.read(file.path)}
      if new_bag.present?
        bag_label_pdf_content = "<div style='margin-top:25%;font-size: 25pt;border: 3px solid black;'><center><h1>#{new_bag.number}</h1></center></div>"
        bag_label = WickedPdf.new.pdf_from_string(bag_label_pdf_content,{orientation: 'Landscape'})
        attachments['Bag Label.pdf'] = bag_label
      end
      OrderMailer.report_mailer("Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.  Total Scanned : #{total_scanned_count}", emails, attachments).deliver      
    end
  end

  def self.rto_inscan(tracking_numbers, current_account)

    file  = Tempfile.new(['rto_inscan','.csv'])
    inscanned_designer_orders=[]
    inscanned_designer_orders = DesignerOrder.preload(:line_items).joins(:order, :line_items).where('ship_to != ? and designer_orders.confirmed_at >= ?', 'mirraw', INSCAN_DO_MONTHS.to_i.month.ago).where("(LOWER(tracking_num) IN (?) AND package_received_on is NULL AND inward_bag_id is null) OR (LOWER(recent_tracking_number) IN (?)) AND designer_orders.state in (?) AND (line_items.qc_status = ?) AND (line_items.available_in_warehouse = ?)", tracking_numbers['valid'].map(&:downcase), tracking_numbers['valid'].map(&:downcase), ['rto'], true, true).uniq

    exact_found_tracking_numbers = (tracking_numbers['valid'].map(&:downcase)& (inscanned_designer_orders.map(&:tracking_num).compact.map(&:downcase) + inscanned_designer_orders.map(&:recent_tracking_number).compact.map(&:downcase))).uniq

    des_orders=[]
    if exact_found_tracking_numbers.size < tracking_numbers['valid'].size
      remaining_track_nums = (tracking_numbers['valid'].map(&:downcase) - exact_found_tracking_numbers)
      remaining_track_nums.each do |num|
        des_orders << DesignerOrder.preload(:line_items).joins(:order, :line_items).where('ship_to != ? and designer_orders.confirmed_at >= ?', 'mirraw', INSCAN_DO_MONTHS.to_i.month.ago).where("(LOWER(tracking_num) LIKE (?) AND package_received_on is NULL AND inward_bag_id is null) OR (LOWER(recent_tracking_number) LIKE (?)) AND designer_orders.state in (?) AND (line_items.qc_status = ?) AND (line_items.available_in_warehouse = ?)", "%#{num}%", "%#{num}%", ['rto'], true, true).last
      end
    end

    dos_ids = inscanned_designer_orders.compact.uniq + des_orders.compact.uniq
    dos_ids.uniq!
    #scanning in return_designer_order
    return_dos = []
    left_tracking_numbers=[]
    left_tracking_numbers = (tracking_numbers['valid'].map(&:downcase) - (dos_ids.map(&:tracking_num).compact.map(&:downcase) + dos_ids.map(&:recent_tracking_number).compact.map(&:downcase))).uniq

    if left_tracking_numbers.present?
      all_return_dos = ReturnDesignerOrder.includes(line_items: :designer_order).where(line_items:{ designer_orders: {confirmed_at: INSCAN_DO_MONTHS.to_i.month.ago..DateTime.now}})
      left_tracking_numbers.each do |num|
        return_dos <<  all_return_dos.where("LOWER(tracking_number) LIKE (?)", "#{num}").last
      end
      return_dos.uniq!
    end

    sor_line_item_ids, inward_to_be_imported = [], []
    CSV.open(file.path, 'wb') do |csv|
      csv << ['Line Item Id', 'Status', 'Marked Recived']
      csv << ['RTO']
      dos_ids.each do |des|
        des.line_items.sor_items.each do |li|
          sor_line_item_ids << li.id
          csv << [li.id, 'Successful', 'yes']
          inward_to_be_imported << InwardDetail.new(line_item: li, mark_inscanned: true, mark_inscanned_by:current_account.id)
        end
      end
      #for return designer order
      if return_dos.present?
        csv << ['Buyer Return']
        return_dos.each do |rdo|
          rdo.line_items.sor_items.buyer_returned_items.each do |li|
            sor_line_item_ids << li.id
            csv << [li.id, 'Successful', 'yes']
            inward_to_be_imported << InwardDetail.new(line_item: li, mark_inscanned: true, mark_inscanned_by:current_account.id)
          end
        end
      end
    end
    if sor_line_item_ids.present?
      #inward detail entry
      InwardDetail.import inward_to_be_imported, validate: false, on_duplicate_key_update: {conflict_target: [:line_item_id], columns: [:mark_inscanned, :mark_inscanned_by]}
      #Add to scans table
      LineItem.bulk_add_into_scan('LineItem', sor_line_item_ids, ['RTO inscanned from RTO panel'], current_account.id)
      filename = 'packages/rto_inscan/'+ "#{Time.current.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
      AwsOperations.create_aws_file(filename,file)
      file_url = AwsOperations.get_aws_file_path(filename)
      emails = {'to_email'=> current_account.email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email' => ACCESSIBLE_EMAIL_ID['bag_report_cc_emails']}
      attachments = {"RTO Inscan Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}.csv"=> File.read(file.path)}
      OrderMailer.report_mailer("RTO Inscan Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.", emails, attachments).deliver
    end
  end

  def self.mail_stitching_searching_data(w_stylist_search, start_date, end_date, email, searching_type)
    searching_data = stitching_searching_data(w_stylist_search, start_date, end_date, searching_type, true)
    file = CSV.generate do |csv|
      csv << ['Order Number','Days Passed', 'State', 'Product ID', 'Product Type', 'Bag Number', 'Rack Code', 'Rack Number', 'Bag Created Date', 'Received Date', 'Stylist', 'Stitching Type', 'Measurement States', 'Package Type', 'Saree Type', 'Stylist Intervention']
      searching_data.each do |data|
        measurements = data.stitching_measurements.to_a
        next if searching_type == 'stitching' && measurements.any?{|sm| (sm.chest_size.to_s.length > 4 ? sm.chest_size.scan(/\d+/).map(&:to_f).sum : sm.chest_size.to_f) > 42}
        designer_order = data.designer_order
        order = designer_order.order
        stylist = order.stylist
        prestitch_saree_size = data.get_prestitch_saree_size
        no_stylist_intervention = data.item_details.values_at('no_stylist_intervention', 'std_auto_approve').compact.present?
        csv << [order.number, ((Time.current - order.confirmed_at)/1.day).floor, order.state, data.design_id, data.design.try(:designable_type), designer_order.inward_bag.try(:number), designer_order.rack_code, designer_order.try(:rack_list).try(:code), designer_order.package_received_on.try(:strftime, "%d-%b-%Y %I:%M %p"), data.received_on.try(:strftime, "%d-%b-%Y %I:%M %p"), stylist.try(:name), (measurements.any?{|mes| mes.code.present?} ? 'Standard' : 'Custom'), measurements.map(&:state).uniq.join(', '), (measurements.blank? ? 'No Measurements Added' : (data.issue_resolved_at.present? ? 'Fresh Replacement Product' : '')), prestitch_saree_size, (no_stylist_intervention ? 'No' : 'Yes')]
      end
    end
    emails = {'to_email'=> email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("#{searching_type.titleize} Searching CSV File #{Time.current.strftime('%d-%b-%Y %I:%M %p')}",'Please Find Attachment.',emails,{"#{searching_type.titleize} Searching #{Time.current.strftime('%d-%b-%Y %I:%M %p')}.csv"=> file} ).deliver
  end

  def self.stitching_searching_data(w_stylist_search, start_date, end_date, searching_type, batching=false)
    all_data, join_addon_array, scan_join_string, addon_having_clause = [], [], '', ''
    w_plus_size_check = ''
    join_addon_array = [:design, line_item_addons: :addon_type_value]
    select_clause = 'line_items.*, designs.designable_type'
    if searching_type == 'FNP'            
      w_stylist_search = ['designs.designable_type = ?', 'Saree']
      scan_join_string = "left outer join scans sc on sc.line_item_id = line_items.id and lower(sc.scan_type) = 'handover to fnp'"
      addon_having_clause = "and string_agg(addon_type_values.name::text, ',') not like '%Blouse Stitching%' AND (string_agg(addon_type_values.name::text, ',') like '%Petticoat Stitching%' OR string_agg(addon_type_values.name::text, ',') not like '%No Fall and Pico%')"
    else      
      scan_join_string = "left outer join scans sc on sc.line_item_id = line_items.id and lower(sc.scan_type) = 'handover to stitching'"
      addon_having_clause = "and (designs.designable_type != 'Saree' or (position('Blouse Stitching' in string_agg(addon_type_values.name,',')) <> 0 or (position('No Fall and Pico' in string_agg(addon_type_values.name,',')) <> 0 and position('No Petticoat' in string_agg(addon_type_values.name,',')) <> 0)))"
      if searching_type == 'Plus Size'
        join_addon_array = [:design, :stitching_measurements, line_item_addons: :addon_type_value]
        w_plus_size_check = ["cast(substring(chest_size from '[0-9]+') as double precision) > ?", 42]
      end
    end
    if batching 
      LineItem.select(select_clause).joins(scan_join_string).joins(order: :tags).joins(join_addon_array).preload(:design, :stitching_measurements, designer_order: [:rack_list, :inward_bag, order: :stylist], line_item_addons: :addon_type_value).where('stitching_required = ? and orders.state not in (?) and line_items.received_on is not null and (line_items.issue_status is null or line_items.issue_status = ?) and designer_orders.state not in (?) and (stitching_sent_on is null or (stitching_sent_on is not null and issue_status = ?)) and stitching_done_on is null and  line_items.status is null and orders.confirmed_at between ? and ?', 'Y', ['cancel', 'dispatched'], 'N', ['canceled', 'buyer_returned'], 'N', start_date.beginning_of_day, end_date.end_of_day).where(w_stylist_search).where(w_plus_size_check).group('line_items.id, designs.designable_type').having("(max(sc.id) is null or (max(sc.scanned_at) < issue_resolved_at and max(sc.scanned_at) < issue_created_at)) and position('addon' in string_agg(tags.name,',')) = 0" + addon_having_clause).find_in_batches(batch_size: 50) do |data_batch|
        all_data += data_batch
      end
      return all_data
    else
      LineItem.select(select_clause).joins(scan_join_string).joins(order: :tags).joins(join_addon_array).preload(:design, :stitching_measurements, designer_order: [:rack_list, :inward_bag, order: :stylist], line_item_addons: :addon_type_value).where('stitching_required = ? and orders.state not in (?) and line_items.received_on is not null and (stitching_sent_on is null or (stitching_sent_on is not null and issue_status = ?)) and stitching_done_on is null and (line_items.issue_status is null or line_items.issue_status = ?) and designer_orders.state not in (?) and line_items.status is null and orders.confirmed_at between ? and ?', 'Y', ['cancel', 'dispatched'], 'N', 'N', ['canceled', 'buyer_returned'], start_date.beginning_of_day, end_date.end_of_day).where(w_stylist_search).where(w_plus_size_check).group('line_items.id, designs.designable_type').having("(max(sc.id) is null or (max(sc.scanned_at) < issue_resolved_at and max(sc.scanned_at) < issue_created_at)) and position('addon' in string_agg(tags.name,',')) = 0" + addon_having_clause)
    end
  end

  def self.generate_tailoring_inscan_bags(tailor_details, all_item_wise_data, current_account, inscan_type, barcodes_data)
    tailoring_bag, inscanned_item_ids, failed_item_ids, bag_relation_to_be_imported, successful_count, material_counts, product_material = nil, [], {}, [], 0, Hash.new(0), {}
    scanned_barcodes, successful_inscanned_tailoring_info_ids = {}, []    
    inscanned_tailor_metric, inscannned_tailoring_info_ids = Hash.new, []
    alteration_bag_ids, join_array, is_warehouse = [], [], inscan_type == 'Warehouse'
    w_clause = 'tailoring_infos.tailor_id = ?', tailor_details[1]
    total_scanned_count = all_item_wise_data.values.flatten.uniq.count
    if inscan_type == 'alteration'
      join_array = [:alteration_batches]
      w_clause = "tailoring_batches.tailor_id = #{tailor_details[1]} and tailoring_bag_relations.state = 'alteration assigned'"
    elsif inscan_type == 'fnp'
      join_array = [:order, :line_item]
    end
    if all_item_wise_data.present?
      all_to_be_inscanned, scanned_barcodes = get_tailoring_infos_to_be_inscanned(all_item_wise_data, join_array, w_clause, inscan_type, barcodes_data)
      all_to_be_inscanned.each do |t_info|
        current_inscanned = t_info.tailoring_bag_relations.to_a.sum{|bag| bag.inscanned_count.to_i } if is_warehouse
        if ((assigned_bag = t_info.assigned_bag).present? || t_info.tailoring_bag_relations.blank? || (is_warehouse && t_info.tailoring_bag_relations.present? && current_inscanned < t_info.line_item_quantity)) && !t_info.material_received_status
          tailoring_bag = generate_tailoring_bag(tailor_details[0]) if tailoring_bag.blank?
          assigned_bag = t_info.tailoring_bag_relations.new if assigned_bag.blank?
          in_scan_cnt = if is_warehouse
            now_in_scan = all_item_wise_data[t_info.item_id].to_a.uniq.count
            ((current_inscanned + now_in_scan <= t_info.line_item_quantity) ? now_in_scan : (t_info.line_item_quantity - current_inscanned))
          else
            scanned_barcodes[t_info.item_id].to_a.uniq.count
          end
          assigned_bag.assign_attributes(tailoring_inscan_bag_id: tailoring_bag.id, state: 'inscanned', assigned_count: t_info.line_item_quantity, inscanned_count: in_scan_cnt)
          bag_relation_to_be_imported << assigned_bag
          inscanned_item_ids << t_info.item_id
          if AUTO_TAILOR_ASSIGN['enable'] && !is_warehouse && t_info.created_at > Date.parse(AUTO_TAILOR_ASSIGN['started_at'])
            tailor_obj = Tailor.find_by_id(tailor_details[1])
            tailor_obj.set_tailor_daily_metric_hash(t_info.item, inscanned_tailor_metric, t_info.line_item_quantity)
            inscannned_tailoring_info_ids << t_info.id
          end
          if is_warehouse
            material_counts[t_info.tailoring_material] += in_scan_cnt
            (product_material[t_info.item_id] ||= Hash.new(0))[t_info.tailoring_material] += 1
          else
            material_counts[t_info.tailoring_material] += t_info.line_item_quantity.to_i
            (product_material[t_info.item_id] ||= Hash.new(0))[t_info.tailoring_material] += t_info.line_item_quantity.to_i
            alteration_bag_ids << t_info.alteration_batches.to_a.select{|batch| batch.open?}.map(&:id)
          end

          if tailoring_bag.created_at - t_info.created_at > TAILOR_DASHBOARD_METRIC['delay_ratio']['threshold_time_in_days'].days
            t_info.order_quality_event(:add, 'TailorInscanDelayed')
          end
          successful_inscanned_tailoring_info_ids << t_info.id
        else
          failed_item_ids[t_info.item_id] = (t_info.material_received_status || is_warehouse ? 'Tailoring Product is already received' : 'Tailor Assigned Bag is not present')
        end
      end
      TailorDailyMetric.update_tailor_daily_metrics_pending_count(inscanned_tailor_metric, inscannned_tailoring_info_ids, :decrement) if inscanned_tailor_metric.keys.present?
      TailoringBagRelation.import bag_relation_to_be_imported, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:tailoring_inscan_bag_id, :state, :assigned_count, :inscanned_count]}
      TailoringInfo.where(id: successful_inscanned_tailoring_info_ids).update_all(latest_inscanned_at: Time.current)
      LineItem.bulk_add_into_scan('LineItem', inscanned_item_ids, ['Tailoring Inscan', "Assigned To Bag Number #{tailoring_bag.try(:number)}"], current_account.id) if inscanned_item_ids.present? && !is_warehouse
      if (bag_ids = alteration_bag_ids.flatten.uniq).present?
        Alteration.update_bag_statues(bag_ids)
      end
      file  = Tempfile.new(['bag_generation','.csv'])
      tailoring_inscan_to_be_imported = []
      CSV.open(file.path, 'wb') do |csv|
        csv << ['Tailor Name', tailor_details[0]]
        csv << ['Inscan Date', (tailoring_bag.try(:created_at).presence || Time.current).try(:strftime, ('%d-%m-%Y %I:%M %p'))]
        2.times do |i|
          csv << []
        end
        csv << ['Material', 'Count']
        material_counts.each do |k,v|
          csv << [k, v]
        end
        3.times do |i|
          csv << []
        end
        csv << ['Barcode', 'Status', 'Bag Number', 'Remark', 'Material Quantity']
        scanned_barcodes.each do |item_id, barcodes|
          is_failed = failed_item_ids.key?(item_id) || inscanned_item_ids.exclude?(item_id)
          successful_count += (is_warehouse ? all_item_wise_data[item_id].to_a.uniq.count : scanned_barcodes[item_id].to_a.uniq.count) unless is_failed
          remark = is_failed ? (failed_item_ids[item_id].presence  || 'Tailor not matched') : ''
          barcodes.each do |code|
            csv << [code, (is_failed ? 'Failed' : 'Success'), (is_failed ? '' : tailoring_bag.try(:number)), remark, product_material[item_id]]
            tailoring_inscan_to_be_imported << TailoringInscan.new(scanned_barcode: code, status: (is_failed ? 'Failed' : 'Success'), tailoring_inscan_bag_id: (is_failed ? nil : tailoring_bag.try(:id)), remark: remark)
          end
        end
      end
      TailoringInscan.import tailoring_inscan_to_be_imported, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:scanned_barcode, :status, :tailoring_inscan_bag_id, :remark]}
      filename = 'packages/tailoring_bag_inscan/'+ "#{Time.current.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
      AwsOperations.create_aws_file(filename,file)
      file_url = AwsOperations.get_aws_file_path(filename)
      to_email_array = [current_account.email, tailor_details[2]].compact
      emails = {'to_email'=> to_email_array, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email' => ACCESSIBLE_EMAIL_ID['bag_report_cc_emails']}
      tailoring_bag.update_columns(report_url: file_url, inscanned_count: total_scanned_count, successful_count: successful_count, created_by: current_account.name) if tailoring_bag.present?
      attachments = {"Tailoring Inscan Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}.csv"=> File.read(file.path)}
      if tailoring_bag.present?
        bag_label_pdf_content = "<div style='margin-top:25%;font-size: 25pt;border: 3px solid black;'><center><h1>#{tailoring_bag.number}</h1></center></div>"
        bag_label = WickedPdf.new.pdf_from_string(bag_label_pdf_content,{orientation: 'Landscape'})
        attachments['Tailoring Inscan Bag Label.pdf'] = bag_label
      end
      OrderMailer.report_mailer("Tailoring Inscan Bag Generation Report #{Time.current.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.  Total Scanned : #{total_scanned_count}, Successful : #{successful_count}, Failed : #{(total_scanned_count - successful_count).abs}", emails, attachments).deliver
    end
  end

  def self.mark_post_tailoring_scans(input_details, total_barcode_scanned, current_account, hand_to, handover_need, stylist_id)
    successful_item_ids, note_message, w_stylist_check = [], nil, ''
    scan_type_message = "Handover To #{hand_to.titleize}"
    scan_type_message += " For #{handover_need.titleize}" if hand_to == 'stylist' 
    is_stylist_receive_scan = (stylist_id.present? && handover_need == 'receiving' && hand_to == 'stylist')
    emails = {'to_email'=> current_account.email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    if is_stylist_receive_scan || (handover_need == 'working' && hand_to == 'stylist')
      stylist = Stylist.find(stylist_id)
      note_message = scan_type_message.gsub('Stylist', "Stylist (#{stylist.try(:name)})") 
      w_stylist_check = ['orders.stylist_id = ?', stylist_id] if !is_stylist_receive_scan
      emails['cc_email'] = stylist.try(:account).try(:email)
    end
    scan_name = (hand_to == 'stylist' ? "#{hand_to}-#{handover_need}" : hand_to)
    input_details.each do |key,val|
      LineItem.joins(:order).preload(:latest_stitching_handover_scan, :latest_stitching_handover_receive_scan, :order, tailoring_info: :last_tailoring_bag_rel).where(design_id:val).where('stitching_required = ? and orders.state NOT IN (?) AND status IS NULL AND orders.number = ?', 'Y', ['cancel','new'],key).where(w_stylist_check).each do |item|
        scan_status, status_message = check_scan_status(item, scan_name)
        order = item.order
        if scan_status
          note_element = (hand_to == 'stylist' ? order : item.designer_order)
          item.add_into_scan(scan_type_message, current_account.id)
          note_element.add_notes_without_callback("Design #{item.design_id} #{note_message.presence || scan_type_message} ", 'stitching', current_account)
          (total_barcode_scanned["#{order.number}-#{item.design_id}"] ||= []) << ['Successful', '']
          successful_item_ids << item.id
        else
          (total_barcode_scanned["#{order.number}-#{item.design_id}"] ||= []) << ['Failed', status_message]
        end
      end
    end
    if is_stylist_receive_scan && successful_item_ids.present?
      StylistReceive.create(stylist_id: stylist_id, line_item_ids: successful_item_ids)
    end
    file = CSV.generate do |csv|
      csv << ['Scanned Barcode', 'Status', 'Remark']
      total_barcode_scanned.each do |key, val| 
        csv << [val[0], (val[1].try(:[], 0).presence || 'Failed'), (val[1].try(:[], 0) == 'Successful' ? '' : (val[1].try(:[], 1).presence || 'Stylist Mismatch Or In Wrong State'))]        
      end
    end
    emails = {'to_email'=> current_account.email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("#{note_message} Report #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}",'Please Find Attachment.',emails,{"#{note_message} Report #{Time.current.strftime('%d-%b-%Y %I:%M %p')}.csv"=> file}).deliver
  end

  def self.send_open_inward_bags_details(start_date, end_date)    
    packed_package_count = 0
    file = CSV.generate do |csv|
      csv << ['Bag Number', 'Order Number', 'Tracking Number', 'Recent Tracking Number']
      InwardBag.preload(packed_designer_orders: :payment_order).
      where('created_at between ? and ? and status = ?', start_date, end_date, 'open').
      find_each(batch_size: 50) do |ibag|       
        ibag.packed_designer_orders.each do |des_order|
          packed_package_count += 1
          csv << [ibag.number, des_order.payment_order.try(:number), des_order.tracking_num, des_order.recent_tracking_number]
        end
      end
    end
    ShipmentMailer.mail_open_inward_bags(packed_package_count, file).deliver_now
  end

  def self.mail_followup_orders(followup_where_claues, team_filter, process_filter, preload_array, to_email)
    found_orders = get_followup_orders(followup_where_claues, preload_array, true)
    file = CSV.generate do |csv|
      csv << ['Order Number', 'Country', 'State', 'Confirmed At', 'Promised Delivery Date', 'Product ID', 'Team', 'Process']
      found_orders.each do |order|
        tag_names = order.try(:tags).to_a.map(&:name).reject{|tag| tag.include?('convert-mkt')} 
        is_addon_order = tag_names.include?('addon')
        line_items = order.line_items.select{|item| item.status.blank? || ['canceled', 'vendor_canceled'].exclude?(item.designer_order.state)}
        promised_date = order.delivery_nps_info.try(:promised_delivery_date).try(:strftime, '%d-%b-%Y %I:%M %p')
        line_items.each do |item|          
          process, team = (is_addon_order ? ['Addon Tag Present', 'Support'] : item.get_item_status)
          next if (process_filter.present? && process != process_filter) || (team_filter.present? && team != team_filter)
          csv << [order.number, order.country, order.state, order.confirmed_at.strftime('%d-%b-%Y %I:%M %p'), promised_date, item.design_id, team, process]
        end
      end
    end
    emails = {'to_email'=> to_email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
    OrderMailer.report_mailer("Order Followup Report #{Time.current.strftime('%d-%b-%Y %I:%M %p')}", 'Please Find Attachment.', emails, {"Order Followup Report #{Time.current.strftime('%d-%b-%Y %I:%M %p')}.csv" => file} ).deliver_now
  end

  def self.get_followup_orders(followup_where_claues, preload_array, mail_report=false)    
    @followup_where_claues = followup_where_claues
    @preload_array = preload_array
    if mail_report
      found_orders = []
      get_followup_query.find_in_batches(batch_size: 100) do |order_batch|
        found_orders += order_batch
      end
      found_orders
    else
      get_followup_query
    end
  end

  def self.mail_csv_uploaded_orders_status(filename, preload_array, to_email)
    bucket = (Rails.env.production? || Rails.env.admin? ) ? 'mirraw' : 'mirraw-test'
    full_path = 'https://s3-ap-southeast-1.amazonaws.com/' + bucket + '/' + filename
    order_numbers = Array.new    
    CSV.new(open(full_path), {headers: true, header_converters: :symbol}).each do |line|
      order_numbers << line[:order_number]
    end
    followup_where_claues = get_where_clauses_for_followup_orders({csv_orders: order_numbers}) 
    mail_followup_orders(followup_where_claues, nil, nil, preload_array, to_email)
  end

  def self.get_where_clauses_for_followup_orders(input_params, start_date=nil, end_date=nil)
    followup_where_claues = Hash.new    
    searched_order_numbers = (input_params[:order_number].to_s.presence || input_params[:csv_orders].to_s).split(',')
    if searched_order_numbers.present?
      followup_where_claues[:number_search] = ['number in (?)', searched_order_numbers]       
    else
      followup_where_claues[:date_filter] = ['confirmed_at between ? and ?', start_date.beginning_of_day, end_date.end_of_day]      
      followup_where_claues[:state_search] = ['orders.state = ?', 'sane']
    end
    if input_params[:stitching_select].present?
      followup_where_claues[:stitching_check] = if input_params[:stitching_select] == 'Stitching' 
        ['orders.other_details @> hstore(?,?)','stitching_order','true']
      else
        "not orders.other_details ? 'stitching_order' or orders.other_details @> 'stitching_order => false'"
      end
    end
    followup_where_claues
  end

  def self.update_searcher_buckets
    all_searcher_buckets = SearcherBucket.all
    SystemConstant.get_picking_worker_config.each do |process, workers_count|
      process_buckets = all_searcher_buckets.select{|sb| sb.kind == process}
      update_process_buckets_as_per_workers_count(process_buckets, workers_count, process)
    end
  end

  class << self
    private

    def update_process_buckets_as_per_workers_count(buckets, workers, process)
      active_buckets = buckets.select{|b| b.active}
      worker_change_size =  (workers - active_buckets.size).abs
      if active_buckets.size < workers
        SearcherBucket.segregate_searcher_process_buckets(buckets, worker_change_size, process)
      elsif active_buckets.size > workers
        SearcherBucket.merge_searcher_process_buckets(buckets, worker_change_size)
      end
    end

    def check_for_rto_cases(awbs, account)
      rto_line_item_ids, rto_inscan_awbs, buyer_return_awbs = [], [], []
      Shipment.preload(:order, :line_items, designer_order: :line_items).where(created_at:6.months.ago..DateTime.now).where('number in (?) and order_id is not null and service_type <> ? and (designer_order_id is null or shipment_type = ?) and shipment_state <> ?', awbs, 'ReverseShipment', 'COD', 'processing_abandoned').find_each(batch_size: 50) do |shipment|
        shipment.order.add_notes_without_callback("RTO Shipment Inscanned With Tracking #{shipment.number}", 'RTO Inscan', account)
        rto_line_item_ids << (shipment.shipment_type == 'COD' ? shipment.designer_order.line_items.map(&:id) : shipment.line_items.map(&:id))
        rto_inscan_awbs << shipment.number
      end            
      if (remaining_awbs = (awbs - rto_inscan_awbs)).present?
        buyer_return_awbs = check_for_buyer_return_cases(remaining_awbs, rto_inscan_awbs, account) 
      end
      LineItem.bulk_add_into_scan('LineItem', rto_line_item_ids.flatten.compact.uniq, ['RTO Inscan'], account.id)
      return rto_inscan_awbs.map(&:downcase), buyer_return_awbs
    end

    def check_for_buyer_return_cases(awbs, found_awbs, account)      
      buyer_return_item_ids, buyer_return_found_awbs = [], []
      ReturnDesignerOrder.preload(:order, :line_items).where(tracking_number: awbs).find_in_batches(batch_size: 100) do |rdo_batch|
        rdo_batch.each do |rdo|
          rdo.order.add_notes_without_callback(
            "Buyer Return Shipment with Products #{rdo.line_items.map(&:design_id).join(', ')} Inscanned With Tracking #{rdo.tracking_number}", 
            'Buyer Return Inscan', 
            account
          ) if rdo.order.present?
          buyer_return_item_ids << rdo.line_items.map(&:id)
          found_awbs << rdo.tracking_number
          buyer_return_found_awbs << rdo.tracking_number
        end
      end
      LineItem.bulk_add_into_scan('LineItem', buyer_return_item_ids.flatten.compact.uniq, ['Buyer Return Inscan'], account.id)
      buyer_return_found_awbs
    end

    def mark_designer_order_package_inscanned(des_orders, bag, system_awb_numbers, inscanned_ids, done_by_name, purchase_order, exact_found_nums=[], inscan_by_id=false)
      to_be_imported, stitching_count = [], 0
      inscanned_ids << des_orders.map(&:id)          
      des_orders.each do |des_o| 
        line_items = des_o.line_items
        is_replacement_case = line_items.any?{|item| item.qc_status == false}             
        found_awb_number = if inscan_by_id 
            (is_replacement_case ? des_o.recent_tracking_number : des_o.tracking_num)
          else
            (exact_found_nums & [des_o.tracking_num.try(:downcase), des_o.recent_tracking_number.try(:downcase)]).first
          end
        ((system_awb_numbers[found_awb_number] ||= {})['matched_awb'] ||= []) << found_awb_number
        system_awb_numbers[found_awb_number]['critical_remark'] = des_o.critical? ? 'Contains Crtitcal Order' : ''
        stitching_count += 1 if line_items.any?{|item| item.status.blank? && item.stitching_required == 'Y'}
        des_o.assign_attributes(inward_bag_id: bag.id, package_received_on: Time.current, package_received_by: done_by_name, package_status: (is_replacement_case ? 'replacement': 'inscanned'), purchase_order_id: purchase_order)                
        to_be_imported << des_o
      end
      [to_be_imported, stitching_count]
    end

    def get_followup_query
      Order.preload(@preload_array).where('country <> ?', 'India').where(@followup_where_claues[:date_filter]).where(@followup_where_claues[:number_search]).where(@followup_where_claues[:state_search]).where(@followup_where_claues[:stitching_check])
    end

    def check_scan_status(item, scan_name)
      case scan_name
      when 'stylist-working'
        status = (item.latest_stitching_handover_scan.present? && item.latest_stitching_handover_receive_scan.present?)
        [status, (status ? 'successful' : 'handover or receive from warehouse scan missing.')]
      when 'stylist-receiving'
        status = item.tailoring_info.find{|info| ['Saree', 'FNP'].exclude?(info.tailoring_material) && !info.material_received_status && info.last_tailoring_bag_rel.try(:state) == 'inscanned'}.present?
        [status, (status ? 'successful' : 'Product is not yet inscanned.')]
      when 'ironing'
        status = item.tailoring_info.find{|info| ['Saree', 'FNP'].exclude?(info.tailoring_material) && info.material_received_status}.present?
        [status, (status ? 'successful' : 'Product is not yet marked as received.')]
      else
        [false, 'Wrong Scan Name.']
      end
    end

    def get_tailoring_inscan_report(failed_products, all_scanned_products, to_email, material_counts, tailor_name, product_material)
      file = CSV.generate do |csv|
        csv << ['Tailor Name', tailor_name]
        csv << ['Inscan Date', DateTime.now.strftime('%d-%m-%Y %I:%M %p')]
        2.times do |i|
          csv << []
        end
        csv << ['Material', 'Count']
        material_counts.each do |k,v|
          csv << [k, v]
        end
        3.times do |i|
          csv << []
        end
        csv << ['Barcode', 'Status', 'Remark', 'Material', 'Quantity']
        all_scanned_products.each{|id, data| csv << [data, ((is_failed = failed_products.include?(id)) ? 'NO' : 'YES'), (is_failed ? 'Tailor Not Matched.' : ''), product_material[id.to_i].try(:[], 0), product_material[id.to_i].try(:[], 1)]}
      end
      emails = {'to_email'=> to_email, 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
      OrderMailer.report_mailer("Tailoring Inscan Report #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}", "Please Find Attachment.  Total Scanned : #{all_scanned_products.count}, Successful : #{(all_scanned_products.count - failed_products.count).abs}, Failed : #{failed_products.count} ", emails, {"Tailoring Inscan Report #{DateTime.now.strftime('%d-%m-%Y %I:%M %p')}.csv"=> file}).deliver
    end

    def generate_inward_bag_entry(purchase_order=false)
      start_time = Date.today.beginning_of_day
      end_time = Date.today.end_of_day
      next_bag_count = InwardBag.where('created_at BETWEEN  ? AND ?',start_time,end_time).pluck(:number).count + 1
      bag_initial = (purchase_order ? 'PO' : 'IB')
      new_bag_number = "#{bag_initial}#{Date.today.strftime('%d-%m-%y')}_#{next_bag_count}"
      InwardBag.where(number: new_bag_number).first_or_create
    end

    def generate_tailoring_bag(tailor_name)
      start_time = Date.today.beginning_of_day
      end_time = Date.today.end_of_day
      next_bag_count = TailoringInscanBag.where('created_at BETWEEN  ? AND ?',start_time,end_time).pluck(:number).count + 1
      new_bag_number = "TIB_#{tailor_name.split[0]}_#{Date.today.strftime("%d-%m-%y")}_#{next_bag_count}"
      TailoringInscanBag.where(number: new_bag_number).first_or_create
    end

    def get_tailoring_infos_to_be_inscanned(input_data, join_array, w_extra_cond, inscan_type, scanned_barcodes)
      to_be_inscanned, barcode_data = [], {}
      if inscan_type == 'Warehouse'
        TailoringInfo.preload(:item, :assigned_bag, :tailoring_bag_relations).where(item_id: input_data.keys, item_type: 'WarehouseSizeItem').find_in_batches(batch_size: 100) do |t_info_batch|
          to_be_inscanned += t_info_batch
        end
        barcode_data = scanned_barcodes
      elsif inscan_type != 'fnp'
        preload_array = [:assigned_bag, :alteration_batches, :tailoring_bag_relations] 
        preload_array << [item: Tailor.get_auto_tailor_assign_item_preload_array] if AUTO_TAILOR_ASSIGN['enable'] && inscan_type == 'tailoring'
        TailoringInfo.only_line_items.preload(preload_array).joins(join_array).where(item_id: input_data.keys, item_type: 'LineItem').where(w_extra_cond).find_in_batches(batch_size: 100) do |t_info_batch|
          to_be_inscanned += t_info_batch
        end
        barcode_data = input_data
      else
        input_data.each do |order_num, design_ids|
          TailoringInfo.only_line_items.preload(:item, :assigned_bag, :alteration_batches, :tailoring_bag_relations).joins(join_array).where('line_items.design_id in (?) and orders.number = ?', design_ids, order_num).where(w_extra_cond).each do |t_info|
            to_be_inscanned << t_info
            barcode_data[t_info.item_id] = (scanned_barcodes["#{order_num}-#{t_info.item.design_id}"])
          end
        end
      end
      return to_be_inscanned, barcode_data
    end

    def get_scan_type_message(receiving_param, handover_for, scan_action, handover_to)
      scan_type_message = if receiving_param
        get_receiving_scan_message(scan_action, handover_for)      
      else
        scan_action == 'receive' ? "Handover Received By Warehouse From #{handover_to}" : "Handover To #{handover_to}"
      end
    end

    def get_receiving_scan_message(scan_action, handover_for)
      case scan_action
      when 'assign'
        get_handover_to_warehouse_scan_message(handover_for)      
      when 'stylist_receive'
        "#{handover_for} Handover Received From Stylist"
      else
        'Handover Received By Stylist'
      end
    end

    def get_handover_to_warehouse_scan_message(handover_for)
      case handover_for
      when 'FNP'
        'Handover To Warehouse From FNP' 
      when 'dispatch'
        'Handover To Warehouse'
      else
        "Handover To Warehouse For #{handover_for}"
      end
    end
  end        

end