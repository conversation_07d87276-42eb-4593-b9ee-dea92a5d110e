class AdditionalPayment < ActiveRecord::Base
  # attr_accessible :title, :body
  has_many :line_item_addons
  belongs_to :order
  has_paper_trail

  def add_custom_addons_to_order
    addon = self.line_item_addons.first
    order = self.order
    if order.notes.exclude?("Custom Addons added to #{addon.line_item.design_id}")
      addon.notes          = "#{addon.notes} #{self.notes}"
      addon.snapshot_price+= self.total
      order.paid_amount   += self.total
      addon.save!
      if self.charge_type == 'custom_addon' && (measurement = addon.line_item.stitching_measurements.first).present?
        measurement.add_padding_cancan(addon.line_item)
        addon.line_item.stitching_measurements.update_all(padded: measurement.padded, cancan: measurement.cancan)
      end
      order.add_notes_without_callback("Custom Addons added to #{addon.line_item.design_id}", 'payment') if order.save!
      # order.add_notes_without_callback('SUCCESS automated addon payment','payment')
    end
  end

  def get_paypal_rate
    currency_record = CurrencyConvert.currency_convert_memcached.find{|cc| cc.country_code == self.country_code}
    rate = currency_record.rate
    paypal_rate = currency_record.try(:paypal_rate)
    paypal_rate.nil? ? self.currency_rate : (rate / paypal_rate)
  end

  state_machine :payment_state, initial: :new do
    event :addon_payment_complete do
      transition all - [:paid] => :paid
    end

    state :added_to_order

    after_transition on: :addon_payment_complete, do: :add_custom_addons_to_order
  end
end
