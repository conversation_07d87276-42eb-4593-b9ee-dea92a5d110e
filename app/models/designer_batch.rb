class DesignerBatch < ActiveRecord::Base
  belongs_to :designer
  has_many :failed_designs
  has_many :version_failed_designs
  has_many :delay_images, through: :designs
  has_many :delay_designables, through: :designs
  has_many :delay_design_groups
  has_many :designs
  #attr_accessible :failed, :filename, :no_of_records, :passed

  extend DynamicTemplateMethod
  dynamic_template_fields :no_of_records ,:passed ,:failed ,:created_at ,:updated_at ,:version 

  def bulk_upload_post_processing
    delay_designables.includes(design: [:categories, :variants, :images]).run_all
    delay_design_groups.includes(:design).run_all
    delay_images.includes(:design).run_all
  end

  def to_csv(opts = {})
    structure = csv_structure
    designable_type = bulk_meta_data[:extra][:designable].to_s.to_sym
    product_type = bulk_meta_data[:extra][:product_type]
    csv = []
    csv << csv_head(structure, designable_type)
    designs.where(opts).preload(:images, :categories, :designable, [property_values: :property], [variants: :option_type_values]).each do |design|
      data_hash = {}
      csv_key_order.each do |key|
        data_hash[key] = Array.new(structure[key].size)
      end
      structure[:design].each_with_index do |attribute, index|
        data_hash[:design][index] = design[attribute]
      end
      data_hash[:category][0] = design.categories.first.try(:name)
      data_hash[:product_type][0] = product_type
      design.images.each_with_index do |image, index|
        data_hash[:image][index] = image.photo.url
      end
      structure[:designable].each_with_index do |attribute, index|
        data_hash[:designable][index] = design.designable.try(:[],attribute)
      end
      property_hash = {}
      design.property_values.each do |property_value|
        (property_hash[property_value.property_name] ||= []).push(property_value.name)
      end
      property_hash.default = []
      structure[:property].each_with_index do |property_name, index|
        data_hash[:property][index] = property_hash[property_name].join('+')
      end
      variant_hash = {}
      design.variants.each do |variant|
        variant_hash[variant.option_type_values.first.try(:id)] = variant.quantity
      end
      structure[:variant].each_with_index do |head, index|
        data_hash[:variant][index] = variant_hash[variant_mapping[head]]
      end
      csv << rearrange_data(data_hash)
    end
    csv
  end

  private

  def variant_mapping
    @variant_mapping ||= Util.flatten_hash(
      OptionType.get_option_type_values(bulk_meta_data[:variants].to_a)
    )
  end

  def csv_structure
    variants = variant_mapping
    row_structure = {
      design: %w(id design_code title embellish description package_details price discount_percent weight tag_list pattern region quantity hsn_code gst_rate),
      category: ['category'],
      product_type: ['product_type'],
      image: %w(image image1 image2 image3 image4),
      property: bulk_meta_data[:designs_property_values].to_a.map!(&:to_s),
      variant: variants.keys
    }
    exclude_header = row_structure.values.flatten + %w(created_at updated_at)
    row_structure[:designable] = bulk_meta_data[:extra][:designable].constantize.column_names - exclude_header
    row_structure
  end

  def bulk_meta_data
    @bulk_meta_data ||= BulkUpload.meta_data[version.to_sym]
  end

  def csv_head(structure, designable_type)
    head = []
    key_order = csv_key_order
    custom_mapping = csv_custom_mapping
    key_order.each do |key|
      custom_key = (key == :designable ? designable_type : key)
      if custom_mapping[custom_key].present?
        structure[key].each do |col_name|
          head.push(custom_mapping[custom_key][col_name.to_sym] || col_name)
        end
      else
        head.concat(structure[key])
      end
    end
    head
  end

  def csv_key_order
    @key_order ||= [:design, :category, :image, :property, :variant, :designable, :product_type]
  end

  def rearrange_data(data_hash)
    data = []
    csv_key_order.each do |key|
      data.concat(data_hash[key])
    end
    data
  end

  def csv_custom_mapping
    @custom_mapping ||= {
      design: {
        id: 'product_id',
        weight: 'weight_in_gms'
      },
      Jewellery:
      { earings_width: 'earrings_width_in_mm',
        earings_height: 'earrings_height_in_mm',
        width: 'width_in_cm',
        height: 'height_in_cm' },
      Saree:
      { blouse_available: 'blouse_availability',
        blouse_size: 'size_of_blouse_in_cms',
        petticoat_available: 'petticoat_availability',
        petticoat_size: 'size_of_petticoat_metres',
        width: 'width_of_saree_in_inches',
        length: 'length_of_saree_in_metres',
        blouse_image: 'blouse_as_shown_in_the_image' },
      Lehenga:
      { blouse_available: 'blouse_availability',
        blouse_size: 'size_of_blouse_in_cms',
        blouse_image: 'blouse_as_shown_in_the_image',
        length: 'lehenga_length',
        model_size: 'model_size_wearing',
        min_waist_size: 'minimum_waist_size',
        max_waist_size: 'maximum_waist_size',
        min_hip_size: 'minimum_hip_size',
        max_hip_size: 'maximum_hip_size',
        lining_size: 'size_of_lining_in_metres' },
      SalwarKameez:
      { max_kameez_length: 'maximum_length_of_kameez_in_inches',
        max_bottom_length: 'maximum_length_of_bottom_in_metres',
        kameez_length: 'kameez_fabric_length',
        bottom_length: 'length_bottom',
        min_bustsize_fit: 'minimum_bustsize_fit',
        max_bustsize_fit: 'maximum_bustsize_fit',
        max_dupatta_length: 'maximum_length_of_dupatta_in_metres',
        lining_length: 'length_of_lining_in_metres' },
      Bag: { length: 'length_bag', width: 'width_bag' },
      Kurti: { length: 'kurti_length' },
      Kurta: { chunari_length: 'kurta_pyjama_chunari_length' },
      Dupatta: { weight: 'weight_in_gms' }
    }
  end
end
