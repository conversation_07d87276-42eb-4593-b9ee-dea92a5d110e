- is_lehenga_mes = %w(blouse lehenga_choli).include?(@stitching_measurement.product_designable_type)
- is_user_view = hide_top_bottom.present?
-if is_user_view
  %tr
    %td
      Order No:
      = @stitching_measurement.order.number
    %td
      Product Id:
      = @stitching_measurement.design_id
-elsif custom
  %tr
    %td Age
    %td= "#{@stitching_measurement.age} years"
  %tr
    %td Weight 
    %td= "#{@stitching_measurement.weight} Kg"
  %tr
    - height = @stitching_measurement.height.to_s.split('.')
    %td Height 
    %td= "#{height[0]} feets #{height[1].to_i} inches"
  %tr
    %td{colspan: "2"}
      TOP
%tr
  %td{width: '55%'}
    Measurement Type:
  %td{width: '30%'}
    = is_user_view ? @stitching_measurement.measurement_type : wicked_pdf_image_tag("stitching_label_images/#{@stitching_measurement.measurement_type.to_s.parameterize('_')}.png", width: '100%', class: 'measurement_names')

- if !is_user_view && prestitched_saree_size.present?
  %tr
    %td= 'Pre-Stitched Saree :'
    %td{style: 'padding: 5px;'}
      = prestitched_saree_size['Waist']
      Inch

- if !is_user_view || @stitching_measurement.length.present?
  %tr
    %td
      = is_user_view ? 'Length :' : wicked_pdf_image_tag('stitching_label_images/length.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.length
      Inch

- if !is_user_view || @stitching_measurement.chest_size.present?
  %tr
    %td
      = is_user_view ? 'Bust Size :' : wicked_pdf_image_tag('stitching_label_images/bust_size.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.chest_size
      Inch
- if !(fabric_color = @stitching_measurement.line_item.get_addon_option_value_from_notes('fabric color')).blank?
  %tr
    %td
      = !is_user_view ? wicked_pdf_image_tag('stitching_label_images/fabric_color.png', width: '100%', class: 'measurement_names') : 'Fabric Color'
    %td
      = fabric_color

-if ['blouse', 'lehenga_choli'].exclude?(@stitching_measurement.product_designable_type)
  - if !is_user_view || @stitching_measurement.waist_size.present?
    %tr
      %td
        = is_user_view ? 'Natural Waist Size :' : wicked_pdf_image_tag('stitching_label_images/natural_waist_size.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.waist_size
        Inch

  - if !is_user_view || @stitching_measurement.hip_size.present?
    %tr
      %td
        = is_user_view ? 'Hip :' : wicked_pdf_image_tag('stitching_label_images/hip.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.hip_size
        Inch

- if !is_user_view || @stitching_measurement.under_bust.present?
  %tr
    %td    
      = is_user_view ? 'Under Bust :' : wicked_pdf_image_tag('stitching_label_images/under_bust.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.under_bust
      Inch

-if is_lehenga_mes
  - if !is_user_view || @stitching_measurement.shoulder_to_apex.present?
    %tr
      %td
        = is_user_view ? 'Shoulder To Apex Point :' : wicked_pdf_image_tag('stitching_label_images/shoulder_to_apex_point.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.shoulder_to_apex
        Inch

- if !is_user_view || @stitching_measurement.shoulder_size.present?
  %tr
    %td
      = is_user_view ? 'Shoulder :' : wicked_pdf_image_tag('stitching_label_images/shoulder.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.shoulder_size
      Inch

- if !is_user_view || @stitching_measurement.size_around_arm_hole.present?
  %tr
    %td
      = is_user_view ? 'Arm Hole :' : wicked_pdf_image_tag('stitching_label_images/arm_hole.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.size_around_arm_hole
      Inch

- if !is_user_view || @stitching_measurement.sleeves_length.present?
  %tr
    %td
      = is_user_view ? 'Sleeve Length :' : wicked_pdf_image_tag('stitching_label_images/sleeve_length.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.sleeves_length
      Inch

- if !is_user_view || @stitching_measurement.sleeves_around.present?
  %tr
    %td
      = is_user_view ? 'Sleeve around :' : wicked_pdf_image_tag('stitching_label_images/sleeve_around.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.sleeves_around
      Inch

- if !is_user_view || @stitching_measurement.front_neck.present?
  %tr
    %td
      = is_user_view ? 'Front Neck :' : wicked_pdf_image_tag('stitching_label_images/front_neck.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.front_neck
      Inch

- if !is_user_view || @stitching_measurement.back_neck.present?
  %tr
    %td
      = is_user_view ? 'Back Neck :' : wicked_pdf_image_tag('stitching_label_images/back_neck.png', width: '100%', class: 'measurement_names')
    %td
      = @stitching_measurement.back_neck
      Inch

-if is_lehenga_mes || @stitching_measurement.product_designable_type.to_s.include?('anarkali')
  - if !is_user_view || @stitching_measurement.padded.present?
    %tr
      %td
        = is_user_view ? 'Padded :' : wicked_pdf_image_tag('stitching_label_images/padded.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.padded

  -if @stitching_measurement.product_designable_type == 'lehenga_choli'
    - if !is_user_view || @stitching_measurement.cancan.present?
      %tr
        %td
          = is_user_view ? 'Cancan :' : wicked_pdf_image_tag('stitching_label_images/cancan.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.cancan

- if is_lehenga_mes
  - if !is_user_view || @stitching_measurement.hook.present?
    %tr
      %td
        = is_user_view ? 'Hook :' : wicked_pdf_image_tag('stitching_label_images/hook.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.hook

  - if !is_user_view || @stitching_measurement.style_no.present?
    %tr
      %td      
        = is_user_view ? 'Front Style No :' : wicked_pdf_image_tag('stitching_label_images/style_no.png', width: '100%', class: 'measurement_names')
      %td
        -if @style_no_image.present?
          .blouse_image{class: "blouse_types_#{@stitching_measurement.style_no}"}
        -else
          = @stitching_measurement.style_no

  - if !is_user_view || @stitching_measurement.back_style_no.present?
    %tr
      %td Back Style no :
      %td
        -if @style_no_image.present? && @stitching_measurement.back_style_no.present?
          .back_blouse_image{class: "blouse_back_types_#{@stitching_measurement.back_style_no}"}
        -else
          = @stitching_measurement.back_style_no

-unless ['blouse','kurti','islamic'].include?(@stitching_measurement.product_designable_type)
  -unless is_user_view
    %tr
      %td{colspan: "2"}
        BOTTOM
  %tr{rowspan: "2"}
    - select_pant_type = "Bottom"
    - if is_user_view || custom
      - if ['anarkali', 'kameez_chudidar'].include?(@stitching_measurement.product_designable_type)
        - select_pant_type = "Chudidar"
      - elsif ["anarkali_pant","kameez_pant"].include?(@stitching_measurement.product_designable_type)
        - select_pant_type = "Pant"
      - elsif @stitching_measurement.product_designable_type == "kameez_salwar"
        - select_pant_type = "Salwar"
      - elsif @stitching_measurement.product_designable_type == "lehenga_choli"
        - select_pant_type = "Lehenga"

    - if !is_user_view || @stitching_measurement.bottom_length.present?
      %td
        - bottom_name = select_pant_type +" Length"
        = is_user_view ? bottom_name : wicked_pdf_image_tag("stitching_label_images/#{bottom_name.parameterize('_')}.png", width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.bottom_length
        Inch

  - if !is_user_view || @stitching_measurement.denim_waist_size.present?
    %tr
      %td
        = is_user_view ? 'Denim Waist Size :' : wicked_pdf_image_tag('stitching_label_images/denim_waist_size.png', width: '100%', class: 'measurement_names')
      %td
        = @stitching_measurement.denim_waist_size
        Inch

  - if @stitching_measurement.product_designable_type == 'lehenga_choli'
    - if !is_user_view || @stitching_measurement.waist_size.present?
      %tr
        %td
          = is_user_view ? 'Natural Waist Size :' : wicked_pdf_image_tag('stitching_label_images/natural_waist_size.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.waist_size
          Inch

    - if !is_user_view || @stitching_measurement.hip_size.present?
      %tr
        %td
          = is_user_view ? 'Hip :' : wicked_pdf_image_tag('stitching_label_images/hip.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.hip_size
          Inch
  -else
    - if !is_user_view || @stitching_measurement.size_around_thigh.present?
      %tr
        %td
          = is_user_view ? 'Size Around Thigh :' : wicked_pdf_image_tag('stitching_label_images/size_around_thigh.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.size_around_thigh
          Inch

    - if !is_user_view || @stitching_measurement.size_around_knee.present?
      %tr
        %td
          = is_user_view ? 'Size Around Knee :' : wicked_pdf_image_tag('stitching_label_images/size_around_knee.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.size_around_knee
          Inch

    - if !is_user_view || @stitching_measurement.size_around_ankle.present?
      %tr
        %td
          = is_user_view ? 'Size Around Ankle :' : wicked_pdf_image_tag('stitching_label_images/size_around_ankle.png', width: '100%', class: 'measurement_names')
        %td
          = @stitching_measurement.size_around_ankle
          Inch 
%tr
  %td{colspan: "2"}
    ="Notes :- #{@stitching_measurement.stitching_notes}"
-unless is_user_view
  %tr
    %td{colspan: "2"}      
      %img.bar_code{:src=> lower_in_line_barcode_img,style:"margin: 1px 1px 0px 0px;width:98%"}
      %b{style:"margin-left: 52px"}=lower_barcode_number