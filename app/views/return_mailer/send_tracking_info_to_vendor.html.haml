= render :partial => 'header', :locals => {buyer: false}
Buyer has returned following items.
%ul
  - @rdo.line_items.each do |item|
    %li= link_to item.title + " - #" + item.design.id.to_s, designer_design_url(@rdo.designer_id, item.design), :style => "text-decoration:underline;"
    %p= "Return Reason of the Product: #{item.return.reason}" 
    %p Return Images Uploaded by the Customer :
    %div{ style: "display: flex; flex-direction: column; gap: 10px; flex-wrap: wrap; align-items: flex-start;" }
      = image_tag(item.return_image.try(:url), alt: item.return_image_file_name, style: "height: 40%; width: 40%; object-fit: cover;") if item.return_image_file_name.present?
      = image_tag(item.return_image2.try(:url), alt: item.return_image2_file_name, style: "height: 40%; width: 40%; object-fit: cover;") if item.return_image2_file_name.present?
%p= "Items have been shipped with #{@rdo.tracking_company} with tracking details #{@rdo.tracking_number}. "
%p Please let us know when you receive the products.
%p 
  Thank you,
  %br
  Team <PERSON>