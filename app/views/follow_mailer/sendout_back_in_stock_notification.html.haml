%html{xmlns: "http://www.w3.org/1999/xhtml", "xmlns:svg" => "http://www.w3.org/2000/svg"}
  %head
    %meta{content: "text/html; charset=UTF-8", "http-equiv" => "Content-Type"}
    %meta{content: "width=device-width, initial-scale=1, maximum-scale=1", name: "viewport"}
    :css
      body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
      table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
      img { -ms-interpolation-mode: bicubic; }

      @media only screen and (max-width: 600px) {
        .deviceWidth { width: 100% !important; }
        .center { text-align: center !important; }
        img { max-width: 100% !important; height: auto !important; }
        .hide { display: none !important; }
        .stack { display: block !important; width: 100% !important; }
      }

      body {
        width: 100% !important;
        background-color: #ffffff;
        margin: 0;
        padding: 0;
        -webkit-font-smoothing: antialiased;
        font-family: Georgia, Times, serif;
      }

      .ReadMsgBody { width: 100%; background-color: #ffffff; }
      .ExternalClass { width: 100%; background-color: #ffffff; }
      table { border-collapse: collapse; }

  %body{leftmargin: "0", marginheight: "0", marginwidth: "0", style: "font-family: Georgia, Times, serif", topmargin: "0", yahoo: "fix"}
    %table{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "100%"}
      %tbody
        %tr
          %td{bgcolor: "#ffffff", style: "padding-top:0px", valign: "top", width: "100%"}
            %table.deviceWidth{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "700"}
              %tbody
                %tr
                  %td{bgcolor: "#ffffff", width: "100%"}
                    / Logo
                    %table.deviceWidth{align: "left", border: "0", cellpadding: "0", cellspacing: "0"}
                      %tbody
                        %tr
                          %td.center
                            %img
                              = link_to root_url, id: "logo1", class: "checkout_link" do
                                = image_tag('logo-red.png', style: "width:150px;height:70px;margin-top: 0x;")
                    %table.deviceWidth{align: "right", border: "0", cellpadding: "0", cellspacing: "0"}
                      %tbody
                        %tr
                          %td.center{style: "font-size: 14px; padding:0px 20px;text-align: right;"}
                            %a{href: designer_design_url(designer_id: @design.designer, id: @design), style: "text-decoration: none; color: #3b3b3b;"} Unable to see this message. Click here to View
                        %tr
                          %td.center{style: "font-size: 14px;font-weight:bold;  text-align: right;padding:0px 20px;"} Free Shipping (India)
                        %tr
                          %td.center{style: "font-size: 14px;font-weight:bold;  text-align: right;padding:0px 20px;"} 7-Day Returns
                        %tr
                          %td.center{style: "font-size: 14px;font-weight:bold;  text-align: right;padding:0px 20px;"} 50,000+ Designs
                        %tr
                          %td.center{style: "font-size: 14px;font-weight:bold;  text-align: right;padding:0px 20px;"} Worldwide Delivery

                    %table{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "100%"}
      %tbody
        %tr
          %td{bgcolor: "#ffffff", style: "padding-top:0px", valign: "top", width: "100%"}
            %table.deviceWidth{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "700"}
              %tbody
                %tr
                  %td
                    %div{style: "height:0px;border-bottom: 1px dotted gray;margin-bottom: 20px;"} &nbsp;
                %tr
                  %td{bgcolor: "#ffffff", width: "100%"}
                    %table.deviceWidth{align: "left", border: "0", cellpadding: "0", cellspacing: "0"}
                      %tbody
                        %tr
                          %td.center
                            %img.deviceWidth
                              = link_to designer_design_url(designer_id: @design.designer, id: @design) do     
                                = image_tag(@design.master_image.photo.url(:small), alt: @design.title)
                    %table.deviceWidth{align: "left", border: "0", cellpadding: "0", cellspacing: "0"}
                      %tbody
                        %tr
                          %td{style: "font-size: 12px; color: #959595; font-weight: normal; text-align: left; font-family: Georgia, Times, serif; line-height: 15px; vertical-align: top; padding: 0px 40px;"}
                            %p{style: "mso-table-lspace:0;mso-table-rspace:0; margin:0; font-size:19px;color:black;margin-bottom: 5px;"}= truncate(@design.title, length: 40)
                            %h1{style: "line-height:0px;position:absolute;margin-top: 18px;margin-bottom: 45PX;"}
                              Rs.
                              =@design.discount_price
                            %table{align: "left", width: "100"}
                              %tbody
                                %tr
                                  %td{align: "left",bgcolor: "#409ea8", style: "position: absolute;padding:10px 10px;background-color:red;background-repeat:repeat-x; margin-top: 50px;"}
                                    %a{href: designer_design_url(designer_id: @design.designer, id: @design), style: "color:#ffffff;text-decoration:none;font-size: 15px;font-weight:bold;margin-left:7px"}
                                      Buy Now
                    %table.deviceWidth{align: "left", border: "0", cellpadding: "0", cellspacing: "0"}
                      %tbody
                        %tr
                          %td
                            %div{style: "height:0px;border-bottom: 1px dotted #fff"} &nbsp;
                        %tr
                          %td{style: "font-size: 12px; color: #959595; font-weight: normal; text-align: left; font-family: Georgia, Times, serif; line-height: 16px;  padding: 4px 40px; padding-top: 130px"}
                            %p{style: "mso-table-lspace:0;mso-table-rspace:0; margin:0; font-size:15px;color:black;margin-bottom:2px;"}
                              Have question or need help with your purchases?
                            %table{align: "left", width: "100%", top: "1%"}
                              %tbody
                                %tr
                                  %td{align: "left", bgcolor: "#409ea8", style: "background-color:rgb(220, 219, 219);"}
                                    %a{href: "", style: "color:#ffffff;text-decoration:none;font-size: 15px;font-weight:bold;"}
                                    %img
                                      = link_to new_order_url, id: "phone", class: "checkout_link" do
                                        = image_tag('phone2.png', style: "width:25px;padding: 2px;margin-left:5px;float:left; margin-top:5px")
                                    %p{style: "color:black; margin-left: -211px;float: left;margin-left:5px"}= MIRRAW_CONTACT_INFO
                                %tr
                                  %td
                                    %div{style: "height:0px;border-bottom: 1px dotted #fff"} &nbsp;
                                %tr
                                  %td{align: "left", bgcolor: "#409ea8", style: "background-color:rgb(220, 219, 219);"}
                                    %a{href: "", style: "color:#ffffff;text-decoration:none;font-size: 15px;font-weight:bold;"}
                                    %img
                                      = link_to new_order_url, id: "emailer", class: "checkout_link" do
                                        = image_tag('emailer.png', style: "width:25px;padding: 2px;margin-left:5px;float:left; margin-top:5px")
                                      %p{style: "color:black; margin-left: -211px;float: left;text-decoration:none;margin-left:5px;"}  <EMAIL>

            %table.deviceWidth{align: "center", bgcolor: "#fff", cellpadding: "0", cellspacing: "0", width: "700"}
              %tr
                %td
                  %div{style: "height:0px;border-top: 1px dotted gray;margin-bottom:1%;margin-top: 2%"} &nbsp;
              %tr
                %td{valign: "top"}
                  %table.deviceWidth{align: "left", cellpadding: "0", cellspacing: "0", width: "32%"}
                    %tbody
                      %tr
                        %td{align: "center", style: "padding:0px 0", valign: "top"}
                          %img.deviceWidth
                            = image_tag('shipping_1.png', style: "height: 35px;width: 165px;float: left;margin-right: 13px;")
                        %td{align: "center", style: "padding:0px 0", valign: "top"}
                          %img.deviceWidth
                            = image_tag('shipping-2.png', style: "height: 35px;width: 170px;float: left;margin-right: 13px;")
                        %td{align: "center", style: "padding:0px 0", valign: "top"}
                          %img.deviceWidth
                            = image_tag('shipping-3.png', style: "height: 35px;width: 161px;float: left;margin-right: 11px;")
                        %td{align: "center", style: "padding:0px 0", valign: "top"}
                          %img.deviceWidth
                            = image_tag('shipping-4.png', style: "height: 35px;width: 173px;float: left;")
              %tr
                %td
                  %div{style: "height:0px;border-bottom: 1px dotted gray;"} &nbsp;

    %table{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "100%", top: "10px" }
      %tbody
        %tr
          %td{bgcolor: "#fff"}
            %table.deviceWidth{align: "center", border: "0", cellpadding: "0", cellspacing: "0", width: "700"}
              %tbody
                %tr
                  %td
                    %table.deviceWidth{align: "left", border: "0", cellpadding: "0", cellspacing: "0", width: "55%", top: "10px"}
                      %tbody
                        %tr
                          %td.center{style: "font-size: 14px;padding-top:7px"}
                            = link_to 'About Us |', page_url("about"), rel: 'nofollow', style: "text-decoration:none;color:black;margin-left: 5px;"
                            = link_to 'Contact |', pages_faq_url, rel: 'nofollow', style: "text-decoration:none;color:black;margin-left: 5px;"
                            = link_to 'Shipping |', pages_faq_url(category: ORDERING_OF_FAQ['Shipping'][0]), target: "_blank", rel: 'nofollow', style: "text-decoration:none;color:black;margin-left:-10px;margin-left: 5px;"
                            = link_to 'Payments |', pages_faq_url(category: ORDERING_OF_FAQ['Payments'][0]), target: "_blank", rel: 'nofollow', style: "text-decoration:none;color:black;margin-left:-19px;margin-left: 5px;"
                            = link_to 'FAQ', pages_faq_url, target: "_blank", rel: 'nofollow', style: "text-decoration:none;color:black;margin-left:-24px;margin-left: 5px;"
                    %table.deviceWidth{align: "right", border: "0", cellpadding: "0", cellspacing: "0", width: "40%"}
                      %tbody
                        %tr
                          %td.center{style: "font-size: 11px; color: #f1f1f1; font-weight: normal; font-family: Georgia, Times, serif; line-height: 26px; vertical-align: top; text-align:right", valign: "top"}
                            %img
                              = link_to 'https://www.facebook.com/MirrawDesigns', id: "emailer", class: "checkout_link" do
                                = image_tag('facebook.png', style: "width:27px;padding: 2px;margin-left:5px;float:right;padding: 4px; ")
                            %img
                              = link_to 'https://twitter.com/MirrawDesigns', id:"emailer", class: "checkout_link" do
                                = image_tag('twitter.jpg', style:"width:27px;margin-left:5px;float:right;padding: 4px;")