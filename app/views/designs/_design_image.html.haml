.col-sm-12.col-md-9.col-lg-6.col-xs-12.nopadding.large_device_img_div
  .large_device_img
    .col-sm-2.col-md-2.col-lg-2.thumb_image_div
      #design_gallery
        #gal1.thumb_images
          - video_links = "https://mirraw-test.s3.ap-southeast-1.amazonaws.com/product-video/#{@design.id}.mp4" if @design.has_video
          - images = @design.images
          - total_images_and_videos = images.to_a.insert(1,video_links) 
          - total_images_and_videos.each_with_index do |object , index|
            - if object.instance_of?(Image)
              = link_to 'javascript:void(0)', :data => {:image => object.photo.url(:zoom),'image-id'=>object.id,'zoom-image' => object.zoom_display, 'fancybox-group' => 'thumb'}, :class => 'fancybox-thumbs' do
                = image_tag(object.photo.url(:thumb), :alt => @design.title, :id => 'image_thumbnail_' + object.id.to_s)
            - el<PERSON><PERSON>_DESIGN_VIDEO && object.instance_of?(String)
              #play_video.play_button.fancybox-thumbs{style: "position: relative"}
                = image_tag(@design.images.first.photo.url(:thumb), :alt => @design.title, :id => 'image_thumbnail_' + @design.images.first.id.to_s, class: 'videos',:remote => true)
                .play_button{style: "bottom: 0px"}
                  %span.glyphicon.glyphicon-play
              = render partial: '/designs/product_page_video_modal', locals: {youtube_link: object}               

    .col-sm-10.col-md-9.col-lg-10.large_img_div{style: "padding: 0px;"}
      .large_img
        -if show_wishlist?
          = render partial: 'wishlists/wishlist_forms', locals: {design: @design}
        - if STITCHING_LABEL_DESIGNABLE_TYPE.include?(@design.designable_type) && stitching_label.present?
          .label_for_image_box
            .label_text= stitching_label.titleize
        - elsif @design.designable_type == 'Kurti' && @design.package_details.try(:gsub, /[^\d]/, '').try(:length) == 1
          .label_for_image_box
            .label_text Top Only
        - if @master_image.present?
          = image_tag(@master_image.photo.url(:zoom), :alt => @design.title, :itemprop => "image",:id => 'master', :data => {'zoom-image' => @master_image.zoom_display, :id => @master_image.id})
          %ul
            %li{unbxdattr: "product", unbxdparam_sku: @design.id, unbxdparam_prank: "1"}
          .clr
    .img_text.col-sm-12.col-md-9.col-lg-10 Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution.
  - if false
    .small_device_img.visible-xs
      .design_images_div{:style =>'margin-top: 5px'}
        .wrapper
          .carousel{'data-mixed' => ''}
            %a.prev{"data-prev" => ""}
            %ul.design_images
              - @design.images.each do |image|
                %li
                  .wrap
                    %figure
                      = link_to image.photo.url(:small), :rel => 'group' do
                        = image_tag(image.photo.url(:small), :alt => @design.title, :'data-src' => image.photo.url(:small))
            %a.next{"data-next" => ""}
            %a.badge
  - if @privileged_user
    .design_counts.col-sm-12.col-md-9.col-lg-10.col-xs-12
      = button_tag 'Make Master', :value => 'Make Master', :class => 'button-medium make-master' if current_account.present? && %w(senior_vendor_team vendor_team admin super_admin).include?(current_account.role.try(:name))
      - if @super_user
        -if grading_access
          = nested_form_for [@designer, @design], :remote => true do |f|
            - if ['inr', 'rs'].include?(@symbol.downcase) || current_account.role.try(:name) == 'senior_vendor_team'
              = f.label :grade, 'Grade:'
              = f.text_field :grade, size: 8
              = f.submit 'save'
              %br
              = f.label :grade_mobile, 'Grade Mobile:'
              = f.text_field :grade_mobile, size: 8
              = f.submit 'save'
            - else
              = f.label :international_grade, 'International Grade:'
              = f.text_field :international_grade, size: 8
              = f.submit 'save'
              %br
              = f.label :international_grade_mobile, 'International Grade Mobile:'
              = f.text_field :international_grade_mobile, size: 8
              = f.submit 'save'
        .row
          Sell Count:
          = @design.sell_count
          %br
          Return Count:
          = @design.return_count
          %br
          Clicks Count:
          = @design.clicks
          %br
          Quantity:
          - unless @design.variants_available
            = @design.designer_quantity
          %br
          Quality:
          = (@design.quality_level.to_f * 10000.0).round(2)
          -if @design.last_seller_out_of_stock.present?
            %br
            Last seller oos: #{@design.last_seller_out_of_stock.strftime('%d %b %Y')}
          -if @design.last_sold_out.present?
            %br
            Last sold out: #{@design.last_sold_out.strftime('%d %b %Y')}
          -if @design.last_in_stock.present?
            %br
            Last in stock: #{@design.last_in_stock.strftime('%d %b %Y')}
          %br
          -if grading_access
            Sell Count Last 30 days:
            = @design.metric_sales_30
          %br
          Shipping Bifurcation:
          %br
          Country Time / SOR:
          - country = Country.country_name(@country_code).try(:downcase)
          = Country.get_country_shipping_time[country].to_f
          %br
          SLA Config for non stitching preparation days
          = Designer::SLA_CONFIG[:non_stitching_preparation_days]
          %br
          Design ETA:
          = @design.eta.to_i
          %br
          Vacation Time:
          = @design.designer.vacation_days_count.to_i
          %br
          Designer Ship Time(international):
          = @design.designer.ship_time
          %br
          Category Time(international):
          = @design.categories.collect(&:eta).max.to_i
          - if current_account.present? && %w(super_admin).include?(current_account.role.try(:name))
            %br
            - usd_price = (@design.effective_price(country_code: 'US')/CONVERSION_RATES['US']).to_f.round(2)
            USD Price:
            = usd_price
            %br
            - inr_price = @design.effective_price(country_code: 'IN')
            INR Price:
            = inr_price
            %br
            - payout = (inr_price * (100 - @design.designer.transaction_rate.to_i)/100).to_i
            Payout:
            = "Rs. #{payout}"
            %br
            - usd_to_inr_price = usd_price*CurrencyConvert.find_by_country_code('US').market_rate.to_f
            Margin:
            = (((usd_to_inr_price-payout) / usd_to_inr_price).to_f.round(2)*100).to_s + "%"
            %br
            - rpv_data = RevenuePerView.where(design_id: @design.id).first
            -if rpv_data.present?
              rpv: #{rpv_data.rpv}

    %br
    -if EXPERT_QC.include?(current_account.try(:email))
      %button#qcbutton.btn.btn-default.btn-sm{"data-target": "#Qcmodel_qc_#{@design.id}", "data-toggle": "modal", type: "button", style: "margin-left: 30%;"}
        ='Expert QC'
      #Qcmodel.modal.fade{id: "qc_#{@design.id}","aria-labelledby": "QcmodelLabel", role: "dialog", tabindex: "-1"}
        .modal-dialog{role: "document" , style: "color: black;"}
          #Qc-model-content.modal-content{style:"margin-left:32% !important; width:545px !important; background: white !important"}
            .modal-header
              %button.close{"aria-label": "Close", "data-dismiss": "modal", type: "button"}
                %span{"aria-hidden": "true"} &#215;
              %h4#QcmodelLabel.modal-title Expert Quality Check
            .modal-body
              %h4#exampleModalLabel.modal-title
                Rate the Product
              %br
              - for i in 1..5
                %button#dLabel{"aria-expanded": "false", "aria-haspopup": "true", type: "button", class:"btn-success btn-lg custom_qc_model_button", data: { design_id: @design.id, rating: "#{i}"}}
                  #{i}
            .modal-footer
              %button#dLabel.btn.btn-danger{"data-dismiss": "modal", type: "button", style: "left:0px !important;", data: { design_id: @design.id, rating: 1}}
                Reject
.nopadding{:style => 'display:none'}
  - @design.images.each do |image|
    = link_to @design.title, image.zoom_display, :class => 'product_gallery', :id => image.id, :title => @design.title