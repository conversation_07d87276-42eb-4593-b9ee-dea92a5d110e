- cache_unless (@super_user || @privileged_user), ["one_design_view_without_rating_#{@symbol}/#{@country}/#{@theme_tester.try(:current_theme)}", @design] do
  - designable_type = @design.designable_type
  - design_specs = DESIGN_SPEC_SUB_GROUP[designable_type].to_a
  - design_spec_hash = get_spec_data(@design,@category,design_specs)
  -#Disabling similar caching temporary. TOBE DONE LATER
  -#  - if @theme_tester.try(:current_theme) != 'red_theme' --- vender mini panel black theme
  -#    = render :partial => '/designs/microdata_header_cache', locals: { stitching_label: design_spec_hash[:stitching].to_s.downcase }
  -#    .col-md-3.col-lg-2.col-sm-3.col-xs-12.hidden-xs.nopadding.floatr
  -#      = render partial: '/designs/vendor_mini_panel'
  -#      .similar_items_outer_div.clear{data: {container: 'Design Details > Right Side Vertical Container'}}
  -#        - if (dd_rgt_side_vr_cntnr = load_unbxd_container('Design Details > Right Side Vertical Container')).present?
  -#          = dd_rgt_side_vr_cntnr
      -# else
        / - if (wholesale_similar_cache = @cache7[:similar]).blank?
        /   - wholesale_similar_cache = Rails.cache.fetch(@list[:similar], :expires_in => CACHE_EXPIRES_IN) do
        /     - render :partial => '/designs/wholesale_similar_cache'
        / = wholesale_similar_cache
        / = raw wholesale_similar_cache
  .container{style: "margin-top: 30px"}
    .design-breadcrumb
      = render partial: 'shared/breadcrumb', locals: {crumbs: @breadcrumb}
    .product_discription_div.floatl.row.col-md-9.col-lg-12.col-sm-9.col-xs-12
      = render :partial => '/designs/image_cache', locals: {design_spec_hash: design_spec_hash, designable_type: designable_type}
      %br
      %br
      - if @super_user
        = render :partial => '/designs/pdp_design_details'
      %br
      %br
      = render :partial => '/designs/design_pairs'
      = render :partial => '/designs/recently_viewed_item_slider'
      .box_ai_widget.design_widget_div.col-md-12.col-lg-12.col-sm-12{data: {title: 'YOU MAY ALSO LIKE', provider: 'BoxAi'}}
      = render :partial => '/designs/details_cache', locals: {design_spec_hash: design_spec_hash, designable_type: designable_type, addon_types: @addon_types}
  :javascript
    addEvent(window, 'load', function() {
    var design_id, design_quantity, path;
      var is_bot = /bot|googlebot|crawler|spider|robot|crawling/i.test(navigator.userAgent);
      if (!is_bot && ($('#line_items_count').length)){
        design_id = $('#line_items_count').attr('pid');
        design_quantity = parseInt($('#line_items_count').data('quantity'));
        path = '/designs/get_line_items_count?id=' + design_id;
        $.ajax({
          url: path,
          type: 'GET',
          datatype: 'script',
          success: function(data) {
            var number;
            number = parseInt(data['count']);
            if (number !== 0) {
              if (design_quantity <= 2 && number > 2) {
                $('.line_items_count_text').html('This product will most likely be <span>Sold Out</span> in a few hours');
              } else if (number > 5) {
                $('.line_items_count_text').html('Fast Mover: <span>' + number + ' people </span>added this to cart today! ');
              } else if (data['count'] === 1) {
                $('.line_items_count_text').html('<span>' + number + ' person </span>added this to cart today!');
              } else {
                $('.line_items_count_text').html('<span>' + number + ' people </span>added this to cart today!');
              }
              $('#line_items_count').fadeIn(1000);
            }
          },
          error: function(error) {}
        });
      }

      if (!is_bot && $('.prod_time').length && gon.india_delivery_date.length) {
        $('.prod_time').html(gon.india_delivery_date);
      }
    });

.container
  .col-md-9.col-lg-10.col-sm-9.col-xs-12.floatl.row.list_top_padding
    - if rating_activated?(action_path_merge(params, 'view', 'rating_ui'))
      - cache_unless account_signed_in?, ["one_design_view_rating_#{@symbol}/#{@country}/#{@theme_tester.try(:current_theme)}", @design] do
        = stylesheet_link_tag 'review-page'
        #review_list_anchor
          = render :partial =>'reviews/review_progress_bar'
          = render :partial =>'reviews/list'
    .col-md-11.col-lg-12.col-sm-11.hidden-xs.google_adsense
