- content_for :footer_js do
  = javascript_include_tag 'designers_show_edit'
  = javascript_include_tag 'seller_design'
.row
  .col-md-12
    %h3= "Editing " + @design.title
.pane 
  = render 'form'
.row
  .col-md-8
    -if (designable = @design.designable).present?
      - if ENABLE_DESIGNER_DESIGNABLE_EDIT || current_account.accountable_type == 'Admin'
        - attributes = designable.attribute_names.map(&:to_sym)-[:id, :created_at, :updated_at]
        = form_tag create_designable_path, method: :post do
          -attributes.each do |attr|
            .row.form-group
              .col-md-2= label_tag attr
              .col-md-8= text_field_tag attr , designable[attr],class: 'form-control'
          = hidden_field_tag :design, @design.id
          %br
          %br
          = submit_tag 'Save',class: 'btn'
:javascript
  $(document).ready(function() {
  MR.designs.init();
  })
