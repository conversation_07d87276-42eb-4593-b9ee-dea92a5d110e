.heading_heart.col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding
  .discount_old_price.floatl{style:"padding-left:0px;"}
    - active_promotions = PromotionPipeLine.active_promotions
    - if @discount_percent == 0
      -if @country_code == "IN"
        %h3.floatl.label{style: "font-size:22px;"} M.R.P &nbsp;
    %h3.floatl.new_price_label= get_price_in_currency_with_symbol(@effective_price)
    .clr
    - if @discount_percent > 0 && @design.designer.name != "Manyavar"
      - if @country_code == "IN"
        .old_price.floatl M.R.P &nbsp;
      .old_price_label.floatl=get_price_in_currency_with_symbol(@design.price)
      .discount_percent.floatl #{@discount_percent} % off
      .clr
    - if @country_code == "IN"
      %h5
        %i (Inclusive of all taxes.)
    .variant-price-text
  - if ['inr', 'rs'].exclude?(@symbol.try(:downcase)) && @design.buy_get_free != 1 && (@design.categories.collect(&:id) & Category::PRICE_MATCH_GUARANTEE_CATEGORIES).present?
    .col-sm-3.price_mismatch
      .price_mismatch_image
        = link_to price_match_guarantee_tnc_url do
          = image_tag('price_match_guarantee.png', class: 'design-cert')
      .price_mismatch_description
        If you find same item with lower price on any other website, contact us and get this item at that lower price. Click to know more!
  - if !@design.mirraw_recommended? && @design.mirraw_certified?
    .col-sm-3
      = image_tag('mirraw-cert2.jpg', class: 'design-cert')  
  - if false
    .inr_heart_div.floatl
      .heart_div.floatr
        = image_tag "heart_40_fill.png", :class => 'heart_40_fill hide'
        = image_tag "heart_40.png", :class => 'heart_40 hide'
        - if current_account && current_account.user? && current_account.user.following?(@design)
          = form_tag '/unfollow/design', :method => 'post', :remote => true, :class => 'form-design-unfollow' do
            = hidden_field_tag 'targetUrl', designer_design_url(@designer, @design)
            = hidden_field_tag 'design_id', @design.id
            = image_submit_tag "heart_40_fill.png"
        - else
          = form_tag '/follow/design', :method => 'post', :remote => true, :class => 'form-design-follow' do
            = hidden_field_tag 'targetUrl', designer_design_url(@designer, @design)
            = hidden_field_tag 'design_id', @design.id
            = image_submit_tag "heart_40.png", :title => "Add to Favorites"
-if false #rating_activated?(action_path_merge(params, 'view', 'rating_ui'))
  -#%a{href: '', title: "View Reviews"}
  %div
    - if ((average_rating = @design.average_rating.to_f) > 1.0 || @super_user)
      .col-md-12.col-sm-12.nopadding.clear{style: "padding-bottom: 20px;"}
        -color_condition = {one: average_rating < 2.0, two: (average_rating >= 2.0 && average_rating < 3.5), three:  average_rating >= 3.5}
        - if rating_activated?(action_path_merge(params, 'view', 'rating'))
          %span.common_small_rating{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]}
            %a{href: '#review_list_anchor'}
              ='★ ' +average_rating.round(1).to_s
          - if @design.mirraw_recommended?
            %span.mirraw-recommended
              = image_tag 'mirraw_recommended.png'
        -if rating_activated?(action_path_merge(params, 'view', 'review_text')) && @design.total_review.to_i > 0
          -if @star_rating && @star_rating.uniq !=[0] && @reviews_list_count.to_i != 0
            %br
            %span.rating_message
              %a{href: '#review_list_anchor'}
                = pluralize(@star_rating.sum.to_i.to_s, 'Rating' ) #+ '  |  ' + pluralize(@reviews_list_count.to_i, 'Review')
                -if @reviews_list_count.to_i > 0
                  =" | #{pluralize(@reviews_list_count.to_i, 'Review')}"
          -else
            %br
            %span.rating_message= "Be the first one to review"
          -# .review-opinion
          -#   -if color_condition[:three]
          -#     = average_rating > 4.5 && @reviews_list_count > 0 ? "Amazing reviews. Great Product." : "Satisfied customers. Good Product."
          -#   -elsif color_condition[:one]
          -#     ="The product has been rated low. #{@reviews_list_count > 0 ? 'Please read customer reviews.' : ''}"
          -#   -else color_condition[:two]
          -#     Mixed ratings on this product.
    -# -elsif false
    -#   %br
    -#   %span.rating_message= "Be the first one to review"

- if @country_code == "IN" 
  - if @bmgnx_hash.present? && @design.buy_get_free? && @design.eligible_for_bmgn_domestic?
    .available-offer-section.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
      .available-offer-label Available offers
      %span#scroll-btn-right.scroll-button
      .available-offer-slider
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.red-b1g1-label
          .offer-container
            .offer-label
            %a.b1g1{href:"/buy-m-get-n-free", title: "View More - Buy #{@bmgnx_hash[:m]} Get #{@bmgnx_hash[:n]} Free Products"}
              - bmgnx_message = Promotions.bmgnx_offer_message
              = bmgnx_message
            .tnc_arrow
            .bmgnx-promo-tnc
              .design-promo-info-tnc{title: bmgnx_message + ' Terms & Conditions'} T&C
          %br
      %span#scroll-btn-left.scroll-button
    .bmgnx-promo-tnc.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
      = render :partial => '/shared/bmgn_tnc_in_detail'

-if !country_or_currency_indian? || @design.ready_to_ship
  .available-offer-section.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
    .available-offer-label Available offers
    %span#scroll-btn-right.scroll-button
    .available-offer-slider
      - if @design.sor_available? || (variant_available = @design.variants.any?{|v| v.sor_available?}) || @design.ready_to_ship
        -hidden_class = (addon_types.blank? || @design.size_in_warehouse? || variant_available) || @design.ready_to_ship  ? '' : 'display: none;'
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.rts-label{style: hidden_class}
          .offer-container
            #ready_to_ship
            .rts_msg{title: "This product is ready to be shipped. Please click on T&C to know more."}  Ready To Ship
            .tnc_arrow
            .rts-promo-tnc
              .design-promo-info-tnc{title: 'Ready To Ship Terms & Conditions'} T&C
          %br
      - if @bmgnx_hash.present? && @design.buy_get_free? && @design.eligible_for_bmgn_international?
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.red-b1g1-label
          .offer-container
            .offer-label
            %a.b1g1{href:"/buy-m-get-n-free", title: "View More - Buy #{@bmgnx_hash[:m]} Get #{@bmgnx_hash[:n]} Free Products"}
              - bmgnx_message = Promotions.bmgnx_offer_message
              = bmgnx_message
            .tnc_arrow
            .bmgnx-promo-tnc
              .design-promo-info-tnc{title: bmgnx_message + ' Terms & Conditions'} T&C
          %br
      - elsif @qpm_messages.present?
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.red-quantity-disc-promo-label
          .offer-container
            .offer-label
            .quantity_discount_message{href:"#", title: @qpm_messages}
              = @qpm_messages
            .tnc_arrow
            .qpm-promo-tnc
              .design-promo-info-tnc{title: @qpm_messages + ' Terms & Conditions'} T&C
          %br
      - is_not_exclude_free_shipping = @design.categories.present? && EXCLUDE_FREE_SHIPPING_CATEGORIES.exclude?(@design.categories.first.id)
      - if @design.free_shipping_eligible? && is_not_exclude_free_shipping
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.red-loyalty-label
          .offer-container
            .offer-label
            = 'Free Shipping'
            .tnc_arrow
          %br

      - if Wallet.cashback_percent > 0
        .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.red-loyalty-label
          .offer-container
            .offer-label
            .loyalty_cashback{title: "#{Wallet.cashback_percent}% cashback of amount payable"} #{Wallet.cashback_percent}% Cashback
            .tnc_arrow
            .loyalty-promo-tnc
              .loyalty-cashback-info-tnc{title:'Loyalty - Terms & Conditions'} T&C
          %br
    %span#scroll-btn-left.scroll-button
  .qpm-promo-tnc.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
    = render :partial => '/shared/quantity_discount_pomotion_tnc_in_detail'
  - if Wallet.cashback_percent > 0
    .loyalty-promo-tnc.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
      = render :partial => '/shared/loyalty_tnc_in_detail'
  .bmgnx-promo-tnc.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
    = render :partial => '/shared/bmgn_tnc_in_detail'
  .rts-promo-tnc.col-lg-9.col-md-9.col-sm-9.col-xs-9.nopadding
    = render :partial => '/shared/rts_tnc_in_detail'
