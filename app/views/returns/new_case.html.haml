= javascript_include_tag 'returns'
= stylesheet_link_tag 'returns'
-if (['new','pending','confirmed'].exclude?(@order.state) && @order.pay_type != COD) || (@order.pay_type == COD && (@order.sane? || @order.dispatched?))
  %h3.text-center= "New Return Case for #{@order.number}"
  #return_admin_panel.container.col-lg-12
    #type_panel.panel.panel-success{style: 'font-size:medium;'}
      .panel-heading.hidden 1. Select Type
      .panel-body.text-center
        .row
          .col-lg-2
          .col-md-2 Select type :
          -options = @order.billing_international? ? %w(Discount Return Shipping) : %w(Return Shipping)
          -options.each do |type|
            .col-md-2
              =radio_button_tag 'type', type
              =type
        %br
        .row=submit_tag 'Continue',id: 'submit_type_form',class: "btn btn-primary"

    #create_return.panel.panel-info.hidden
      =form_for @return do |f|
        =hidden_field_tag 'order_id', @order.id
        =hidden_field_tag 'return_type', ''
        .panel-heading{style: 'font-size:medium;'}
        .panel-body
          .col-md-1=f.label 'Select State :', id: 'return_state_label'
          .col-md-3#discount_state=select_tag :dos_state, options_for_select(['Canceled','Dispatched','Rto']),include_blank: true,class:'form-control',required: true
          #shipping_state.hidden.form-group
            .col-md-3= f.number_field :shipping, placeholder: 'Amount to be refunded',class: 'form-control'
            = f.submit 'Return charges', class: 'btn btn-success text_black'
          .col-md-8.error_message.alert.alert-danger.hidden
        .panel-footer.hidden
          %table.table
            %tr
              %td
              %th.col-md-2 Item
              %th Image
              %th Price (Rs.)
              %th Item Price (Rs.)
              %th.hidden Addon Price (Rs.)
              %th.col-md-2 Reason
              %th Quantity
              %th.hidden Discount Percent (%)
              %th.hidden Discount on
              %th.col-md-2 Total (Rs.)
            -order_discount,order_total,order_total_only_bmgn_products = (@order.discount.to_i + @order.additional_discount.to_i + (@order.referral_discount.to_f * @order.currency_rate.to_f)), 0, 0
            -if @order.discount.to_i > 0 && (coupon = @order.coupon).present? && coupon.coupon_type == 'COFF'
              - order_discount = order_discount - @order.discount.to_i
            -if order_discount > 0
              - @order.line_items.each{|li| current_line_item_total = (li.snapshot_price * li.quantity).to_f ; order_total += current_line_item_total; order_total_only_bmgn_products += current_line_item_total if li.check_country_wise_bmgn_availability(li.snapshot_country_code)}

            -@line_items.each do |item|
              -addon_price = item.line_item_addons.to_a.sum(&:snapshot_price)
              -options = (addon_price.to_i > 0 ? ['Only Product','Only Stitching','Both Product and Stitching'] : ['Only Product'])
              %tr.items{id: "row_#{item.id}"}
                -return_items,designer_order_total = {},0
                -design = item.design
                -designer = design.designer
                -designer_order = item.designer_order
                -premium = design.premium
                -if designer_order.discount.to_i > 0
                  -designer_order_total = designer_order.line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f
                -item_price,total_discount,support_discount,tax_amount = item.get_return_amount(order_total,designer_order_total,designer_order.discount.to_i,order_discount,@order,order_total_only_bmgn_products )
                %td
                  -if premium != true || ['canceled'].include?(designer_order.state)
                    =check_box_tag "return_items[]", item.id, false, class: 'select_return_items'
                %td.col-md-2
                  =link_to item.title, designer_design_path(designer, design)
                  %br
                  =designer.name
                  %br
                  =design.id
                %td= link_to image_tag(item.image(:thumb)),  designer_design_path(designer, design)
                %td
                  + #{item.snapshot_price}
                  -if ['canceled','vendor_canceled'].include?(designer_order.state) || item.status == 'cancel'
                    %br
                    + #{item.line_item_addons.to_a.sum(&:snapshot_price)}
                  %br
                  ="- #{total_discount.to_f.round(2)}"
                  -if tax_amount.to_i > 0
                    %br
                    ="+ #{tax_amount} (Tax)"
                  -if support_discount.to_i > 0
                    %br
                    ="- #{support_discount} (Goodwill)"
                  %br
                  _________
                  %br
                  #{item_price}
                %td=f.label nil, item_price, id: "item_price_#{item.id}"
                %td.hidden=f.label nil, addon_price, id: "addon_price_#{item.id}"
                %td
                  -if premium != true || ['canceled'].include?(designer_order.state)
                    =select_tag "reason_#{item.id}", options_for_select([]),class: 'form-control col-md-2 line_item_reason'
                  -else
                    %p.not-return-box Luxe products are not returnable and non-refundable
                  %br
                  %br
                  =select_tag "reason_description_#{item.id}", options_for_select([]),selected: true, class: "hidden reason_description_#{item.id}"
                %td=select_tag "quantity_#{item.id}", options_for_select((1..item.quantity),item.quantity), class: 'quantity', data: {item_id: item.id}
                %td.hidden=select_tag "discount_#{item.id}", options_for_select((5..50).step(5)),class: 'discount', data: {item_id: item.id}
                %td.hidden=select_tag "discounttype_#{item.id}", options_for_select(options), class: 'discount_on', data: {item_id: item.id}
                %td.col-md-2=text_field_tag "total_#{item.id}", (item_price * item.quantity), class: 'form-control text_black', readonly: true
            %tr
              %td.text-center{colspan: 11}
                = f.submit 'Proceed', class: 'btn btn-success text_black return_products',disable_with: 'Please Wait...'

-else
  %h3.text-center= "Order #{@order.number} is not yet marked sane !"