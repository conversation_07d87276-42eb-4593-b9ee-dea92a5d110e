= javascript_include_tag 'seller_campaign'
= stylesheet_link_tag 'mirraw_admin'
= content_for :page_title, 'Campaign Portal'
.row
  .col-12
    - if @seller_campaigns.present?
    - else
      .alert.alert-danger.mt-4
        %strong Note:
        | No campaigns are currently active.
.container-fluid.campaign-container.seller_admin_portal
  .date_filter 
    = render :partial => '/designers/date_filter', locals: {date_filter_path: all_campaign_path}
  .campaign-row
    - @seller_campaigns.each do |seller_campaign|
      .campaign_columns
        .card-campaign.shadow-sm
          .card-body-campaign.d-flex.flex-column
            .campaign_portal_card_info
              %h5.card-title.text-primary= seller_campaign.name
              %p.card-text.text-muted= seller_campaign.description
              .campaign_portal_date_format
                %p.card-text
                  %strong.text-dark Start Date:
                  = seller_campaign.start_date.strftime("%Y-%m-%d %H:%M")
                %p.card-text
                  %strong.text-dark End Date:
                  = seller_campaign.end_date.strftime("%Y-%m-%d %H:%M")
            .campaign_portal_button    
              - if @designer.present?
                - participation = @designer.designer_campaign_participations.find_by(seller_campaign_id: seller_campaign.id)
                - if !participation.present? && seller_campaign.end_date >= Time.now
                  = form_tag participate_in_campaign_path(seller_campaign_id: seller_campaign.id), method: :post, multipart: true do
                    %div.mt-4
                      %label.font-medium.text-gray-700{:for => :csv_file} Upload CSV File
                      %div.flex.items-center.gap-4.mt-2
                        = file_field_tag :csv_file, accept: 'text/csv', class: 'file-input px-4 py-2 border border-gray-300 rounded cursor-pointer'
                        = link_to "Download Demo CSV", 'https://mirraw-test.s3.ap-southeast-1.amazonaws.com/discount_csv.csv', class: "text-blue-500 underline"
                    %br
                    = hidden_field_tag :designer_id, @designer.id
                    = submit_tag 'Participate', class: 'btn btn-primary mt-auto', data: { confirm: 'Our team will get back to you soon for further details. Thank you for participating.' }, disabled: false

                - elsif participation.present?
                  - if seller_campaign.start_date <= Time.now
                    = link_to 'Participated', '#', class: 'btn btn-success mt-auto'
                  - elsif seller_campaign.start_date > Time.now
                    = link_to 'Cancel Participation', cancel_participation_path(participation), method: :delete, data: { confirm: 'Are you sure you want to cancel your participation?' }, class: 'btn btn-danger mt-auto'
  = will_paginate @seller_campaigns

