#campaigns-popup-modal.modal.fade{role: "dialog"}
  .modal-dialog
    .modal-content
      .modal-header
        %button.close#close-campaigns-modal x
        %h4.modal-title= SELLER_CAMPAIGN_POPUP_HEADERS.sample
      .modal-body
        - active_campaigns.take(2).each do |campaign|
          .campaign-card
            %h3= campaign.name
            = form_tag participate_in_campaign_path(seller_campaign_id: campaign.id), method: :post,data: { type: 'json' },remote:true do
              = hidden_field_tag :designer_id, @designer.id 
              %br
              = submit_tag 'Participate', class: 'btn btn-primary btn-sm mt-3 participateButton'

:javascript
  $(document).ready(function() {
    MR.sellerCampaign.campaignPopup();
  });

