=content_for :page_title, 'Designer Batch failed'
- if @ids.present?
  %div{style: "display: flex;"}
    %div{style: "flex: 1; padding: 20px;"}
      = form_tag failed_designs_path, :method => 'GET' do
        .row
          .col-md-4= select_tag 'failed_batch_id' , options_for_select(@ids,params[:failed_batch_id]),class: 'form-control',style: 'width: 300px'
          .col-md-2= submit_tag 'Get Batch List',class: 'btn', style: 'margin-left: 140px'
    %div{style: "flex: 1; padding: 20px;"}
      -if ACCESSIBLE_EMAIL_ID['designer_batch_delete_access'].include?(current_account.email)
        .row
          = hidden_field_tag 'designer_id', @designer.id
          = text_field_tag :batch_ids, '', placeholder: 'Enter Single Batch ID', required: true, style: 'color: black;margin-left:10px;height: 35px;width: 300px'
          = submit_tag 'Delete Processing SKUs', :class => 'button btn btn-success delete_processing_skus', style: 'background-color: red'
  .row
    .col-md-12= will_paginate @failed_designs unless @failed_designs.nil?
  .row
    .col-md-6
      %h4 Designer Batch Details
      %table.table
        %tr 
          %th Passed Records: 
          %th Failed Records
          %th Total Records:
        %tbody
          %tr
            %td= @failed_batch.passed 
            %td= @failed_batch.failed 
            %td= @failed_batch.no_of_records
    .col-md-6
      %h4 Passed Designs Details
      %table.table
        %tr 
          %th Under Review Records:
          %th Rejected Records:
          %th Approved Records:
        %tbody
          %tr
            %td= @design_review
            %td= @design_rejected
            %td= @design_approved
  .row
    .col-md-6
      %h4
        %u Failed Designs
    .col-md-6
      %h4
        %u Rejected Designs due to Failed Images
  .row
    .col-md-6
      - if @failed_designs.to_a.present?
        %table#tbl_bulk_upload.table.table-bordered
          %thead
            %tr
              %th Errors found while processing
              - %w(design_code title category).each do |column|
                %th= column.titleize
              %th Action
          %tbody
            - @failed_designs.each do |design|
              -error_columns = []
              %tr{:id => "row_#{design.id}"}
                %td.red
                  -if @version_header.present?
                    -design.error.each do |err|
                      = err
                      -error_columns << err.split.first.try(:downcase)
                      -error_columns << err.split.last.try(:downcase)
                  -else
                    =design.errors_found
                -if @version_header.present?
                  %td= label_tag :design_code, design.row[0]
                  %td= label_tag :title, design.row[1]
                  %td= label_tag :category, design.row[2]
                -else
                  %td= label_tag :design_code, design.design_code
                  %td= label_tag :title, design.title
                  %td= label_tag :category, design.category
                %td= button_tag 'Edit', class: 'btn',type: 'button',id: design.id,data: {toggle: 'modal', target: "#modal_#{design.id}"}
                .design-details{class: 'modal fade',role: 'dialog',id: "modal_#{design.id}"}
                  .modal-dialog{style: 'width: 80%'}
                    .modal-content
                      =form_tag(@version_header.present? ? uploading_version_failed_designs_path : uploading_failed_designs_path,:method => 'post') do
                        = hidden_field_tag :failed_batch_id, @batch_id
                        = hidden_field_tag :design_id, design.id
                        .modal-header
                          =button_tag type: 'button',class: 'close hide_modal','data-dismiss'=>'modal','aria-label'=>'close' do
                            %span{'aria-hidden' => 'true',style: 'color:black;font-size:1.1em;'} &times;
                          %h4=@version_header.present? ? design.row[1] : design.title
                          %span.red
                            Errors :
                            =@version_header.present? ? design.error.join(' , ') : design.errors_found
                        .modal-body{style: 'height: 430px;overflow-y: scroll;'}
                          -if @version_header.present?
                            - @version_header.each_with_index do |column,index|
                              -unless @skip_columns.include?(column)
                                .form-group.col-md-4
                                  =label_tag column.try(:titleize)
                                  =text_area_tag "value[#{index}]", design.row[index], class: "form-control #{error_columns.include?(column) ? 'border-red' : ''}"
                          -else
                            - %w(design_code title category package_details weight_in_gms fabric_of_saree work type_name width_of_saree_in_inches length_of_saree_in_metres saree_color blouse_availability blouse_as_shown_in_the_image size_of_blouse_in_cms fabric_of_blouse blouse_color blouse_work petticoat_availability size_of_petticoat_metres color_of_petticoat fabric_of_petticoat tag_list quantity price discount_percent occasion look saree_border pallu_style region pattern embellish celebrity description product_type).each do |column|
                              .form-group.col-md-4
                                =label_tag column.try(:titleize)
                                =text_area_tag column, design[column], class: 'form-control'
                            -%w(image image1 image2 image3 image4).each do |column|
                              -if design[column].present?
                                .form-group.col-md-4
                                  .col-md-3=label_tag column.try(:titleize)
                                  .col-md-9=image_tag(design[column], height: '150')
                                  =text_area_tag column, design[column], class: 'form-control'
                        .modal-footer
                          .form-group.text-center
                            =submit_tag 'Update', :class => 'btn btn-success'
    - if @failed_images.present?
      .col-md-6
        %table.table.table-bordered
          %tr
            %th.col-md-4 Message
            %th.col-md-3 url
            %th.col-md-1 mirraw id
            %th.col-md-2 SKU code
            %th.col-md-2 Action
          %tbody
            - @failed_images.each do |image|
              =form_tag(failed_images_path,:method => 'post') do
                %tr{:id => "row_#{image.id}"}
                  -if image.message.present?
                    %td.col-md-4= "Error: " + image.message.to_s
                    
                    %td.col-md-3= text_area_tag :url, image.url.truncate(30), cols: '80', rows: '5'

                    %td.col-md-1= image.design_id
                    %td.col-md-2= image.design.design_code
                    %td.col-md-2
                      = hidden_field_tag :image_id, image.id
                      = submit_tag 'Update', :class => 'btn btn-sm'
                  -else
                    %td.col-md-4= "Processing"
                    
                    %td.col-md-3= image.url.truncate(30)

                    %td.col-md-1= image.design_id
                    %td.col-md-2= image.design.design_code
                    %td.col-md-2
- else
  %h5 No Bulk Uploads from Your Account
