= stylesheet_link_tag 'admin_style'
= javascript_include_tag 'grading_tag'
%h3.grading-tag-title GRADING TAG PANEL

#grading_tag_actions
  = link_to 'Create Grading Tag', new_mirraw_admin_design_grading_url, class: 'btn btn-primary create-grading-tag-btn'
  = link_to "Download Demo CSV", 'https://mirraw-test.s3.ap-southeast-1.amazonaws.com/demodesigngradingseq.csv', class: 'btn btn-primary demo-csv-btn'
#grading_tag_search
  = form_tag mirraw_admin_design_gradings_path, method: :get, class: 'form-inline' do
    = select_tag :app_name, options_for_select(['App Name','Mirraw','Luxe']), class: "form-control bg-dark text-white"
    = select_tag :platform, options_for_select(['Platform'], selected: 'Platform'), class: "form-control bg-dark text-white"
    = select_tag :country_code, options_for_select([['Country', 'Country Code'], ['All', 'All']] + country_options), class: "form-control bg-dark text-white", id: 'country_code'
    = select_tag :grading_taggable_type, options_for_select(['Grading Taggable Type','Category', 'Collection','Catalogue','Designer'], selected: 'Category'), class: "form-control bg-dark text-white", id: 'grading_taggable_type'
    = select_tag :grading_taggable_id, options_for_select(Category.order(:name).pluck(:name, :id)), class: "form-control bg-dark text-white", id: 'grading_taggable_id'
    
    %button{ type: 'submit', class: 'btn btn-primary' } Search
    = link_to 'Clear Filters', mirraw_admin_design_gradings_path, class: 'btn btn-primary ml-2'
    
= render partial: 'grading_tags', locals: { grading_tags: @grading_tags }

:javascript
  $(document).ready(function () {
    MR.gradingTag.init()
  });