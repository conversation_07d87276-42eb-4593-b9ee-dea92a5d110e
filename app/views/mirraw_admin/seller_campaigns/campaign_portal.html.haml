= javascript_include_tag 'seller_campaign'
= stylesheet_link_tag 'mirraw_admin'
.campaign_page
  %h1.mb-4.campaign_header Seller Campaigns
  .filters
    = render :partial => '/designers/date_filter', locals: {date_filter_path: campaign_portal_mirraw_admin_seller_campaigns_path}
    .new_seller_button
      = link_to 'New Seller Campaign', new_mirraw_admin_seller_campaign_path, class: 'btn btn-success'
  .table-responsive
    %table.table
      %thead
        %tr
          %th Name
          %th Description
          %th Start Date
          %th End Date
          %th{ colspan: 3 } Actions
      %tbody
        - if @seller_campaigns.present?
          - @seller_campaigns.each do |seller_campaign|
            %tr
              %td= seller_campaign.name
              %td= seller_campaign.description
              %td= seller_campaign.start_date.strftime("%Y-%m-%d %H:%M")
              %td= seller_campaign.end_date.strftime("%Y-%m-%d %H:%M")
              %td.two-buttons
                = link_to 'Show', mirraw_admin_seller_campaign_path(seller_campaign), class: 'btn btn-info btn-sm'
                = link_to 'Delete', mirraw_admin_seller_campaign_path(seller_campaign), method: :delete, data: { confirm: 'Are you sure?' }, class: 'btn btn-danger btn-sm'


        - else
          .field Empty
    = will_paginate @seller_campaigns
  