%h1.mb-4.text-center New Seller Campaign

.container-fluid
  .row.seller-campaign
    .col-md-6
      = form_for [:mirraw_admin, @seller_campaign] do |form|
        .form-group
          = form.label :name, class: 'form-label'
          = form.text_field :name, class: 'form-control form-control-sm', required: true, maxlength: 75

        .form-group
          = form.label :description, class: 'form-label'
          = form.text_area :description, rows: 3, class: 'form-control form-control-sm', required: true

        .form-group
          = form.label :start_date, 'Start Date and Time', class: 'form-label'
          = form.datetime_local_field :start_date, class: 'form-control form-control-sm', required: true, min: Time.now.strftime('%Y-%m-%dT%H:%M')
        
        .form-group
          = form.label :end_date, 'End Date and Time', class: 'form-label'
          = form.datetime_local_field :end_date, class: 'form-control form-control-sm', required: true, min: Time.now.strftime('%Y-%m-%dT%H:%M')


        .form-group.mt-4.text-center
          = form.submit 'Create Seller Campaign', class: 'btn btn-primary'
          = link_to 'Back', campaign_portal_mirraw_admin_seller_campaigns_path, class: 'btn btn-secondary ml-2'
