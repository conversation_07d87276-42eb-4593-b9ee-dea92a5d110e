= javascript_include_tag 'designer_orders'
= javascript_include_tag 'warehouse_order'
= form_tag designer_warehouse_orders_path(@designer), :method => 'get' do
  .col-md-2.col-sm-6.col-xs-6= select_tag :type_of_filter, options_for_select(['Warehouse Order Number', 'Design Id'], params[:type_of_filter]), class: 'form-control', id: 'type_of_filter'

  #warehouse_input.col-md-2.col-sm-6.col-xs-6{ style: (params[:type_of_filter] && params[:type_of_filter] != 'Warehouse Order Number') ? "display: none;" : nil }= text_field_tag :warehouse_order_number, params[:warehouse_order_number], placeholder: 'Enter Warehouse Order Number', class: 'form-control'

  #design_input.col-md-2.col-sm-6.col-xs-6{ style: (params[:type_of_filter] == 'Design Id') ? nil : "display: none;" }= text_field_tag :design_id, params[:design_id], placeholder: 'Enter Design ID', class: 'form-control'

  
  .col-md-2
    = select_tag :state, options_for_select(WarehouseOrder.state_machine.states.map{|i|[i.name.to_s.humanize, i.name]},params[:state]),class: 'form-control',prompt: 'All'
  #reportrange.col-md-2.col-sm-6.col-xs-6
    %i.glyphicon.glyphicon-calendar.fa.fa-calendar
    / %span #{Time.now.strftime('%d %B, %Y')} - #{7.days.ago.strftime('%d %B, %Y')}
    / -date = params[:daterange_selector].presence || "#{Time.now.strftime('%d %B, %Y')} - #{7.days.ago.strftime('%d %B, %Y')}"
    =hidden_field_tag :daterange_period,(params[:daterange_period].presence || 'Last 3 Months')
    =text_field_tag :daterange_selector,params[:daterange_selector],style: 'border: 0px;width: 85%'
    %b.caret
  %span.submit(style="margin-left:10px;")
    %span.button-medium
      = submit_tag "Search", name: nil, class: 'btn btn-success'
    %span.button-medium(style="margin-left:5px;")
      = submit_tag "Export", name: 'export', class: 'btn btn-primary'

=will_paginate @warehouse_orders
.row
  %table.table
    %thead
      %tr
        %th.col-md-1.text-center Created On
        %th.col-md-4.text-center Order details
        %th.col-md-2.text-center Delivery
        %th.col-md-2.text-center Status
        %th.col-md-3.text-center Action
  .clearfix
  - @warehouse_orders.each do |warehouse_order|
    -warehouse_order_invoice = WarehouseOrderInvoice.new(warehouse_order)
    %span{class: "warehouse_order_#{warehouse_order.id}"}
      =render partial: 'warehouse_order_account', locals: {warehouse_order: warehouse_order, warehouse_order_invoice: warehouse_order_invoice}
  