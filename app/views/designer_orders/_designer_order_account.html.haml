.row.designer_orders.states_css
  .col-md-12.col-sm-12.col-xs-12
    .panel.panel-default.border-panel
      %table.table.table-bordered
        -shipment = designer_order.shipment
        -rtv_shipments = designer_order.rtv_shipments.sort
        %tr
          -order_date = @order.confirmed_at || @order.created_at
          %td.col-md-1.col-sm-2.col-xs-2
            %span #{@order.created_at.strftime('%a, %d %B %Y')}
            %hr
            %b #{@order.pay_type}

          %td.col-md-4.col-sm-8.col-xs-8
            %b #{@order.number}
            %br
            - if (designer_order.ship_to.blank? && @order.international?) || (designer_order.ship_to == 'mirraw')
              =w_order_invoice.ship_to.html_safe
            - else
              %br
              = "Name: " + @order.name
              %br
              -# = "Phone: " + @order.phone
              -# %br
              = "Address: " + @order.street + ', ' + @order.city + ', ' + @order.buyer_state + ', ' + @order.pincode.to_s + ', ' + @order.country
          %td.col-md-2.col-sm-2.col-xs-2.text-center
            %p
              -if ['pickedup','dispatched','completed'].include? designer_order.state
                %b Dispatched On
                %br
                #{(designer_order.pickup || shipment.try(:created_at) || designer_order.updated_at).try(:strftime,'%d %B %Y')}
              -else
                %b Dispatch by
                %br
                #{designer_order.get_vendor_dispatch_days(express_delivery).strftime('%d %B %Y')}
                %br
                %br
                / %b Deliver By
                / %br
                / #{(designer_order.delivery_nps_info.try(:promised_delivery_date).try(:strftime,'%d %B %Y')) || (@order.international? ? order_date.advance(days: 5) : order_date.advance(days: SHIPPING_TIME)).strftime('%d %B %Y')}
                %br
                - if designer_order.fedex_serviceable
                  %br
                  Deliverable By Fedex
            %hr
            %b Payout : Rs. #{designer_order.payout}
            - if ACCESSIBLE_EMAIL_ID['payout_breakdown'].to_a.include?(current_account.try(:email))
              - vendor_payout, sor_item_total, item_selling_price, item_commission = designer_order.get_vendor_payout 
              - if item_selling_price && item_selling_price > 0
                %br
                %b Item Total : Rs. #{item_selling_price.round(2)}
              - if item_commission && item_commission > 0
                %br
                %b Commission : Rs. #{item_commission.round(2)}
              - if sor_item_total && sor_item_total > 0
                %br
                %b SOR Item Total : Rs. #{sor_item_total.round(2)}
              - if designer_order.shipping && designer_order.shipping > 0
                %br
                Shipping Charges : Rs. #{designer_order.shipping.round(2)}
            - if designer_order.discount && designer_order.discount > 0 
              %br
              Discounts Offered : #{designer_order.discount}
          %td.col-md-2.col-sm-2.col-xs-2
            .row.text-center
              %b.label{class: "#{designer_order.state}"} #{designer_order.state}
              -if @order.international?
                %span.label.label-warning='Export'
              - if express_delivery.present?
                %br
                = image_tag('express_delivery.png', alt: 'Express Delivery')
              %br
              %br
              - if rtv_shipment = rtv_shipments.select{|rtv_ship| rtv_ship.shipment_type == 'rtv'}.last
                .alert.alert-success.replacement_rtv_note
                  %h5 RTV Dispatched
                  %p
                    %b Shipping Co:
                    #{rtv_shipment.shipper_name}
                  %p
                    %b Tracking no.:
                    #{rtv_shipment.number}
                %br
              %b.red
                ="Days Passed : #{(Date.today - (designer_order.confirmed_at || designer_order.created_at).to_date).to_i} days" if designer_order.state == "pending"
                - if shipment.present?
                  - if shipment.system_shipment_charges.present? && shipment.system_weight.present?
                    %br
                    = 'Shipment Weight: '  + shipment.system_weight.to_s
                    %br
                    = 'Shipment Charges: ' + shipment.system_shipment_charges.to_s
            %hr
            ='Error: ' + @order.fedex_shipment_error.to_s if @order.fedex_shipment_error.present?
            ='Error: ' + designer_order.shipment_error.to_s if designer_order.shipment_error.present?
            .row.text-center
              - if designer_order.state == "pending"
                %b.red
                  -tat_breatch = Date.today<(designer_order.get_vendor_dispatch_days(express_delivery).strftime('%d %B %Y')).to_date ? "0 days" : "#{(Date.today - (designer_order.get_vendor_dispatch_days(express_delivery).strftime('%d %B %Y')).to_date).to_i} days"
                  ="TAT Breach"
                  %br
                  ="Days Passed:  #{tat_breatch}"
          %td.col-md-3.col-sm-10.col-xs-10.text-center{rowspan: 2}
            - latest_replacement_shipment = rtv_shipments.select{|rtv_ship| rtv_ship.shipment_type == 'replacement'}.last
            - if shipment.present?
              = render partial: '/designer_orders/shipment_links', locals: {shipment: shipment, replacement_shipment: latest_replacement_shipment, designer_order: designer_order}
            - next_month = designer_order.completed_at + 1.month if designer_order.completed_at.present?
            - if (designer_order.credit_note_url.present?)
              = link_to "[Edit / Generate Credit Note]", edit_credit_note_path(edit_credit_note_link: Base64.urlsafe_encode64("#{@order.id}/#{designer_order.id}/#{designer_order.credit_note_number}")), type: 'button', class: 'btn btn-info btn-sm', :target => '_blank'
              %br
            - if (designer_order.state != 'completed' || ((current_account.admin? || ACCESS_TO_CHANGE_INVOICE_NUMBER.include?(@designer.id)) && next_month.present? && !Date.new(next_month.year, next_month.month, 5).past?)) && designer_order.bulk_shipment_id.blank?
              = link_to "[Edit / Generate Invoice]", edit_designer_invoice_path(edit_invoice_link: Base64.urlsafe_encode64("#{@order.id}/#{designer_order.id}")), type: 'button', class: 'btn btn-info btn-sm', :target => '_blank'
              %br
              %b.small.text-danger * Generate Invoice only when product is READY TO DISPATCH to be eligible for payment.
            %hr
            = form_tag designer_order_mark_pickup_done_path, class: "tracking_details form form-horizontal",id: "vendor_tracking_form_#{designer_order.id.to_s}",enctype: "multipart/form-data" do
              = hidden_field_tag 'designer_order_id', designer_order.id, class: 'designer_order_id'
              - if @order.present? && @order.cod? && ['pending', 'critical'].include?(designer_order.state) && !@order.international?
                -unless shipment.present?
                  = hidden_field_tag :cod, true
                  - if current_account.designer?
                    - if designer_order.priority_shipper_cod_id.present?# && designer_order.priority_shipper_cod.name != 'ecomexpress'
                      = hidden_field_tag :shipper_id, designer_order.priority_shipper_cod_id
                      -if order_date < DISPATCH_COD_BLOCK_MINUTES.minutes.ago
                        = submit_tag 'Dispatch COD Shipment', class: 'btn btn-small btn-success', data: {disable_with: 'Please Wait...'}
                  - elsif current_account.admin?
                    - if (cod_shippers=designer_order.get_serviceable_shippers_cod(@cod_designer_shippers)).present?
                      .form-group
                        = label_tag 'Shipping Company'
                        .col-md-10.col-md-offset-1= select_tag :shipper_id, options_from_collection_for_select(cod_shippers, :last, :first), :required => true,class: 'form-control col-md-12'
                    -if order_date < DISPATCH_COD_BLOCK_MINUTES.minutes.ago
                      = submit_tag 'Dispatch COD Shipment', class: 'btn btn-small btn-success', data: {disable_with: 'Please Wait...'}
              - elsif ['pending', 'replacement_pending'].include?(designer_order.state) 
                - shippers = designer_order.get_serviceable_shippers
                - if current_account.designer? && shippers.present? && (shipper_names = shippers['RAPID DELIVERY'].present? ? shippers.slice('RAPID DELIVERY') : ((names = (shippers.keys & DOMESTIC_PREPAID_COURIER_AUTOMATION[designer_order.ship_to].to_a.map(&:upcase))).present? ? shippers.slice(*names) : nil)).present?
                  -if designer_order.clickpost_serviceable.present? && designer_order.priority_shipper_id.present? && shipper_names.include?(designer_order.priority_shipper.name.upcase) && !designer_order.replacement_pending?
                    - shipper_names = {designer_order.priority_shipper.name.upcase => designer_order.priority_shipper.id}
                  .form-group
                    = label_tag 'Shipping Company'
                    .col-md-10.col-md-offset-1= select_tag :shipper_id, options_from_collection_for_select(shipper_names, :last, :first), :class => 'shipper_id form-control col-md-12', data: {id: designer_order.id, ship_to: designer_order.ship_to}
                  .form-group
                    = hidden_field_tag :state, 'dispatched', class: 'state state_'+designer_order.id.to_s
                    = submit_tag 'Dispatch PREPAID Shipment', class: 'btn btn-small btn-success', data: {disable_with: 'Please Wait...'}
                - else
                  .form-group
                    = label_tag 'Shipping Company'
                    .col-md-10.col-md-offset-1= select_tag :shipper_id, options_from_collection_for_select(shippers, :last, :first), :class => 'shipper_id admin_shipper_id form-control col-md-12', :data => {:id => designer_order.id, ship_to: designer_order.ship_to}
                  .form-group
                    #tracking_number_label{:class => 'tracking_number_label_' + designer_order.id.to_s}= label_tag 'Tracking Number'
                    #tracking_number.col-md-10.col-md-offset-1{:class => 'tracking_number_' + designer_order.id.to_s}= text_field_tag :tracking, nil, class: 'form-control col-md-12'
                    =hidden_field_tag :state, 'dispatched', class: 'state state_'+designer_order.id.to_s
                  .form-group
                    = submit_tag 'Dispatch', class: 'btn btn-small btn-success pickup_done_button', data: {international: @order.international?, dos_id: designer_order.id.to_s}
              -elsif designer_order.bulk_shipment_id.present?
                = link_to "View Bulk Shipment Details", designer_bulk_dispatch_path(@designer, options_for_filter: 'dispatched', bulk_shipment_id: designer_order.bulk_shipment_id), target: '_blank'
              -else
                - if designer_order.tracking_partner.present?
                  Shipped By : 
                  %b #{designer_order.tracking_partner}
                  %br
                  Tracking Number : 
                  %b #{designer_order.tracking_num}
                  %br
                  Recent Tracking Number :
                  %b #{designer_order.recent_tracking_number}
                  %hr
                -if (designer_order.created_at > Date.parse('2017-09-01')) && ((designer_order.ship_to.blank? && @order.international?) || (designer_order.ship_to == 'mirraw'))
                  - if designer_order.state == "dispatched" && designer_order.invoice_state == "not_uploaded"
                    %b{style: 'font-size: 18px;'}= 'Upload Signed Invoice here to be eligible for payment'
                    = file_field_tag :file,class: 'form-control col-md-12',accept: 'application/pdf',required: true
                    =hidden_field_tag :state, 'invoice_uploaded'
                    %br
                    = submit_tag 'Upload Invoice', class: 'btn btn-small btn-success',style: 'margin-top:10px;'
                  - elsif designer_order.state == "dispatched" && designer_order.invoice_state == "invoice_uploaded" && !@order.cod?
                    =hidden_field_tag :state, 'completed'
                    = submit_tag 'Mark Complete', class: 'btn btn-small btn-success'
                  - elsif designer_order.state == "completed"
                    %p= "Payout Status = " + designer_order.designer_payout_status if designer_order.designer_payout_status.present?
                    %p= "Payout Date = " + designer_order.designer_payout_date.strftime('%d/%m/%Y') if designer_order.designer_payout_date.present?
                    %p= "Payout Notes = " + designer_order.designer_payout_notes if designer_order.designer_payout_notes.present?
                -else
                  - if designer_order.state == "dispatched" && ((shipment.present? && shipment.shipment_type.try(:upcase) == 'PREPAID') || shipment.blank?)
                    =hidden_field_tag :state, 'completed'
                    = submit_tag 'Mark Complete', class: 'btn btn-small btn-success'
                  - elsif designer_order.state == "completed"
                    %p= "Payout Status = " + designer_order.designer_payout_status if designer_order.designer_payout_status.present?
                    %p= "Payout Date = " + designer_order.designer_payout_date.strftime('%d/%m/%Y') if designer_order.designer_payout_date.present?
                    %p= "Payout Notes = " + designer_order.designer_payout_notes if designer_order.designer_payout_notes.present?
              %br
              %br
              - if return_result.present?
                - return_result.each do|rdo|
                  -if rdo.designer_order_id == designer_order.id
                    Shipped By :
                    %b #{rdo.tracking_company}
                    %br
                    Tracking Number:
                    %b #{rdo.tracking_number}
                    %br
                    “If Undelivered Contact to Mirraw with in 72 Hrs..”
                    %hr


        %tr
          %td.col-md-3.col-sm-10.col-xs-10{colspan: 4}
            - if designer_order.violate_sla?
              - sla_date = (designer_order.confirmed_at.to_date + designer_order.line_items.map{|i| i.design.critical_eta}.max).in_time_zone
              - if sla_date > Date.current
                .alert.alert-warning This order is about to violate Service level agreement please dispatch it before #{sla_date.strftime('%d %b %Y').to_s}
              - else
                .alert.alert-danger This order has violated Service level agreement it should have been dispatched before #{sla_date.strftime('%d %b %Y').to_s}
        = render :partial => '/designer_orders/designer_order', :locals => {:order => @order, :designer_order => designer_order}
