- if designer_order.credit_note_url.present?
  %a{:href => designer_order.credit_note_url} [Credit Note]
- if shipment.invoice_updated_at.present?
  %a{:href => shipment.invoice} [Invoice]
- if shipment.courier_label_url.present? && !designer_order.replacement_pending?
  %a{:href => shipment.courier_label_url} [Label]
- elsif ((label_url = replacement_shipment.try(:label_url)) || shipment.label_updated_at.present?) && !designer_order.replacement_pending?
  %a{:href => label_url || shipment.label} [Label]
- if shipment.return_label_updated_at.present?
  %a{:href => shipment.return_label} [Fedex Return_Label]
- if shipment.tofrom_label_updated_at.present?
  %a{:href => shipment.tofrom_label} [Address Label]