- @index = 0 unless @index.present?
- if design && design.images.present? && design.categories.present? && design.designer.present?
  - no_design_found_page = no_design_found_page.presence
  .col-lg-3.col-md-3.col-sm-3.col-xs-3.nopadding.design-box{class: ('design-'+design.id.to_s)}
    - sibling_design_class = 'sibling_design_' + design.id.to_s
    .sib-design-box{class: sibling_design_class}
      = render partial: 'shared/design_block', locals: {design: design, no_design_found_page: no_design_found_page}
    - @index = @index + 1  
    - if design.in_stock_siblings.present?
      - design.in_stock_siblings.each do |s_design|
        - if design.id != s_design.id
          - sibling_design_class = 'sibling_design_' + s_design.id.to_s
          .sibling-design-box{class: sibling_design_class}
            = render partial: 'shared/design_block', locals: {design: s_design, is_sibling: true, no_design_found_page: no_design_found_page}
      .col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.sibling-design-thumbnails
        .design-group-by-color
          .sibling-design{class: 'current', data: {id: design.id.to_s, current_design_id: design.id}}
            = link_to designer_design_path(design.designer, design), :class => 'design-group-thumb row' do
              - if design.master_image.present?
                = image_tag('11.png', :alt => design.title, :id => 'image_thumbnail_' + design.master_image.id.to_s, height: 30, data: {original: design.master_image.photo.url(:thumb)}, class: 'sibling-thumb-lazy')
          - design.in_stock_siblings.each do |sibling_design|
            - if design.id != sibling_design.id
              .sibling-design{data: {id: sibling_design.id.to_s, current_design_id: design.id}}
                = link_to designer_design_path(sibling_design.designer, sibling_design), :class => 'design-group-thumb row' do
                  - if sibling_design.master_image.present?
                    = image_tag('11.png', :alt => sibling_design.title, :id => 'image_thumbnail_' + sibling_design.master_image.id.to_s, height: 30, data: {original: sibling_design.master_image.photo.url(:thumb)}, class: 'sibling-thumb-lazy')
        - if (sib_count = design.in_stock_siblings.size) > 5
          .sib-count
            = link_to designer_design_path(design.designer, design) do
              %span= '+' + (sib_count - 5).to_s

