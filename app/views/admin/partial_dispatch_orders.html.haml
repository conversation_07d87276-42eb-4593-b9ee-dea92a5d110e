= form_tag admin_partial_dispatch_orders_path, method: 'get', class: 'form-inline' do
  = select_tag 'region', options_for_select(['international', 'domestic'], params[:region]), prompt: 'Select Region'
  = select_tag 'warehouse_id', options_for_select(@warehouse_ids, params[:warehouse_id]), prompt: 'Select Warehouse Id'
  = select_tag 'stitch_check', options_for_select(['Yes', 'No'], params[:stitch_check]), prompt: 'Select Stitching'
  = submit_tag "Get Orders", class: "btn btn-md btn-primary", name: nil
  = submit_tag 'Download Data', name: 'mail_rfd_data', class: 'btn btn-md btn-primary'

%h3 Dispatch Sheet
%hr
%table.table.table-bordered
  %thead
    %tr
      %td Order
      %td Order Country
      %td Customer Name
      %td Rack
      %td Unpack-Id
      %td Total Items
      %td tags
      %td Stitching
      %td Hours Passed
      %td EDD
      %td Order created
  %tbody
    - @line_items_grouped_by_order.each do |order, items|
      - order.tags.present? ? all_tags = order.tags.collect(&:name) : all_tags = []
      - tags = all_tags.reject{|tag| tag.include?('convert-mkt')}.join(',')
      - partial_dispatched_event = order.events.find { |e| e.notes.include?("partial dispatched") }
      %tr
        %td= link_to order.number, order_order_detail_path(order), target: '_blank'
        %td= order.country
        %td= order.name
        %td= formatted_rack_codes(items)
        %td= items.map(&:id).join(', ')
        %td= items.sum { |li| li.quantity.to_i }
        %td= tags.present? ? "Tags: #{tags}" : "Tags: none"
        %td= items.map { |li| li.stitching_required == 'Y' ? 'Yes' : 'No' }.uniq.join(', ')
        - if partial_dispatched_event.present?
          - partial_dispatched_timestamp = partial_dispatched_event.event_timestamp
          - time_diff_hours = ((Time.now - partial_dispatched_timestamp) / 3600).round
          %td= time_diff_hours
        - else
          %td N/A
        %td
          -if (dns = order.delivery_nps_info).present? && dns.promised_delivery_date.present?
            = dns.promised_delivery_date.to_date
          -else
            -
        %td= order.created_at
  = will_paginate @line_items_grouped_by_order
          