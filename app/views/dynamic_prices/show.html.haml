%table
  %tr
    %th
    = @design.title
    - @dynamic_p.each do |x|
      = form_for x, :url => {:action => :update, :method=> :put} do |f|
        = f.hidden_field :country, value: x.country_id
        %tr
          %td
            .country_name
              = x.country_id
              = @countries_hash.delete (x.country_id)

          %td
            .price
              = get_price_in_currency_with_symbol_for_scaling(@design.price(RETURN_NORMAL),x.country_code)
          %td
            = f.text_field :scale, value: x.scale.to_s , size: 3
          %td
            .scaled_price
              = get_price_in_currency_with_symbol_for_scaling( x.scale * @design.price(RETURN_NORMAL),x.country_code)
          %td
            = f.submit "Change"
  %tr
    %td
      = form_for @dynamic_price, :url => { :method=> :post} do |w|
        = w.select :country_id, @countries_hash.map {|t,v| [v,t]}
        = w.submit 'Add' 