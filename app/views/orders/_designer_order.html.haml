= javascript_include_tag 'designer_order'
%table{:class => 'table designer_order_items_' + designer_order.id.to_s}
  %tr
    %th.col-md-3 Item
    %th.col-md-1 Quantity
    %th.col-md-2 Price
    %th.col-md-1 Image
    %th QC
    %th
    %th
    %th Addons
    %th Shipment
  - allow_unpacking = INHOUSE_VENDOR_IDS.map(&:to_i).include?(designer_order.designer_id) || designer_order.inward_bag_id.present?
  - rack_list = designer_order.rack_list
  - designer_order.line_items.each do |item|
    - item_vendor_cost = designer_order.transaction_rate.to_f > 0 ? (item.vendor_selling_price.present? ? item.vendor_selling_price : item.unscaled_price) : item.snapshot_price
    - if item.design
      -@item = item
      -if (item.status == 'cancel')
        -status_of_item = "display:none"
        -show_status = "Canceled"
      -elsif (item.status == 'buyer_return')
        -status_of_item = "display:none"
        -show_status = "Buyer Returned"
      -else
        -status_of_item = 'background:none'
      %tr.item-view{id:"line_item_#{item.id}", data: {item_id: item.id, designer_order_id: item.designer_order_id, design_id: item.design_id, item_quantity: item.quantity, item_vendor_cost: item_vendor_cost}}
        %td
          = link_to item.title + " - #" + item.design.id.to_s, designer_design_path(designer_order.designer, item.design), :name => "line_item_#{item.id}", :style => "text-decoration:underline;"
          %br
          %br
          - if item.check_country_wise_bmgn_availability(designer_order.order.country_code) 
            .span.label.label-xs.label-warning{style:"color:black;"} Buy m Get n
            %br
            %br
          - if (prestitch_note = item.get_prestitch_saree_size)
            .label.label-info{style: 'color:black;font-size:13px;'}= prestitch_note
          - if item.is_product_plus_size?
            %br
            %br
            - plus_size = item.get_addon_option_value_from_notes('plus size')
            - fabric_color = item.get_addon_option_value_from_notes('fabric color')
            - unless plus_size.blank?
              .label.label-info{style: 'color:black;font-size:13px;background-color:#F0AD4E;'} Plus Size : #{plus_size}
              %br
              %br
            - unless fabric_color.blank?
              .label.label-info{style: 'color:black;font-size:13px;background-color:#F0AD4E;'} Plus Size Fabric Color : #{fabric_color}
          %div{:class => ("btn btn-success" if item.note? && item.note.match('Rakhi Schedule Delivery'))}
            = "Notes : " + (item.note? ? item.note : 'none')
            -if item.combo_variant.present?
              =","
              = combo_variant_option_details(item)
          %br
          - vendor_notes = item.events.collect{|e| ([e.event_timestamp, e.note_type, e.notes]) if (e.note_type != 'stylist')}.compact.sort_by(&:first).reverse.map{|e| e.join(' => ')}.join("\r\n")
          - if vendor_notes.present?            
            = render partial: '/orders/line_item_notes_modal', locals: {note_type: 'vendor_notes', notes_content: vendor_notes}
            %br
          - stylist_notes = item.events.collect{|e| ([e.event_timestamp, e.note_type, e.notes]) if (e.note_type == 'stylist')}.compact.sort_by(&:first).reverse.map{|e| e.join(' => ')}.join("\r\n")
          - if stylist_notes.present?
            = render partial: '/orders/line_item_notes_modal', locals: {note_type: 'stylist_notes', notes_content: stylist_notes}
            %br
          -if @view_flag
            %button.btn.btn-default.btn-xs{"data-target" => "#modal_adjustment-#{@item.id}", "data-toggle" => "modal", type: "button"} Adjustment
            .modal.fade{id: "modal_adjustment-#{item.id}", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
              .modal-dialog{:role => "document"}
                .modal-content
                  .modal-header
                    %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"}
                      %span{"aria-hidden" => "true"} &times;
                    %h4#exampleModalLabel.modal-title
                      Create Negative Adjustment For Design Id : #{item.design_id}
                  .modal-body
                    .adjustment_form
                      = render partial: '/orders/create_adjustment' ,:locals => {order_number: @order.number,designer_order: designer_order,item_cost: item_vendor_cost,design_id: item.design_id,quantity: item.quantity, item_id: item.id}

          / CHECK IF ORDER ISSUE PRESENT AND NOT MARKED CLAIMED
          -if item.claim_flag? && @order.state == 'sane'
            = button_to "Mark For Claim",admin_create_claim_design_path(:order_id => @order.id , :line_item => item.id,:design_id => item.design.id),:remote => true,:class => 'btn-danger', :id=> 'claimbtn',method: :post
          %br
          -if (item.status != 'cancel' && designer_order.state != 'canceled' ) || item.issue_status == 'Y'
            = render partial: 'orders/create_issue_modal', locals: {item: item, button_class: 'btn-xs', order_id: @order.id}
          %br
          - if item.stitching_required == 'Y' && item.stitching_done != 'Y'
            = link_to 'Remove Stitching Required', 'javascript:void(0)', data: {item_id: item.id, view_flag: true}, class:'remove-stitching-required' ,id: "remove-stitching-required-#{item.id}"

          %br
          -if @order.best_shipper.present? || @order.courier_company.present?
            .div{id: 'item_check_'+item.id.to_s}
              -if item.sent_to_invoice.present?
                .span.label.label-xs.label-success Checked
                -if item.check_items_done_by.present?
                  = "by #{@account_details.try(:[], item.check_items_done_by).to_a[0]} at #{item.check_items_done_at.strftime('%Y-%m-%d %H:%M')}"
              -else
                = link_to 'Check Item','javascript:void(0)',class: 'btn btn-xs btn-primary checkItem', :"data-item" => item.id.to_s unless (['canceled','vendor_canceled'].include? designer_order.state) || (item.status == 'cancel')
            %div{id: 'check_items_done_'+item.id.to_s}
          - if item.available_in_warehouse?
            .label.label-success{style: 'color:black;font-size:14px;', id: "warehouse_sor_tag_#{item.id}"}= 'Used From Warehouse Inventory'
          - if item.return.try(:support_reason).present?
            %br
            %br
            %p.refund-reason= "Refund Reason: #{item.return.support_reason}"

        %td
          %span= text_field_tag 'quantity_' + item.id.to_s, item.quantity, :size => 1, :class => "form-control"
          %br
          = link_to 'update', 'javascript:void(0)', :class => "update_line_item", :"data-item" => item.id.to_s, style: status_of_item
          - if item.design.variants_available
            - quantity = 0
            - item.design.variants.each {|v| quantity += v.quantity}
            %span='( Variants ' + quantity.to_s + ' avbl)'
          - else
            %span='(' + item.design.quantity.to_s + ' avbl)'
          
        %td
          -show_currency_as_order_country = (current_account.has_support_role? or current_account.is_ops_support_user?) && item.scaling_factor != 1.0
          - if item.snapshot_discount_price.present?
            - if item.snapshot_discount_price == item.snapshot_price(RETURN_NORMAL)
              - if show_currency_as_order_country
                %hr{:style => 'margin:0px'}
                = 'Scaled Price: ' + get_price_in_currency_for_roles(item.price)
              - else
                =get_price_in_currency_for_roles(item.price(RETURN_NORMAL))
            - else
              = 'Discount Price: '+ get_price_in_currency_for_roles(item.snapshot_discount_price)
              %hr{:style => 'margin:0px'}
              = 'Actual Price: '+ get_price_in_currency_for_roles(item.snapshot_price(RETURN_NORMAL))
              %hr{:style => 'margin:0px'}
              - if show_currency_as_order_country
                = 'Scaled Price: ' + get_price_in_currency_for_roles(item.price)
                %hr{:style => 'margin:0px'}
              %div= get_price_in_currency_for_roles(item.snapshot_price(RETURN_NORMAL) - item.snapshot_discount_price)
          - else
            - if show_currency_as_order_country
              = 'Scaled Price: ' + get_price_in_currency_for_roles(item.price)
            - else
              = get_price_in_currency_for_roles(item.price(RETURN_NORMAL))
        %td
          = image_tag(item.thumbnail_image, :width => '50px', :height => '50px')
          %br
          = '(now: ' + item.design.human_state_name
          = "#{item.design.current_state_date.strftime('%d-%m-%y') if item.design.current_state_date.present?}"
          = ')'
          %br
          - if item.received == 'Y'
            - if item.received_by.present?
              = "Received by "+item.received_by+' ('+item.received_on.strftime('%a, %e %b')+')'
            - else
              = "Status: received"
          - elsif @view_flag
            - if item.stitching_required == 'Y' || stitching_flag
              - stitching = "Y"
            - if !item.available_in_warehouse && allow_unpacking
              = link_to 'mark received', 'javascript:void(0)', :id => item.id, :data => {:order_no => @order.number, stitching: stitching}, :class => 'mark-item-received',style: status_of_item
          - stitching_measurements = @order.stitching_measurements.select{|sm| sm.line_item_id == item.id}.to_a if item.stitching_required == 'Y'
          -if (rack_code = item.rack_list_code).present?
            %div{style: "padding: 0px 25px; background: #f5f5f5; color: white;border-radius: 5px; box-shadow: 2px 2px 2px gray;"}
              -unless item.available_in_warehouse?
                =link_to "RTV rack: #{rack_code}", rack_code_sticker_url(designer_order.id, rack_code: rack_code, format: 'pdf'), :target => '_blank'
          - if item.stitching_required == 'Y' 
            %br
            %br
            - state_wise_class = {'processing' => 'btn-default', 'rejected' => 'btn-danger', 'phone_call' => 'btn-warning', 'approved' => 'btn-success', 'pending' => 'btn-info'}
            %button.btn.btn-xs{"data-target" => "#modal_measurement-#{@item.id}", "data-toggle" => "modal", type: "button", class: state_wise_class[item.get_measurement_state(stitching_measurements)]} Measurement
            .modal.fade{id: "modal_measurement-#{item.id}", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
              .modal-dialog{role: "document"}
                .modal-content{style: "width: 720px"}
                  .modal-header
                    %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"}
                      %span{"aria-hidden" => "true"} &times;
                    %h4#exampleModalLabel.modal-title Measurement Form
                  .modal-body
                    .measurement_form
                      %ul.nav.nav-tabs
                        -item.quantity.times do |i|
                          %li{class: ('active' if i == 0)}
                            %a{'data-toggle' => 'tab', href: "#stitching_form_#{item.id}_#{i}"} ##{i+1}
                      .tab-content
                        -item.quantity.times do |i|
                          .tab-pane.fade{id: "stitching_form_#{item.id}_#{i}",class: ('active in' if i==0)}
                            - measurement_flow_string = stitching_measurements[i].try(:user_review).try(:[],'system_approved') == 'true' ? stitching_measurements[i].try(:measurement_flow).to_s : ('Processing,' + stitching_measurements[i].try(:measurement_flow).to_s)
                            %p{style:'color:#ce8a31;font-size:17px;font-weight:bold;margin-top:5px;border:2px solid gray;border-radius:5px;padding:5px;'}= measurement_flow_string.gsub(',', ' -> ')
                            - if stitching_measurements[i].present?
                              = "Measurement Done By: #{stitching_measurements[i].measurement_info_done_by}"
                              %span.label.label-danger.label-lg.pull-right{style:'font-size: 18px;'}= stitching_measurements[i].try(:state).try(:titleize)
                              %br
                              = render partial: '/orders/measurement_update_form', locals: {:@stitching_measurement => stitching_measurements[i] , namespace: "stitching_form_#{i}", suggestions: mes_suggestions[stitching_measurements[i].id]}
                            - else
                              = render partial: '/orders/measurement_form', locals: {namespace: "stitching_form_#{i}"}
        %td
          - skip_qc = false
          - if item.qc_done == 'Y'
            - if item.qc_done_by.present?
              - val = (item.qc_status?) ? "<span class='label label-success'>QC Passed<span>".html_safe : "<span class='label label-danger'>QC Failed<span>".html_safe
              = "QC Done: "+ @account_details.try(:[], item.qc_done_by).to_a[0].to_s + ' (' + item.qc_done_on.strftime('%a, %e %b %Y %I:%M %p') + ')'
              - if @view_flag && !item.qc_status?
                %button.btn.btn-default.btn-xs{"data-target" => "#modal_qc_issue-#{@item.id}", "data-toggle" => "modal", type: "button"} QC Fail Upload
                .modal.fade{id: "modal_qc_issue-#{item.id}", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
                  .modal-dialog{role: "document"}
                    .modal-content
                      .modal-header
                        %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                          %span{"aria-hidden" => "true"} &times;
                        %h4.modal-title
                          Scan the QR Code to upload QC images
                          - token = encrypt_string(current_account.id, item.design_id,@order.id)
                          - url = designer_orders_upload_qc_issue_url(token: token)
                          - qr_code = "data:image/svg+xml;base64,#{Base64.encode64(RQRCode::QRCode.new(url).as_svg)}"
                          %img.bar_code{src: qr_code, width: '50%'}

              %h5= val.to_s
            - else
              = "QC Done"
            - if item.issue_status == 'Y'
              - is_bucket_scan_needed = check_if_bucket_scan_needed(item)
              %br
              =link_to 'Do Replacement QC?',  'javascript:void(0)',class: 'do_replacement_qc'
              %br   
              %button.btn.btn-success{type: "button", style: 'display: none;', class: 'order_page_qc_pass', data: {target: (is_bucket_scan_needed ? "#bucket_scan_modal_#{item.id}" : ''), toggle: 'modal', item_id: item.id,designer_order_id: item.designer_order_id} } 
                ='QC Pass'
              - if is_bucket_scan_needed
                = render partial: '/orders/bucket_form', locals: {bucket_check_res: @bucket_check_res, item_id: item.id, order_page: 'true'}                
              %br
              %br
              %button.btn.btn-danger{"data-target": "#Qcmodel_qc_#{item.id}", "data-toggle": "modal", type: "button", style: 'display: none;', class: 'mark-qc-done' + ' mark-qc-done-' + item.id.to_s}
                ='QC Reject'
          - else
            / - if qc_not_required(item)
            /   - skip_qc = true
            /   = "SKIP QC"
            / - else
            -if item.design.skip_qc
              -skip_qc = true
              = "SKIP QC"
            -elsif item.available_in_warehouse || allow_unpacking
              - is_bucket_scan_needed = check_if_bucket_scan_needed(item)
              %br
              %button.btn.btn-success{type: "button", class: 'order_page_qc_pass', data: {target: (is_bucket_scan_needed ? "#bucket_scan_modal_#{item.id}" : ''), toggle: 'modal', item_id: item.id,designer_order_id: item.designer_order_id} } 
                ='QC Pass'
              - if is_bucket_scan_needed
                = render partial: '/orders/bucket_form', locals: {bucket_check_res: @bucket_check_res, item_id: item.id, order_page: 'true'} 
              %br
              %br
              %button.btn.btn-danger{"data-target": "#Qcmodel_qc_#{item.id}", "data-toggle": "modal", type: "button", class: 'mark-qc-done' + ' mark-qc-done-' + item.id.to_s}
                ='QC Reject'
            - elsif ACCESSIBLE_EMAIL_ID['bag_update'].include?(current_account.email) && @order.domestic? && designer_order.inward_bag_id.blank?
              = link_to 'update bag', mark_received_order_path(designer_order), method: :post, class: 'btn btn-primary'
        - is_all_mes_not_approved = (stitching_measurements.present? && stitching_measurements.any?{|sm| !sm.approved?})
        - item_already_stitched = item.already_stitched?
        - if (item.qc_done != 'Y' && skip_qc == false) || item.stitching_required != 'Y' || is_all_mes_not_approved
          - fabric_measurement_style = "display:none"
        - else
          - mark_required_style = "display:none"

        - unless item.fabric_measured_by.present? || item.fabric_measured_on.present?
          - fabric_measurement_required_style = "display:none"
        / for measurement of designs code
        / -if ['Lehenga', 'Saree','SalwarKameez'].include?(@item.design.designable_type)
        /   %td
        /     %button.btn.btn-default.btn-xs{"data-target" => "#modal-#{@item.id}", "data-toggle" => "modal", :type => "button"} Measurement
        /     .modal.fade{:id => "modal-#{@item.id}", :role => "dialog", :tabindex => "-1", style: 'color:black !important;'}
        /       .modal-dialog{:role => "document"}
        /         .modal-content
        /           .modal-header
        /             %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
        /               %span{"aria-hidden" => "true"} &times;
        /             %h4#exampleModalLabel.modal-title Stitching Measurement
        /           .modal-body
        /             -if @item.design.designable_type == 'Saree' || @item.design.designable_type == 'Lehenga'
        /               -type = 'Blouse'
        /             -elsif @item.design.designable_type == 'SalwarKameez'
        /               -type = 'Dress'
        /             - if (@stitching_measurement=StitchingMeasurement.where(line_item_id: item.id).first).present?
        /               =render partial: '/orders/edit_measurement_form', locals: {type: type}
        /             - else
        /               =render partial: '/orders/measurement_form', locals: {type: type}
        %td{:style => fabric_measurement_style, :class => 'fabric_measured_class_'+item.id.to_s}
          - if item.fabric_measured_by.present? || item.fabric_measured_on.present?
            - if item.fabric_measured_by.present?
              = "Fabric Measurement Done: "+ @account_details.try(:[], item.fabric_measured_by).to_a[0].to_s + ' (' + item.fabric_measured_on.strftime('%a, %e %b') + ')'
            - else
              = "Fabric Measurement Done"
          - else
            = link_to 'Mark Fabric Measurement Done', 'javascript:void(0)', :data => {order_no: @order.number, item_id: item.id, view_flag: @view_flag}, :class => 'mark-fabric-measurement-done' + ' mark-fabric-measurement-done-' + item.id.to_s,style: status_of_item

        %td{:style => fabric_measurement_required_style, :class => 'measurement_confirmed_class_'+item.id.to_s}

          - if item.measuremnet_confirmed == 'Y'
            - if item.measuremnet_received_by.present?
              = "Measurement Confirmed: "+ @account_details.try(:[], item.measuremnet_received_by).to_a[0].to_s + ' (' + item.measuremnet_received_on.strftime('%a, %e %b') + ')'
            - else
              = "Measurement Confirmed"
          - else
            -if item.line_item_addons.any?{|lia| lia.status == 'unpaid'}
              Addon Payment Pending
            -else
              = link_to 'Mark Measurement Confirmed', 'javascript:void(0)', :data => {:order_no => @order.number, :item_id => item.id, view_flag: @view_flag}, :class => 'measurement-confirmed-required' + ' measurement-confirmed-required-' + item.id.to_s,style: status_of_item
        - if (item.fabric_measured_by.present? || item.fabric_measured_on.present? || item_already_stitched)
          %td
            -if @view_flag && ['buyer_returned'].exclude?(designer_order.state)
              - if item.stitching_required == 'Y' && item.line_item_addons.find{|lia| lia.try(:addon_type_value).try(:name).try(:downcase) == 'petticoat stitching'}.present?
                = render partial: '/orders/dynamic_modal', locals: {addon_type_value: 'petticoat', item: item, status_of_item: status_of_item}
              - elsif item.stitching_required == 'Y' && item.line_item_addons.find{|lia| lia.try(:addon_type_value).try(:name).try(:downcase) == 'shapewear'}.present?
                = render partial: '/orders/dynamic_modal', locals: {addon_type_value: 'shapewear', item: item, status_of_item: status_of_item}
            -if designer_order.state != 'buyer_returned'
              %button.btn.btn-default.btn-xs{"data-target" => "#modal-#{@item.id}", "data-toggle" => "modal", :type => "button", style:"#{status_of_item};background-color:white"} Stitching sent
            .modal.fade{:id => "modal-#{@item.id}", :role => "dialog", :tabindex => "-1", style: 'color:black !important;'}
              .modal-dialog{:role => "document"}
                .modal-content{style: "width: 720px"}
                  .modal-header
                    %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
                      %span{"aria-hidden" => "true"} &times;
                    %h4#exampleModalLabel.modal-title Tailoring Details
                  .modal-body
                    - count = 1
                    -if @item.design.designable_type == 'Saree'
                      -type = 'Blouse'
                      -count = 2
                    -elsif @item.design.designable_type == 'SalwarKameez'|| @item.design.designable_type == 'Lehenga'
                      -type = 'Dress'
                    - if (@tailoring_info_data = item.tailoring_info).present?
                      -@tailoring_info_data.each do |t_info|
                        = render partial: '/orders/tailor_info_update_form', locals: {type: type, t_info: t_info}
                        %br
                        - count-=1
                    -if true#DISABLE_ADMIN_FUCTIONALITY['order_page_tailor_assign_and_update']
                      - (1..count).each do |i_info|
                        =render partial: '/orders/tailor_info_form', locals: {type: type}
                        %br
            - if @tailoring_info_data.present?
              Tailoring Status:
              -tailoring_sent = @tailoring_info_data.collect{ |t| t.tailoring_material if t.tailoring_material.present? }.compact
              -tailoring_received = @tailoring_info_data.collect(&:material_received_status).compact - [false]
              Tailor Names: #{@tailoring_info_data.collect(&:tailor_name).join(', ')}
              %br
              -type = @item.design.designable_type == 'Saree' || @item.design.designable_type == 'Lehenga' ? 'Blouse' : 'Dress'
              - if type == 'Blouse'
                - if tailoring_sent.length == 2
                  %br All sent
                - elsif tailoring_sent.length == 1
                  %br 1 material sent
                - if tailoring_received.length == 2
                  %br All received
                - elsif tailoring_received.length == 1
                  %br 1 material received
              - else
                - if tailoring_sent.length == 1
                  %br All Sent
                - if tailoring_received.length == 1
                  %br All Received
        %td{:style => (item_already_stitched ? '' : fabric_measurement_required_style), :class => 'stitching_sent_class_'+item.id.to_s}
          - if item.stitching_done == 'Y'
            - if item.stitching_done_by.present?
              = "Stitching Done: "+ @account_details.try(:[], item.stitching_done_by).to_a[0].to_s + ' (' + item.stitching_done_on.strftime('%a, %e %b') + ')'
            - else
              = "Stitching Done"
          - elsif (@tailoring_info_data.present? && !@tailoring_info_data.any?{|t| t.material_received_status.nil?}) || item_already_stitched
            - stitching_done_rack = rack_list.present? ? rack_list.code : nil
            = link_to 'Mark Stitching Done', 'javascript:void(0)', :data => {:order_no => @order.number, :item_id => item.id, view_flag: @view_flag, stitching_done_rack: stitching_done_rack}, :class => 'mark-stitching-done' + ' mark-stitching-done-' + item.id.to_s,style: status_of_item
        
        %td{:style => mark_required_style, :class => 'stitching_required_class_'+item.id.to_s}
          - if item.stitching_required == 'Y'
            = 'Marked Stitching Required'
          - elsif ['Saree', 'Lehenga', 'SalwarKameez', 'Kurti'].include?(item.design.designable_type)
            = link_to 'Mark Stitching Required', 'javascript:void(0)', :data => {:order_no => @order.number, :item_id => item.id, qc: skip_qc, view_flag: true}, :class => 'mark-stitching-required' + ' mark-stitching-required-' + item.id.to_s,style: status_of_item
          -if item.replacement_item_id.present?
            %h5
              .label.label-pickedup Replacement : #{item.replaced_product.design_id}

        %td{id: 'status_'+item.id.to_s}
          -if show_status == 'Canceled'
            .label.label-cancel=show_status
          -elsif show_status == 'Buyer Returned'
            .label.label-cancel_complete=show_status
          -if designer_order.state != "canceled" && designer_order.line_items.size > 1
            = link_to "javascript:void(0)", :class => "remove_line_item", :"data-item" => item.id.to_s, :"data-design_id" => item.design.id, :"date-order-no" => @order.number  do
              = image_tag('cross.png', :alt => 'remove', :style => status_of_item, id: 'state_'+item.id.to_s , :size => '15x15')
        %td
          -if item.received && (item.item_details['fake_rack'].present? || (rack_list.present? && designer_order.rack_code.present?))
            %div{id: "rack_sticker_link_#{item.id}"}
              %span= link_to 'Rack Code Sticker', rack_code_sticker_url(designer_order.id,design_id: item.design_id, fake_rack: 'Y', format: 'pdf'), target: '_blank'
          -if designer_order.ship_to == 'mirraw'
            %div{id: "unpack_label_link_#{item.id}"}
              %span= link_to 'Unpacking Label', line_item_label_path(item_id: item.id, format: 'pdf', label_type: 'unpacking', orientation: 'landscape', from_order_page: 'true'), target: '_blank'
            %hr
          -if ACCESSIBLE_EMAIL_ID['remove_sor_tag'].to_a.include?(current_account.email) && item.available_in_warehouse
            %button.btn.btn-danger.btn-xs{:id => "remove_sor_tag-#{item.id}", class: 'remove_sor_tag' ,data: {item_id: item.id }} Remove SOR Tag
            %hr
          -elsif ACCESSIBLE_EMAIL_ID['remove_sor_tag'].to_a.include?(current_account.email) && (item.design.in_stock_warehouse.present? || (item.variant.present? && item.variant.in_stock_warehouse.present?))
            %button.btn.btn-success.btn-xs{:id => "add_sor_tag-#{item.id}", class: 'add_sor_tag' ,data: {item_id: item.id }} Add SOR Tag
            %hr
          - if item.stitching_required == 'Y' && item.line_item_addons.none?{|lia| lia.status == 'unpaid'} && stitching_measurements.present?
            = link_to "Stitching Measurement URL", stitching_measurement_label_path(item_id: item.id, format: 'pdf')
            - if item.tailored_measurement.try(:stitching_label_url)
              = link_to 'QC Measurement URL', item.tailored_measurement.try(:stitching_label_url)
            %br
          - if (@addons = item.get_addons)
            - total_addons_price = 0
            - @addons.each do |addon|
              - if addon.snapshot_discount_price.present?
                - addon_price = addon.snapshot_discount_price * item.quantity
              - else
                - addon_price = addon.snapshot_price * item.quantity
              - total_addons_price += addon_price
              - if addon_price !=0 
                = (addon.try(:addon_type_value).try(:name).try(:camelize)).to_s + ' - '+ get_price_in_currency_for_roles(addon_price)
              - else
                = (addon.try(:addon_type_value).try(:name).try(:camelize)).to_s + ' - Free'
              %br
              = " Notes: " + (addon.notes? ? addon.notes : 'none')
              %hr{:style => 'margin:0px'}
        %td
          - if (line_item_shipment = @order.shipments.select{|sh| sh.id  == item.shipment_id}.first) && line_item_shipment.number.present?
            = link_to line_item_shipment.shipper.name + ' ' + line_item_shipment.number, shipment_url(item.shipment_id)
      - if @order.state == 'sane' && Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(item.design.designable_type) && item.stitching_required == 'Y' && item.has_addon_type_value_group?('standard_stitching')
        %tr
          %td{colspan: 9}
            %h4
              - if @stitching_measurement.try(:measurement_info_done_by) == 'System'
                .label.label-success Notice : Stitching Measurement Taken by System
              - elsif @stitching_measurement.try(:stitching_label_url).blank?
                .label.label-warning Notice : Take Measurement from User Form
      - if item.issue_status == 'Y' && item.issue_created_at.present? && item.issue_created_at < 3.days.ago 
        %tr
          %td{colspan: 9}
            %h4
              .label.label-warning{style: 'color: black;'} Alert : The Issue on Item #{item.design_id} is not resolved for last #{distance_of_time_in_words(Time.now, item.issue_created_at)}. Please resolve it at earliest.
      - if item.available_in_warehouse? && item.received != 'Y' && item.status != 'cancel' && designer_order.ignore_states.exclude?(designer_order.state)
        %tr
          %td{colspan: 9}
            %h4{id: "warehouse_receive_#{item.id}"}
              .label.label-success Notice : Item #{item.design_id} Available In Warehouse. Collect it from  #{item.rack_list_code} and scan the barcode here
              %button.btn.btn-default.btn-xs{"data-target" => "#mark_receive-#{@item.id}", "data-toggle" => "modal", :type => "button"} Mark Assigned
              .modal.fade{:id => "mark_receive-#{@item.id}", :role => "dialog", :tabindex => "-1", style: 'color:black !important;'}
                .modal-dialog{:role => "document"}
                  .modal-content
                    .modal-header
                      %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
                        %span{"aria-hidden" => "true"} &times;
                      %h4.modal-title Scan the barcode
                    .modal-body
                      =form_tag :packages_mark_warehouse_received,method: :get,class: 'mark_warehouse_package_assigned',data: {id: @item.id} do
                        =hidden_field_tag :line_item_id, item.id
                        =hidden_field_tag :variant_id,item.variant_id
                        =text_field_tag :barcode,nil,class:'form form-control',placeholder: 'Enter Barcode from the design here'
                        %br
                        =submit_tag 'Submit',class: 'btn btn-success'
              %br
              %br
              -if ACCESSIBLE_EMAIL_ID['remove_sor_tag'].to_a.include?(current_account.email)
                %button.btn.btn-danger.btn-xs{:id => "mark_sor_lost-#{item.id}", class: 'mark_sor_lost' ,data: {item_id: item.id, account_id: current_account.id }} Mark SOR LOST

    = render partial: '/orders/qc_modal', locals: {item: item, order_number: @order.number, order_page: 'true'}
:css
  .issue-radio-inline > input[type='radio'] { 
    transform: scale(2);
  }

:javascript
  $(document).ready(function() {
    MR.designerOrders.init();
  });