:css
  .alertify-message{
    color: #000;
  }

%script{:src => "https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"}

= javascript_include_tag 'alertify.min.js','admin', 'order_qc_check', 'datatables/jquery.dataTables.min','datatables/dataTables.bootstrap.min', 'bootstrap'

= stylesheet_link_tag 'alertify.core.css', 'alertify.bootstrap3.css', 'alertify.default.css', 'dataTables.bootstrap.min'
- disable_properties = ['pickedup', 'pending']
- if ACCESSIBLE_EMAIL_ID['pending_mark_access'].to_a.include?(current_account.email)
  - disable_properties.delete('pending')
- if @order.present? && @order.international? && !@order.check_weight_and_dimension 
  - disable_properties << ["dispatched"]
- line_items = @order.line_items
- stitching_order = line_items.any?{|l| l.stitching_required == 'Y'}
- design_ids = line_items.collect(&:design_id)

-@email_ids||={}
.designer_orders  
  %table.table
    %tr
      = hidden_field_tag 'stitching_order',stitching_order
      = hidden_field_tag 'design_ids',design_ids
      %td{:width => "20%"}      
        - otext = "Order # " + @order.id.to_s
        - prioritized_designer_orders = @order.designer_orders.to_a.group_by(&:update_designer_order_position)
        - prioritized_designer_orders.map{|k,v| [k,v.size]}.to_h.each do |position, count|     
          .btn{class: "#{position == 1 ? 'btn-danger' : (position == 2 ? 'btn-warning' : 'btn-success')}"}
            %span.badge{style: 'color:black;'}= count
        %h3
          %span{:class => "label label-#{@order.state}", :id => "order_status_#{@order.id}"}= @order.state.humanize
        %br
        - if @order.payment_state
          ="PAYMENT: " + @order.payment_state
        - if @order_count.to_i > 0
          %h3
            %span.label.label-info= "Repeat"
        %br
        %span= link_to otext, "#{root_url}"'admin/order/' + @order.id.to_s
        %br
        %span= link_to "edit", "#{root_url}"'admin/order/' + @order.id.to_s + "/edit", class: "label label-default"
        / %span= link_to "edit", edit_order_path(@order), class: "label label-default"
        / -if (CurrencyConvert.get_commercial_countries.keys.exclude? @order.country.try(:downcase))
        /   %span= link_to "invoice", invoice_url(@order.number, :edit => 1), :target => '_blank', class: "label label-default"
        -if @order.best_shipper.present? || @order.courier_company.present?
          %span= link_to "Order Check Label", check_order_label_url(@order.number, format: 'pdf'), :target => '_blank', class: "label label-default"
        - if @view_flag && @order.country_code.present? && @order.state_code.present? && @order.confirmed_at.present? && ADMIN_PANEL_ACCESS["label_access"].to_a.include?(current_account.email)
          %h4
            -if @order.shipments.present?
              -if (shipment = @order.shipments.select{|sh| !sh.designer_order_id? && sh.shipper.name.upcase == 'DHL'}.last).present?
                %p= link_to 'DHL Label', shipment.label.url,target: '_blank', class: "label label-default"
              %p= link_to 'Combined Labels', combined_sticker_url(@order.id),target: '_blank', class: "label label-default"  
            %p#best_shipper_link
              -if (@order.best_shipper.try(:downcase) == 'ups').present?
                %p= link_to '[ups_automated]', ups_invoice_url(@order.number), target: '_blank', id: 'ups', class: 'label label-default'
              -elsif (best_shipper = @order.best_shipper.try(:downcase)).present? 
                - parameterized_best_shipper = best_shipper.parameterize('_')
                - courier_url = get_courier_invoice_url(parameterized_best_shipper, @order.number)
                = link_to "[#{parameterized_best_shipper}_automated]", courier_url, target: '_blank', id: parameterized_best_shipper, class: "label label-default" if courier_url
          / %span=  link_to "[fedex_manual]", invoice_url(@order.number, :edit => 1, :fedex => 1), :target => '_blank', class: "label label-default"
          / %span= link_to "[fedex_pdf]", invoice_url(@order.number, :fedex => 1, :format => 'pdf'), :target => '_blank', class: "label label-default"
          %span= link_to "label", label_url(@order.number, :format => 'pdf'), :target => '_blank', class: "label label-default"
 
        %br 
        %div.order_detail{:style=> "line-height: 30px;"}
        - if @location.blank?
          = "NOT SERVICEABLE"
        - else
          - if @location.cbd == 'Y'
            = "Gharpay Is Available"
          - if @location.cod == 'Y' 
            = "COD Is Available"
        %br
        = "Number : "
        = link_to @order.number.to_s, order_url(@order.number), :target => '_blank'
        %br
        = "Order through Referral : " + @order.referral.present?.to_s.capitalize
        %br
        = "Expected Delivery Date: #{ expected_delivery_date(@order) }"
        %br
        = "Created : " + @order.created_at.in_time_zone('Mumbai').strftime('%d/%m/%Y %H:%M')
        - if @order.confirmed_at.present?
          %br
          = "Confirmed : " + @order.confirmed_at.strftime('%d/%m/%Y %H:%M')
        - if @order.pickup.present?
          %br
          =  "Pickup : " + @order.pickup.strftime('%d/%m/%Y %H:%M')
        - if @order.ready_for_dispatch_at.present?
          %br
          =  "Ready for Dispatch : " + @order.ready_for_dispatch_at.strftime('%d/%m/%Y %H:%M')
        %br
        .pay_type{style: "font-size:15px;font-weight:bold;color:#4CAF50",:class => 'order_pay_type_'+@order.id.to_s}= "Payment type : " + @order.pay_type
        - if @order.express_delivery.to_i > 0
          = 'Shipping + Express Cost : ' + get_price_in_currency_for_roles(@order.shipping)
          %br
          = 'Express Delivery Charge : ' + get_price_in_currency_for_roles(@order.express_delivery)
        - else
          = 'Shipping Cost : ' + get_price_in_currency_for_roles(@order.shipping)
        - if @order.discount.present?
          %br
          = "Discounts: " + get_price_in_currency_for_roles(@order.discount + @order.additional_discount + @order.wallet_discount(@order.currency_rate))
        %br
        ="Addon charges: " + get_price_in_currency_for_roles(@order.mirraw_addon_charges)
        %br  
        = "Total Tax: " + get_price_in_currency_for_roles(@order.total_tax)
        .cod_charge{:class => 'order_cod_charge_'+@order.id.to_s}
          - if @order.cod?
            ="COD Charge : " + get_price_in_currency_for_roles(@order.cod_charge)
        .total{:class => 'order_total_' + @order.id.to_s}
          = "Total : " + get_price_in_currency_for_roles(@order.total)
        = "Paid : " + get_price_in_currency_for_roles(@order.paid_amount)
        %br
        = "Referral Amount Used : " + "#{@order.currency_code} #{@order.referral_discount.to_f}  ---  INR #{(@order.referral_discount.to_f * @order.currency_rate.to_f).round(2)}"
        %br
        = "Refund Amount Used : " + "#{@order.currency_code} #{@order.refund_discount.to_f}  ---  INR #{(@order.refund_discount.to_f  * @order.currency_rate.to_f).round(2)}"
        %br
        = "Currency Conversion rate : " + "#{@order.currency_rate}"
        %br
        = "Credit Amount : " + get_price_in_currency_for_roles(@order.credit_amount)
        %br 
        = "Quantity Count = " + @order.total_items_quantity.to_s
        %br
        = "Line Item Count = " + @order.total_line_items.to_s
        %br
        = "AppSource = " + @order.app_source.to_s

        - if @order_dispute
          %hr
          - @order_dispute.slice(:status, :dispute_id, :dispute_amount, :currency_code).each do |key, val|
            Order dispute #{key}: #{val.to_s}
            %br
          %hr

        %br
        - if @order.payment_gateway.present?
          = "Payment Gateway = " +  @order.payment_gateway.to_s
        - if %w(accounts accounts_admin admin support).include?(current_account.role.try(:name))
          - %w(paypal_txn_id amazon_order_id paytm_txn_id razorpay_id g2a_txn_id).each do |attr_name|
            - if (attr_value = @order[attr_name]).present?
              %br
              = "#{attr_name.titleize}= #{attr_value}"
              %br
          ="Additional Payments" if @order.additional_payments.present?
          -@order.additional_payments.each do |additional_payment|
            - %w(paypal_txn_id amazon_order_id paytm_txn_id razorpay_id bank_deposit_txn_id).each do |attr_name|
              - if (attr_value = additional_payment[attr_name]).present?
                %br
                = "#{attr_name.titleize}= #{attr_value}"
                %br
        - if %w(accounts accounts_admin).include?(current_account.role.try(:name))
          -if @order.other_details.present? && (key=@order.other_details.keys.find{|k| k.to_s.include? 'proforma_invoice'}).present?
            =link_to key, @order.other_details[key], target: '_blank'
          %br
          -if @order.order_notification.present? && (key=@order.order_notification.keys.find{|k| k.to_s.include? 'invoice'}).present?
            Invoice :
            =link_to key, @order.order_notification[key], target: '_blank'
        - tags = @order.tag_list.reject{|tag| tag.include?('convert-mkt')}.join(',')
        .tags{:class => @order.id}= tags.present? ? "Tags: #{tags}" : "Tags: none"
        - if @order.ccavenue_payment_link.present?
          .ccavenue_payment_link
            [NEW CCAVENUE CBP LINK] Ccavenue Payment Link:
            %a{:href => @order.ccavenue_payment_link}= "#{@order.ccavenue_payment_link}"
        %br
        - if @order.items_received_status
          .items_received_status{:id => @order.number}= "All Items Received = Yes"
          .items_received_on{:id => @order.number}= 'Items Received On = ' + @order.items_received_on.strftime('%a, %e %b %I:%M %P')
        - else
          .items_received_status{:id => @order.number}= "All Items Received = No"
          .items_received_on{:id => @order.number}=''
        -if (['support', 'outsourced_support'].include?(get_current_role) || current_account.is_super_sales_team?) && @old_wallet_transactions.present? && @wallet.present?
          / -user.wallet.wallet_transactions.last(10)
          / -old_wallet_transactions = WalletTransaction.where(wallet_id: @order.wallet_transactions.first.wallet_id).preload(:order).order('created_at desc').paginate(page: params[:page], per_page: 10)
          -columns = ['created_at', 'state', 'return_amount', 'referral_amount', 'fund_type']
          #wallet_history.btn.btn-primary{"data-target": "#wallet-history-modal", "data-toggle": "modal", type: "button", style: ""}
            User wallet history
          #wallet-history-modal.modal.fade{class: "Wallet_#{@order.id}","aria-labelledby": "QcmodelLabel", role: "dialog", tabindex: "-1"}
            .modal-dialog{role: "document" , style: "color: black;"}
              #Qc-model-content.modal-content{style:"margin-left:10% !important; width:1000px !important; background: white !important"}
                .modal-header
                  %button.close{"aria-label": "Close", "data-dismiss": "modal", type: "button"}
                    %span{"aria-hidden": "true"} &#215;
                  %h4#QcmodelLabel.modal-title User wallet history
                  %br
                  Referral Amount
                  = @wallet.total_amount(:referral)
                  %br
                  Return Amount
                  = @wallet.total_amount(:return)
                  %br
                  Total Balance
                  = @wallet.total_amount
                  %br
                  Expiration Date:
                  = @wallet.referral_expires_at.present? ? @wallet.referral_expires_at.strftime("%A, %d %B, %Y %I:%M %p %Z ") : 'nil'
                .modal-body
                  .row
                    -columns.each do |column|
                      .col-md-2
                        %h5=column
                    .col-md-2
                      %h5= 'Order Number'

                  -@old_wallet_transactions.each do |wt|
                    -if wt.order.present?
                      .row{style: 'height: 40px; border-bottom: 1px solid black;margin-top: 5px;'}
                        -columns.each do |column|
                          .col-md-2
                            = column == 'created_at' ? wt.send(column).to_date : wt.send(column)
                        .col-md-2
                          =wt.order.number
                =will_paginate @old_wallet_transactions
                .modal-footer
        - if @order.gharpay?
          %br
          = @order.gharpay_order_id? ? "Gharpay: " + @order.gharpay_order_id : "Gharpay: None."
          %br
          = @order.gharpay_status? ? "Gharpay Status: " + @order.gharpay_status : "Gharpay Status: None"

        -if @returns && @order.international? && (['accounts' , 'accounts_admin','super_admin'].include?get_current_role)
          %h5 RETURNS
          -@returns.each_with_index do |return2,i|
            = link_to "Return No.#{i+1}" , return2.return_invoice_url
        %hr
        = form_tag order_add_notes_path, :class => "order_add_notes", :style => "padding:0px" do
          = hidden_field_tag 'order_id', @order.id, :class => 'form-control order_id_add_notes'
          = text_field_tag 'notes_id', '', placeholder: "enter notes",  :class => "form-control add_notes"
          %br
          = submit_tag 'Add notes', :class => "button-small btn-sm btn btn-default"
        %br

        = form_tag order_add_tags_path, :class => "order_add_tags", :style => "padding:0px" do
          = hidden_field_tag 'order_id', @order.id, :class => 'order_id_add_tags'
          - if DEPARTMENT_WISE_TAGS.keys.include?(get_current_role)
            = select_tag 'add_tags',options_for_select(DEPARTMENT_WISE_TAGS[get_current_role].sort).downcase, prompt: 'Select Tag to ADD', :class => "form-control add_tags"
            %br
            = text_field_tag 'add_tags_reason', '', placeholder: 'enter reason', class: 'form-control add_tags_reason'
          -else
            = text_field_tag 'add_tags', '', required: true, :class => "form-control add_tags"
          %br
          = submit_tag 'Add Tags', :class => "button-small btn-sm btn btn-default add_tags_button"
          -if @order.tag_list.present? && DEPARTMENT_WISE_TAGS.keys.include?(get_current_role)
            %br
            %br
            - list = @order.tag_list.sort.collect{|tag| [tag,"-"+tag]}
            = select_tag 'add_tags', options_for_select(list), prompt: 'Select Tag to Remove', class: 'form-control remove_tags'
            %br
            = submit_tag 'Remove Tags', class: 'button-small btn btn-sm btn-danger remove_tags_button'
        %br

        = form_tag order_add_design_path, :class => "order_add_design", :style => "padding:0px" do
          = hidden_field_tag 'order_id', @order.id, :class => 'order_id_add_design'
          = text_field_tag 'add_design', '', :class => "form-control add_design"
          %br
          = submit_tag 'Add design', :class => "button-small btn-sm btn btn-default" 

      %td
        - if @order.present?
          %span{:style => "margin-top:-20px;font-size:14px;"}
            .row
              .col-md-8
                -if @order_dispute && !@order_dispute.resolved?
                  %h3
                    .label.label-danger{style: 'color:black;'} ORDER DISPUTE #{@order_dispute.dispute_id}
                %h3
                  Shipping Detail:
                  #best_shipper_notice.label.label-dispatched=@order.best_shipper
                  -if @order.confirmed_at.present? && @order.international?
                    - total_paid_amount = @order.get_user_total_paid_amount
                    -if total_paid_amount >= 50000 
                      #shipment_tag.label.label-warning{style: 'color: black'} E-WayBill
                    -if total_paid_amount >= (CSB_LIMITS[@order.best_shipper.to_s] || 25000)
                      #shipment_tag.label.label-warning{style: 'color: black'} Cargo Shipment
                  -if @order.other_details.present? && @order.other_details['blacklist_user'].present?
                    #best_shipper_notice.label.label-danger CUSTOMER IS BLACKLISTED
              .col-md-4
                - if @order.stylist_id.present?                  
                  %center
                    %h3 
                      Stylist:
                      .label.label-info{style: 'color:black;'}= @order.stylist.try(:name).try(:titleize)
                    - measurements_count = get_orders_measurements_state_wise_count(@order.stitching_measurements)
                    - state_wise_class = {'processing' => 'btn-default', 'rejected' => 'btn-danger', 'phone_call' => 'btn-warning', 'approved' => 'btn-success'}
                    - measurements_count.each do |state, count|     
                      .btn{class: state_wise_class[state]}
                        %span.badge{style: 'font-size:medium;'}= "#{state.split('_').map(&:first).join.upcase}-#{count}"
            %hr
            -if ADMIN_PANEL_ACCESS["order_detail_access"].to_a.include?(current_account.email)
              %h4#buyer_name
                -if @order.name.humanize.split.size > 1
                  =@order.name.humanize
                - else
                  =@order.email.split('@')[0].split('.').join(' ').humanize
              - if ['support'].include?(current_account.role.try(:name)) || ACCESSIBLE_EMAIL_ID['update_phone'].to_a.include?(current_account.email)
                #phone_number
                  = render partial: 'orders/edit_contact', locals: { contact_type: 'phone', order: @order }
                  %br
              - if ['support'].include?(current_account.role.try(:name)) || ACCESSIBLE_EMAIL_ID['email_update'].to_a.include?(current_account.email)
                = render partial: 'orders/edit_contact', locals: { contact_type: 'email', order: @order }
              %br
              #shipping_address
                %h5= @order.shipping_address
                - is_order_international = @order.international?
                - is_order_cod = @order.cod?
                - is_geo_international = @order.actual_country_code != 'IN'
                - is_state_correct = ['ready_for_dispatch','pickedup','dispatched'].include?(@order.state)
                - if ACCESSIBLE_EMAIL_ID['edit_address'].to_a.include?(current_account.email) || ((is_geo_international && !is_order_international && !is_order_cod && is_state_correct) || (is_order_cod && ['pending','confirmed','sane'].include?(@order.state)) || (is_order_international && is_state_correct))
                  %button.btn.btn-default.btn-xs{"data-target" => "#shipping_address_modal", "data-toggle" => "modal", :type => "button"} Edit Address
                  .modal.fade{:id => "shipping_address_modal", :role => "dialog", :tabindex => "-1", style: 'color:black !important;'}
                    .modal-dialog{:role => "document"}
                      .modal-content{style: "width: 720px"}
                        .modal-header
                          %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
                            %span{"aria-hidden" => "true"} &times;
                          %h4#exampleModalLabel.modal-title Update Shipping Address
                        .modal-body
                          = form_tag 'shipping_address_form', onsubmit: "UpdateShippingAddress(#{@order.id})" do
                            = hidden_field_tag 'order',@order.id
                            = "Name:"
                            = text_field_tag 'Name' ,@order.name, placeholder: "enter name", :class => "form-control",required:true
                            = "Street:"
                            = text_field_tag 'Street',@order.street, placeholder: "enter street",  :class => "form-control",required:true
                            = "City:"
                            = text_field_tag 'City',@order.city, placeholder: "enter city",  :class => "form-control",required:true
                            = "Pincode:"
                            = text_field_tag 'Pincode',@order.pincode.to_s, placeholder: "enter pincode",  :class => "form-control",required:true
                            -if DEPARTMENT_HEAD_EMAILS['support'] == current_account.email
                              = "Phone:"
                              = text_field_tag 'phone',@order.phone.to_s, placeholder: "enter phone",  :class => "form-control",required:true
                              = "Billing Phone:"
                              = text_field_tag 'billing_phone',@order.billing_phone.to_s, placeholder: "enter phone",  :class => "form-control",required:true
                            - if !is_order_international && @shipping_states.present?
                              = hidden_field_tag 'Country',@order.country
                              = "Buyer State:"
                              = select_tag 'BuyerState',options_for_select(@shipping_states,@order.buyer_state), placeholder: "enter buyer state",  :class => "form-control",required:true
                            - if is_order_international || !is_order_cod
                              = 'State Code'
                              = text_field_tag 'StateCode',@order.state_code, placeholder: 'enter state code', class: 'form-control', maxlength: 3,required:true
                              - if is_order_international
                                = 'Country:'
                                = text_field_tag 'Country',@order.country, placeholder: 'enter country', class: 'form-control'
                                = 'Buyer State:'
                                = text_field_tag 'BuyerState',@order.buyer_state, placeholder: 'enter buyer state', class: 'form-control',required:true
                              = 'Country Code'
                              = text_field_tag 'CountryCode',@order.country_code, placeholder: 'enter country code', class: 'form-control', maxlength: 2,required:true


                            = submit_tag 'Submit',id: 'shipping_submit', style:'margin-top:10px', class: "btn btn-success"
              -show_customer_email = ACCESSIBLE_EMAIL_ID['order_email_show_role_ids'].to_a.include?(current_account.role_id)
              -if show_customer_email
                %br
                -(@email_ids['Customer']||={})["Customer : #{@order.name.titleize}"] = @order.email      
              %hr
  
              %h3 Billing Detail:
              %hr
              %h4=@order._billing_name.humanize
              - if (['support', 'stylist'].include?(current_account.role.try(:name))) || current_account.is_super_sales_team?  || (['sales'].include?(current_account.role.try(:name)) && ['pending', 'cancel', 'followup'].include?(@order.state))
                %br
                = "Phone: "
                = link_to @order._billing_phone, 'http://www.truecaller.com/search/?country=India&q=' + @order._billing_phone, :target => '_blank'
              %br
              = @order.billing_address
            %br
            = link_to  "Duplicate Orders: ", orders_url(duplicate: 't', number: @order.number)
            
            -if ACCESSIBLE_EMAIL_ID['can_edit_user_phone'].to_a.include?(current_account.email)
              %hr
              #update_user_phone{style: "width: 50%;"}
                %h3 User Phone:
                - phone = @order.try(:user).try(:account).try(:phone)
                *For Domestic users only*
                #update-message.hide
                = form_tag :admin_update_user_phone, remote: true, method: :post do
                  .row
                    .dial_code{style: 'width: 60px;display: inline-block; margin-left: 15px;'}
                      = text_field_tag 'user_dial_code', '+91', class: 'form-control', readonly: true
                    .phone{style: 'width: 250px; display: inline-block;'}
                      = text_field_tag 'user_phone', phone, placeholder: 'Enter 10 digit mobile number', class: 'form-control', maxlength: 10
                      = hidden_field_tag 'user_id', @order.user_id
                  .row
                    .col-md-10
                      = check_box_tag :send_reset_password_sms
                      = label_tag :send_reset_password_sms, "Send reset password link via sms"
                  = submit_tag 'Add / Update', id: 'update_phone_submit', style:'margin-top:10px', class: "btn btn-success"
              %hr
        %br
        - @seller_out_of_stock_items = {}
        - sold_out_flag = false
        - needs_stitching = 0
        - stitching_done = 0
        - @order.designer_orders.each do |des_order|
          - des_order.line_items.each do |lt|
            -if des_order.state != 'canceled'
              -if lt.stitching_required == 'Y'
                -needs_stitching +=1
              -if lt.stitching_done == 'Y'
                -stitching_done +=1
            - if (design = lt.design).present?
              - if ['seller_out_of_stock', 'delete'].include?(design.state)
                -(@seller_out_of_stock_items[des_order.designer.name]||=[]) << design
              - if ['sold_out','seller_out_of_stock'].include?(design.state)
                - sold_out_flag = true
        - if @seller_out_of_stock_items.present?
          - oos = true
        - else
          - oos = false
        %br
          %div.row
            %div.col-md-12
              %ul.list-inline{style:"line-height:30px;"}
                %li= link_to 'Payu/Ccavenue', 'javascript:void(0)', class: "multiple_local_invoice_links", data:{:oos => oos}, id:"payu_" + @order.number.to_s
                - if @order.state.in?(['new', 'pending', 'followup'])
                  %li= link_to 'Paypal', 'javascript:void(0)', :class => "paypal_invoice_link", :id => "paypal_" + @order.number.to_s, :data => {:oos => oos}
                %li= link_to 'Cash on delivery', 'javascript:void(0)', :class => "convert-to-cod", :data => {:order_id => @order.id}
                %li= link_to 'OrderAck', 'javascript:void(0)', :class => "order-ack-email", :id => "Ack_Email_" + @order.number.to_s
                / %li= link_to 'Gharpay', 'javascript:void(0)', :class => "convert-to-gharpay", :id => "gharpay_" + @order.number.to_s, :data => {:oos => oos}
                %li= link_to 'Initiate_Return', new_return_case_path(@order.number), :target => '_blank'
                / %li= link_to 'Duplicate', 'javascript:void(0)', :class => 'duplicate_order', :id => @order.number
                %li= link_to 'New vendor issue', 'javascript:void(0)', :class => "new-vendor-issue", :id => @order.number
                %li= link_to 'Min req', 'javascript:void(0)', :class => 'mail_action', :id => @order.id, :data => {:request_type => 'min_req'} if @order.total < MIN_INTERNATIONAL_ORDER and @order.international?
          = render :partial => '/orders/items_out_of_stock'
          - if @order.international? && @order.sane?
            = link_to 'See Department Wise Products Status', orders_path(region: 'inter', state: 'sane', view: 'list', number: @order.number), target: '_blank'
          %hr
            = @order.number + " : "
            - @order.designer_orders.each do |dos|
              - if dos.rack_code.present?
                = dos.rack_code +  " / "
              - else
                = dos.id.to_s + " / "
          %hr
          %p="Notes: #{@order[:notes]}"
          -if (wallet_notes = @order.wallet_transaction(@old_wallet_transactions).try(:notes)).present?
            %p="Wallet Notes: #{wallet_notes}"
          %hr
          %p Number of orders placed : #{@total_order_count_number}
          %p Last order number : #{@last_order}
          %hr

          %table#order-notes.table.table-condensed{style: "display: #{@events.present? ? 'block' : 'none'}"}
            %thead
              %tr
                %th.col-md-2 Date
                %th.col-md-2 Name
                %th.col-md-1 Type
                %th.col-md-7 Note
              %tr
                %td
                - 3.times do
                  %td.filter
            %tbody
              -@events.to_a.each do |event|
                %tr
                  %td.col-md-2=event.event_timestamp.strftime('%d, %b %y %I:%M %p')
                  %td.col-md-2=event.done_by
                  %td.col-md-1=event.note_type
                  %td.col-md-7.text-justify=event.notes
          -@order.other_details.keys.select{|i| i[/_status$/]}.each do |key|
            ="#{key.titleize} : #{@order.other_details[key]}"
            %br
          = render :partial => '/orders/shipments'
          -if @order.stitching_measurements.present?
            %hr
              %div{style: "color: blue"}
                =button_to 'Send Stitching Info', stitching_measurement_mailer_path(order_id: @order.id), method: :post,disable_with: "Please Wait"
          -if @order.sane? && !@order.standard_addon? && @order.stitching_addon?('true') && @order.stitching_form_required?
            %br
              %div{style: "color: blue"}
                =button_to 'Send Stitching Form', stitching_measurement_form_mailer_path(order_number: @order.number), method: :post, data: {disable_with: "Please Wait"}
          -if (outbound_coupons = @order.outbound_coupons).present? && (%w(accounts accounts_admin support super_admin admin).include? current_account.try(:role).try(:name))
            %br
            %hr
            -returns = Hash[@order.returns.select{|r| outbound_coupons.map(&:id).include? r.coupon_id}.map{|r| [r.coupon_id, r.id]}]
            .panel.panel-info
              .panel-heading{style: 'padding: 1px'}
                %h3.panel-title.text-center.text_black Coupons Given
              .panel-body{style: 'background: #151515;padding: 0px;'}
                %table.table
                  %tr
                    %th Code
                    %th Applied On
                    %th
                    %th Used
                    %th Expired
                    %th
                    %th Return Id
                  -outbound_coupons.each do |coupon|
                    %tr
                      %td=coupon.code
                      %td
                        -if (applied_order = coupon.coupon_used_on).present?
                          =link_to applied_order.number, order_order_detail_path(applied_order.number)
                      %td='Converted To Refund' if coupon.converted_to_refund
                      %td=(coupon.use_count.to_i == coupon.limit.to_i ? 'YES' : 'NO')
                      %td=activation=(coupon.live? ? 'NO' : 'YES')
                      %td
                        = button_to "Extend Validity ?",extend_coupon_path(coupon_id: coupon.id),class: 'btn-link', method: :post,data: { confirm: 'Are you sure?', disable_with: 'Please Wait...' } if activation == 'YES' && coupon.use_count < coupon.limit
                      %td
                        -if (return_id = returns[coupon.id] || coupon.return_id).present?
                          =link_to return_id, return_path(return_id), target: '_blank'
      %td{:width=> "20%"}
        -if @order.cod? && %w(super_admin accounts_admin operations support admin).include?(current_account.role.try(:name))
          %button.btn.btn-xs{"data-target" => "#modal_#{@order.id}", "data-toggle" => "modal", type: "button", style: "color: black; background-color: white"} Order Summery
          .modal.fade{:id => "modal_#{@order.id}", :role => "dialog", :tabindex => "-1", style: 'color:black !important;', data:{backdrop: 'static', keyboard: 'false'}}
            .modal-dialog{:role => "document"}
              .modal-content{style: 'width: 180%; margin-left: -245px;'}
                .modal-header
                  %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"} 
                    %span{"aria-hidden" => "true"} &times;
                  %h4#exampleModalLabel.modal-title Orders Summary
                .modal-body
                  =render partial: '/orders/order_summary'
        - if GIFT_ORDER['enable'] && @order.get_user_total_paid_amount && @order.get_user_total_paid_amount >= GIFT_ORDER['value'].to_i && @order.international?
          %h3{class: "h2"}
            %span{class: "label label-warning"}  FREE GIFT
        - if @order.app_name == 'luxe'
          %h3
            %span{class: "label btn-warning"}  Luxe Site Order
        - elsif luxe_order(@order)
          %h3
            %span{class: "label label-info"}  Mirraw Premium Order
        - if @order_risk.present?
          %hr
          %h4 Risk Factor: #{@order_risk.risk_factor}
        %hr
        %button.btn.btn-default{"data-target" => "#send_email", "data-toggle" => "modal", type: "button"}Contact via Email
        -if ORDER_SMS_EMAIL.to_a.include?(current_account.email)
          %button.btn.btn-default.btn-xs{data: {target: '#order_sms', toggle: 'modal', type: 'button'}} Send SMS
        %hr         

        %div
          -urgent_style = @order.tag_list.to_s.include?("urgent") ? "block" : "none"
          #urgent_label.label.label-BAD{style: "font-size: 20px; display: #{urgent_style}"}  
            URGENT  
          -if ['admin','accounts_admin'].include? current_account.role.try(:name)
            - unless (@order.tag_list.to_s.include?("urgent") || ['sane','dispatched','partial_dispatch','pickedup','ready_for_dispatch'].exclude?(@order.state)) 
              %button{id: "mark_urgent" ,class: "urgent_order_button btn btn-sm btn-danger", data:{id: @order.id}}
                MARK URGENT
            %hr
          %br
          -if %w(super_admin accounts_admin support admin operations outsourced_support senior_vendor_team accounts sales stylist).include?(current_account.role.try(:name))
            =render partial: '/orders/raise_internal_ticket'
            %br
          -if @order.tickets.present?
            = link_to 'View Internal Tickets',tickets_index_path(order_number: @order.number),class: 'btn btn-sm btn-success',style: 'margin: 10px 0px;',target: '_blank'
        %br
        - if !@order.international? || (@order.international? && (@order.partial_dispatch? || (@order.actual_weight.present? && @order.volumetric_weight.present?)))
          -if !@order.cod? || (@order.cod? && @order.international?)
            = form_tag get_best_shipper_path, class: "form-inline", remote: true, :method => :get do
              = hidden_field_tag 'order_id', @order.id
              = label_tag 'Weight'
              = number_field_tag 'weight', @order.actual_weight, min: 0, required: true, class: "form-control", step: 0.01
              %br
              %br
              = submit_tag 'submit', class: "btn btn-default btn-sm"
              %br
          - if @view_flag && @order.international? 
            %hr
            = form_tag get_volumetric_weight_path, class: "form-inline",remote: true, :method => :get do
              = hidden_field_tag 'order_id', @order.id
              = label_tag 'L:'
              = number_field_tag 'length', nil, min: 0, required: true, class: "form-control",style: 'width: 70px;', :step => 1
              = label_tag 'B:'
              = number_field_tag 'breadth', nil, min: 0, required: true, class: "form-control",style: 'width: 70px;', :step => 1
              %br
              %br
              = label_tag 'H:'
              = number_field_tag 'height', nil, min: 0, required: true, class: "form-control",style: 'width: 70px;', :step => 1
              = label_tag 'V.W='
              #volume_weight.label.label-default{style: 'font-size:16px;margin-left:5px;'}=@order.volumetric_weight
              %br
              %br
              = submit_tag 'Get Volumetric Weight', id: 'order_volumetric_weight', class: "btn btn-default btn-sm"
          %hr
        .modal.fade{id: "modal_sane_orde", role: "dialog", tabindex: "-1", style: 'color:black!important;',data: {backdrop: "static",keyboard:"false" }}
          .modal-dialog{role: "document"}
            .modal-content
              .modal-header
                %button.close{"aria-label": "Close", "data-dismiss": "modal", type: "button"}
                  %span{"aria-hidden": "true"} &times;
                %h4#exampleModalLabel.modal-title
                  Enter Transaction Id
              .modal-body
                -pay_type_attributes = [[BANK_DEPOSIT,'bank_deposit_txn_id'],[PAYPAL,'paypal_txn_id'],['PAYU','payu_mihpayid'],[PAY_WITH_AMAZON,'amazon_order_id'],[PAYTM,'paytm_txn_id'],['Razorpay','razorpay_id'],['Duplicate Order','duplicate_order'],['G2A','g2a_txn_id']]
                .form.form-inline
                  .text-center  
                    #txn_id_notice.alert-danger
                    %br
                    = select_tag 'pay_type_for_transaction_id', options_for_select(pay_type_attributes), class: 'form-control'
                    = text_field_tag 'transaction_id','',placeholder: 'Transaction Id',required: true,class: 'form-control'
                    = hidden_field_tag :order_number_for_transaction,@order.number
                    = submit_tag 'Mark Order Sane',class: 'btn btn-primary form-control',id: 'mark_sane_with_transaction_id'
        = form_tag order_event_trigger_path, id: 'order_change_state', :class => 'order_event' do
          -extra_class = 'order_state'
          = hidden_field_tag 'order_id', @order.id, :class => 'order_id'
          = hidden_field_tag 'form_transaction_id'
          = hidden_field_tag 'source_order_id'
          - tags = @order.tag_list.reject{|tag| tag.include?('convert-mkt')}.each {|d| d.downcase!}
          - if @order.tracking_number.present?
            %p= @order.courier_company.to_s + ' - ' + @order.tracking_number.to_s
          -if @view_flag || (@order.cod? && @order.confirmed? && ACCESSIBLE_EMAIL_ID['cod_sane'].to_a.include?(current_account.email))
            - if @order.tracking_number.present?
              %br
                - if disable_properties.present?
                  .alert.alert-warning{style: "color:black;"} Please fill weight and dimensions before dispatch
                = select_tag "state", options_for_select(Order.state_machine.states.map{|x|[x.name.to_s.humanize, x.name]},selected: @order.state, disabled: disable_properties), :class => "state form-control #{extra_class}"
              %br
            - elsif (tags.include?('oos') || tags.include?('issue') || tags.include?('wait') || tags.include?('addon') || tags.include?('shipment_error') || tags.include?('stitching') || tags.include?('tailoring') || tags.include?('fnp') )
              %p You cannot dispatch this order since it has one or more tags (OOS, Issue, Wait, Addon, Stitching, Tailoring, FNP, Shipment Error)
              = select_tag "state", options_for_select(Order.state_machine.states.map{|x|[x.name.to_s.humanize, x.name]}, disabled: ["pickedup","ready_for_dispatch","dispatched"] + disable_properties), :class => "state form-control #{extra_class}"
              %br
            - elsif needs_stitching != stitching_done
              %p You cannot dispatch this order since it has 'stitching required marked' and products havent been stitched
              = select_tag "state", options_for_select(Order.state_machine.states.map(&:name),disabled: ["pickedup","ready_for_dispatch","dispatched"] + disable_properties), :class => "state form-control #{extra_class}"
              %br
            - else
              = label_tag 'Courier'
              - if @order.international?
                = select_tag "Courier", options_for_select(SHIPPER_LIST_INTERNATIONAL), :class => "courier form-control"
              - else
                = text_field_tag 'Courier', '', class: "form-control"
              = label_tag 'Tracking number'
              = text_field_tag 'Tracking_number', '', class: "form-control"
              %br
                - if disable_properties.present?
                  .alert.alert-warning{style: "color:black;"} Please fill weight and dimensions before dispatch
                = select_tag "state", options_for_select(Order.state_machine.states.map{|x|[x.name.to_s.humanize, x.name]}, selected: @order.state, disabled: disable_properties), :class => "state form-control #{extra_class}"
              %br
          -else
            = select_tag "state", options_for_select(['cancel','cancel_complete'],@order.state),prompt: '--select--',required: true, :class => "state form-control #{extra_class}"
            %br

          - if sold_out_flag
            .warning{:style=>"color:red;"}
              Warning: Seller Out Of Stock Or Sold Out Products Present in the order
          .order_cancel_reason
            = label_tag :reason, 'Select Reason'
            = select_tag :reason, options_for_select(Order::ADMIN_ORDER_CANCEL_REASON), prompt: '--- select reason ---', class: 'form-control reason', style: 'display: block'
          %span.button-medium
            = submit_tag 'submit', class: "btn btn-default btn-sm btn_change_order_state", data: {disable_with: 'Please Wait...'}
          %hr
        = render :partial => 'orders/returned_products'
        / = render :partial => 'orders/coupon'
        = render :partial => '/orders/designer_issue'
        = render :partial => 'orders/returns'
        = render partial: 'orders/additional_payments'
        %hr
        - coupon = @order.coupon
        - if ['new','pending','confirmed', 'followup'].include?(@order.state) && coupon.nil?
          %h4 Apply Coupon
          = form_tag apply_coupon_on_order_path, class: 'form',method: :post do
            = hidden_field_tag :order_id,@order.id
            .form-group
              = text_field_tag :coupon_code,'',placeholder: 'Enter Coupon Code',class: 'form-control'
            .form-group
              = submit_tag 'Apply Coupon' , class: 'form-control'
        - if coupon.present?
          ="Used Coupon : #{coupon.code}"

    = render :partial => '/orders/designer_orders', :locals => {:order => @order, prioritized_designer_orders: prioritized_designer_orders}
    = render partial: '/orders/send_email'

  :javascript
    $(document).on("submit",'#change_state', function(e){
      e.preventDefault();
      location.reload();
    });

.modal.fade{id: 'order_sms', role: 'dialog', tabindex: '-1', style: 'color:black !important;'}
  .modal-dialog{role: 'document'}
    .modal-content{style: 'width: 720px'}
      .modal-body
        %button.close{'aria-label' => 'Close', 'data-dismiss' => 'modal', :type => 'button'} 
        =form_tag send_order_sms_order_path(@order), class: '', style: '', id: 'order_sms_form' do
          .row
            .alert{style: 'display:none'}
          .row
            .col-md-9=select_tag :type, options_for_select(ORDER_MESSAGES.keys), prompt: 'Select SMS type', class: 'form-control'
            .col-md-3=submit_tag :send, class: 'btn btn-primary btn-block'