- @order.designer_orders.each do |designer_order|
  #designer_order_items.panel
    .panel-heading
      = link_to designer_order.designer.name.upcase, designer_path(designer_order.designer)
      - if @order.country == 'India'
        - designer_order_state = designer_order.state.downcase == 'completed' ? '- Dispatched' : "- #{designer_order.state}"
        = link_to designer_order_state.upcase, pages_faq_path(category: ORDERING_OF_FAQ['Order'][0]), :target => '_blank'
    .panel-content
      #designer_order_group  
        -if @show_domestic_pipeline && designer_order.state != 'buyer_returned'
          %br
          .status_pipeline
            = render partial: 'orders/domestic_order_status',locals: {designer_order: designer_order}

        %br
        %table{style: 'width: 100%'}
          -line_items = designer_order.line_items
          -line_items.each do |item|
            -design = item.design
            %tr
              %td{style: 'background: none'}
                #details_strip
                  %table{style: 'width: 100%'}
                    %tr
                      %td{style: 'width: 18%', rowspan: 2}
                        #item_image
                          = link_to image_tag(item.image(:small), alt: item.title), designer_design_path(design.designer, design),target:'_blank' if item.image(:small)
                      %td{style: 'width: 32%'}
                        #item_price
                          .product_title
                            = link_to truncate(design.title.titleize, length: 25),designer_design_path(design.designer, design)
                          .product_details
                            Price :
                            - item_price = cod_check ? @order.international_cod_price(item.price) : item.price 
                            -price = get_actual_price_in_currency_with_symbol(item_price, @order.currency_rate, @order.currency_code)
                            -@symbol = price.split(' ')[0]
                            =price
                          .product_details  
                            Quantity :
                            = item.quantity
                          .product_details
                            -if ['cancel','buyer_return'].include?(item.status) || ['canceled', 'vendor_canceled'].include?(designer_order.state)
                              %span.label.label-warning State : #{item.status == 'buyer_return' ? 'Returned' : 'Canceled'}
                            -else
                              Notes : 
                              - if controller_name == 'orders' && action_name == 'show'
                                - note_without_bmgn = item.note_without_bmgn
                                = note_without_bmgn.present? ? note_without_bmgn : ' - '
                              - else
                                = item.note? ? item.note : " - "
                              -if item.combo_variant.present?
                                =","
                                = combo_variant_option_details(item)
                      %td{style: 'width: 50%'}
                        #item_addons
                          - if (@addons = get_addons(item.id))
                            = render :partial => 'orders/order_addon', locals: {item: item,cod_check: cod_check}
                          - else
                            %span.sub_total.span-8.sub
                              .span-3
                                Sub Total : 
                              .span-4
                                = "#{@symbol} #{(get_actual_price_in_currency(item_price, @order.currency_rate) * item.quantity)}"
                        -if !@order.international? && item.return.present?
                          = render :partial => '/line_items/return_status', locals: {line_item: item}
                      -if item.present?
                        - if @order.state == 'sane'
                          - form_items = []
                          - item.get_forms.each do |key, value|
                            - if value.present?
                              - form_link,key_text = '',''
                              - case key
                              - when :blouse
                                - form_link = stitching_form_path(order_number: @order.number,design_id: item.design_id,product_designable_type: 'lehenga_choli')
                                - key_text = 'Click Here For Blouse / Lehenga Choli Form'
                              - else
                                - des_type = ['kurti','islamic'].include?(item.design.designable_type.try(:downcase)) ? 'kurti' : 'anarkali'
                                - form_link = stitching_form_path(order_number: @order.number,design_id: item.design_id,product_designable_type: des_type)
                                - key_text = 'Click Here For Dress/ Suit/ Anakarli/ Kurti/ Salwar Kameez Form'
                              - form_items << [key_text, form_link]
                          - unless form_items.blank?
                            - form_items.each do |form_item|
                              %tr
                                %td
                                %td.form_links{colspan:"2"}
                                  =link_to "<span class='stitching_form_link #{item.stitching_measurements.length >= item.quantity ? 'filled' : ''}'>#{form_item[0]}</span>".html_safe, form_item[1], target: '_blank'
                    - if @order.international? && design.designable_type != 'Kurti' && design.get_design_addons(@actual_country, @symbol).to_a.present? && item.line_item_addons.any?(&:unstitched?)
                      %tr
                        %td{colspan: 3}
                          .notification{style: 'padding-left: 18px;'}
                            - is_item_added = @order.order_notification['require_custom_stitching_item'].to_a.include?(item.id)
                            - if @order.sane? && !is_item_added
                              = check_box_tag :post_order_stitching_option, item.id, false, class: 'post_order_stitching_options', id: "post_order_stitching_option_for_#{item.id}"
                              = label_tag "post_order_stitching_option_for_#{item.id}", 'Add custom stitching'
    .tracking_details{style:"text-align:center;padding-bottom: 1%;"}
      - if @show_domestic_pipeline && designer_order.state != 'buyer_returned' && designer_order.shipment.present? and designer_order.shipment.service_type == 'ClickpostForward'
        = link_to "Track", "https://mirraw.clickpost.in/page?waybill=#{designer_order.shipment.number}" ,class: 'button-block-info btn btn-success', style: 'width: 130px', target: '_blank'

#post_order_stitching_submit
  = submit_tag 'Pay for Stitching on the selected items.'

:css
  .stitching_form_link {
    padding: 0.6em 1em;
    color: #fff;
    background: #670b19;
  }
  
  .stitching_form_link.filled {
    background: #808080;
  }

  .stitching_form_link:hover {
    font-weight:bold;
  }