!!!
%html{:xmlns => "//www.w3.org/1999/xhtml", "xml:lang" => "en", :lang => "en", "xmlns:fb" => "//ogp.me/ns/fb#"}
  %head
    %title= content_for(:title) || @seo_title || 'Artificial Jewellery Online, Buy Fashion, Imitation Jewellery Online India'
    - keywords = content_for(:keywords) || @seo_keywords || 'Mirraw, Mirraw online shopping'
    - description =  content_for(:description) || @seo_description || 'Find wide range of fashion jewellery, imitation, bridal, artificial, beaded and antique jewellery online. Buy Designer Sarees & Bags. Buy imitation jewellery online from designers across India. Call us on +91-8080 781 780 now to resolve your queries.'
    - description = "" if description == 'blank'
    - keywords = "" if keywords == 'blank'
    %meta{:name => 'keywords', :content => keywords }
    %meta{:name => 'description', :content => description }
    %meta{content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0", name: "viewport"}
    %meta{:name => 'google-site-verification', :content => 'agapK5s18B0nYsx87rslHH_kTY2Uxa_WuGeZ8uGtxas'}
    %meta{:content => "ae162c57511278e951259fb168ee2257", :name => "p:domain_verify"}
    %meta{:content => "2a662054611db26020810ba3279e4d02", :name => "p:domain_verify"}
    %meta{:content => "C73FA91FB27EB19AF53A38C69211F328", :name => "msvalidate.01"}

    - og_title = content_for(:title) || @seo_title || 'India’s Largest Online Ethnic Store'
    - og_image = content_for(:og_image) || @og_image || asset_url('logo-red.png')
    -# - og_url =  content_for?(:og_url) ? content_for(:og_url) : '//www.mirraw.com'
    - og_type = content_for(:og_type) || @og_type || 'website'

    %meta{:property => "og:title", :content => og_title }
    %meta{:property => "og:type", :content => og_type}
    %meta{:property => "og:url", :content => request.url}
    %meta{:property => "og:image", :content => og_image }
    %meta{:property => "og:site_name", :content => "Mirraw.com"}
    %meta{:property => "og:description", :content => description }
    %meta{:property => "og:locale", :content => "en_US" }
    %meta{:property => "fb:admins", :content => "506263136"}
    %meta{:property => "fb:app_id", :content => Mirraw::Application.config.fb_app_id }
    /Twitter Card data
    %meta{:name => "twitter:card", :content => "summary_large_image"}
    %meta{:name => "twitter:site", :content => "@MirrawDesigns"}
    %meta{:name => "twitter:creator", :content => "@blacklife"}
    /Twitter app Card data
    %meta{:name => "twitter:app:country", :content => "IN"}
    %meta{:name => "twitter:app:name:iphone", :content => "Mirraw"}
    %meta{:name => "twitter:app:id:iphone", :content => "1112569519"}
    %meta{:name => "twitter:app:url:iphone", :content => request.url}
    %meta{:name => "twitter:app:name:googleplay", :content => "Mirraw.com"}
    %meta{:name => "twitter:app:id:googleplay", :content => "com.mirraw.android"}
    %meta{:name => "twitter:app:url:googleplay", :content => request.url}
  
    - if (request.env['HTTP_HOST'].downcase != 'www.mirraw.com')
      %meta{content: "NOINDEX,NOFOLLOW", name: "robots"}
    - elsif ((params[:action] == "landing" && params[:landing] == "Rakhi-2016") || (params[:controller] == "store" && params[:action] == "online") || (params[:controller] == "users" && (params[:action] == "show" || params[:action] == "designers")) || check_for_collection_noindex || check_for_landings || (params[:controller] == 'pages' && params[:action] == 'price_match_guarantee_tnc')) || params[:kind] == 'direct_dollar' || (params[:controller] == "store" && params[:action] == "tags1")
      %meta{content: "NOINDEX,NOFOLLOW", name: "robots"}
    - elsif @facet_properties.to_a.length > 2 || (params[:controller] == "store" && params[:action] == "catalog2" && params[:id].present? && params[:page].present? && params[:page] > 1) || (params[:controller] == "reviews" && params[:action] == "site_review" && params[:page].present? && params[:page] > "1")
      %meta{content: "NOINDEX,FOLLOW", name: "robots"}
    - else
      %meta{:content => "index, follow, max-image-preview:large, max-snippet:-1", :name => "robots"}

    %meta{:content => "text/html; charset=utf-8", "http-equiv" => "Content-Type"}
    
    = favicon_link_tag '/apple-touch-icon-144x144.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '144x144'
    = favicon_link_tag '/apple-touch-icon-114x114.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '114x114'
    = favicon_link_tag '/apple-touch-icon-72x72.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '72x72'
    = favicon_link_tag '/apple-touch-icon.png', rel: 'apple-touch-icon', type: 'image/png'
    
    %link{:rel => 'icon', :type => 'image/x-icon', :href => '/red-favicon.ico' }

    /Canonical Links
    /***********************************************************************************************
    - request_path = @canonical_path || request.path
    - if (params[:controller] == 'store' && params[:action] == 'catalog2') || (params[:controller] == 'reviews' && params[:action] == 'site_review')
      - request_params = params.dup
      - request_params[:page] = (request_params[:page].presence || 1).to_i + 1
      %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}?page=#{request_params[:page]}",rel: "next"}
      - request_params[:page] = request_params[:page].to_i - 1
      -if request_params[:page] == 1
        %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}",rel: "canonical"}
      -elsif request_params[:page] == 2
        %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}?page=#{request_params[:page]}",rel: "canonical"}
        %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}",rel: "prev"}
      -else
        %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}?page=#{request_params[:page]}",rel: "canonical"}
        - request_params[:page] = request_params[:page].to_i - 1
        %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}?page=#{request_params[:page]}",rel: "prev"}        
    -else
      %link{href: "#{request.protocol}#{DESKTOP_SITE_URL}#{request_path}",rel: "canonical"}
    /***********************************************************************************************

    %script{:type => "application/ld+json"}
      {
      "@context" : "https://schema.org",
      "@type" : "Organization",
      "name" : "Mirraw",
      "url" : "https://www.mirraw.com",
      "contactPoint" : [{
      "@type" : "ContactPoint",
      "telephone" : "#{MIRRAW_CONTACT_INFO}",
      "contactType" : "Customer Service"
      }],
      "logo" : "#{asset_url('logo-red.png')}",
      "sameAs" : [
      "https://www.facebook.com/MirrawDesigns",
      "https://twitter.com/MirrawDesigns",
      "https://www.instagram.com/mirraw"
      ]
      }
    /[if lt IE 8]
    //= stylesheet_link_tag 'blueprint/ie'
    /[if lt IE 7]
      %style(type="text/css")
        #container {display:table;height:100%}
    - if (params[:controller]  == 'store') && (params[:action] == 'full_image')
      %script{:type => "application/ld+json"}
        {
        "@context" : "https://schema.org",
        "@type" : "Website",
        "URL" : "https://www.mirraw.com",
        "potentialAction" : [{
        "@type" : "SearchAction",
        "target" : "https://www.mirraw.com/search?utf8=✓&q={search_term_string}",
        "query-input" : "required name=search_term_string"
        },
        {
        "@type" : "SearchAction",
        "target" : "android-app://com.mirraw.android/mirraw/search?utf8=✓&q={search_term_string}",
        "query-input": "required name=search_term_string"
        }]
        }

    = include_gon
    %style
      = Rails.application.assets['critical_inline_red.css'].to_s.html_safe
      //= Sprockets::Railtie.build_environment(Rails.application, true)['critical_inline.css'].to_s
    - if Rails.env.production?
      / Google Tag Manager
      :javascript
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-M5TVG6S');
      / End Google Tag Manager

    :javascript
      function addEvent(t,n,e){return t.attachEvent?t.attachEvent("on"+n,e):t.addEventListener(n,e,!1)};function loadScript(e,t,a){var n;n=document.createElement("script"),n.type="text/javascript",n.readyState?n.onreadystatechange=function(){("loaded"===n.readyState||"complete"===n.readyState)&&(n.onreadystatechange=null,0!=a&&a())}:n.onload=function(){0!=a&&a()},n.src=e,0!=t&&n.setAttribute(t,""),document.getElementsByTagName("head")[0].appendChild(n)};function loadScriptSync(url){var script;script=document.createElement("script"),script.type="text/javascript",script.readyState?script.onreadystatechange=function(){("loaded"===script.readyState||"complete"===script.readyState)&&(script.onreadystatechange=null,loadJsArr.length>1?(loadJsArr.shift(),callbackArr.forEach(function(cb){cb[1]===url[0]&&eval(cb[0])}),loadScriptSync(loadJsArr[0])):(loadJsArr=[],callbackArr=[]))}:script.onload=function(){loadJsArr.length>1?(loadJsArr.shift(),callbackArr.forEach(function(cb){cb[1]===url[0]&&eval(cb[0])}),loadScriptSync(loadJsArr[0])):(loadJsArr=[],callbackArr=[])},script.src=url[0],document.getElementsByTagName("head")[0].appendChild(script)};var loadJsArr=[],callbackArr=[];Array.prototype.forEach||(Array.prototype.forEach=function(r,t){for(var o=0,a=this.length;o<a;++o)r.call(t,this[o],o,this)});


    - if false
      -#onlineseles.ai tracking js
      -#= render :partial => '/layouts/online_sales_designs'
    - if false
      / = javascript_include_tag "//cdn.optimizely.com/js/2226520484.js"
      = javascript_include_tag "//ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"
    - request_url = StringModify.string_utf8_clean_without_space(request.fullpath)
    = csrf_meta_tags
    - if MOBILE_SEO == 'true'
      %link{href: "#{request.protocol}#{MOBILE_SITE_URL}#{request_url}" , media: "only screen and (max-width: 640px)", rel: "alternate"}

    - if (params[:controller]  == 'store') && (params[:action] == 'full_image' || params[:action] == 'catalog2')
      - request_split = request_url.split("?")
      -# - amp_url = current_page?(root_url) ? "#{request.protocol}#{MOBILE_SITE_URL}/amp" : ((request_split.size == 1) ? "#{request.protocol}#{MOBILE_SITE_URL}#{request_split.first}/amp" : "#{request.protocol}#{MOBILE_SITE_URL}#{request_split.first}/amp?#{request_split.second}")
      -# %link{rel: 'amphtml' ,href: amp_url}

  
  - if @integration_status == "new"
    - class_controller = controller.controller_name + '_' + 'new' + " " + controller.controller_name
  - else
    - class_controller = controller.controller_name
  = render :partial => 'layouts/google_tag_manager'
  = render partial: '/layouts/remarketing_top'
  %body{:class => class_controller} 
    #main-container
      %div#overlay_subscription
      %span{:style => "float:right"}
      %noscript
        .jswarning
          This website will only function properly with JavaScript enabled. Please enable javascript in your browser and then refresh the page.
      - if Rails.env.production?
        %noscript
          %iframe{:height => "0", :src => "https://www.googletagmanager.com/ns.html?id=GTM-M5TVG6S", :style => "display:none;visibility:hidden", :width => "0"}
      = render 'layouts/flash_messages'

      - if CRITEO_ACCOUNT_ID[@country_code].present? && ((controller_name == 'designs'.freeze && action_name == 'show'.freeze && @design.present?) || (controller_name == 'store'.freeze && action_name == 'full_image'.freeze) || (controller_name == 'orders' && (action_name == 'new'.freeze || (action_name == 'show'.freeze && @order.present? && @trackGA))))
        = render partial: 'layouts/criteo_tags'
      .hidden-xs= render :partial => 'layouts/info_strip'
      - if @integration_status == "new"
        = stylesheet_link_tag(critical_pages? ? 'application_critical' : 'application_new')
      - else
        = stylesheet_link_tag 'application'
      = yield :page_specific_css
      
      = javascript_include_tag 'jquery-critical.js'

      = render :partial => '/layouts/ga'
      - if Rails.env.production?
        - unless params[:controller] == "orders" && params[:action] == "show" 
          = render :partial => '/layouts/fb_custom_audience'

      - if Rails.env.production? || Rails.env.staging?
        :javascript
          user1 = "#{@theme_tester.try(:theme_a_b_or_c) || 'A'}";
          ci = "0002319";
          errList = [];
          window.addEventListener('error',function(e){var obj={};try {obj['f']=(e.filename).replace("|","~$");obj['m']=(e.message);obj['l']=(e.lineno);errList.push(obj);} catch(ex){}});
        /= javascript_include_tag "https://cdn.epsilondelta.co/static/gemGen.js", async: true

      - unless current_account.present? && current_account.designer?
        - if browser.device.mobile?
          = render :partial => 'layouts/mobile/menu_header'
        - else
          = render :partial => 'layouts/menu_header'
        = render :partial => 'layouts/menu1'
      - else
        .account-login.pull-right
          =link_to designer_path(current_account.designer),id: 'account-show' do
            = image_tag('user.png', id: 'image-account')
            %span.account-text= "#{account_signed_in? ? 'My Account' : 'Sign In' }"

      - if params[:controller] != 'carts'
        #lightbox

      - if @integration_status == "new"
        = yield :before_main_container
        #content.container-fluid
          - if params[:controller] != 'orders' and params[:controller] != 'carts'
            = render partial: '/carts/cart_header'
          - if notice
            .bs-example.bs-example-bg-classes
              %p.bg-success= notice
          - elsif alert
            .bs-example.bs-example-bg-classes
              %p.bg-warning= alert
          = yield
        .clr
      - else
        #container.container
          - if params[:controller] != 'orders' and params[:controller] != 'carts'
            #header_full_cart.span-24
              .span-24
                .cart_title
                  %span= image_tag('Moneyback.jpg', :size => '120x23', :alt => '100% money back guarantee.')
                  %span= image_tag("FreeShipping2.png",:size => '120x23', :alt => "Free Shipping at Mirraw.com")
                  %span= image_tag('Delivery.jpg',:size => '120x23', :alt => 'Delivery in less than 10 days.')
                  %span= image_tag('Returns.png',:size => '120x23', :alt => "Hassle free returns")
                  -if ['inr', 'rs'].include?(@symbol.downcase)
                    %span= image_tag('Phone1.png',:size => '120x23')
                  -elsif @symbol.downcase == 'cad'
                    %span= image_tag('contact_cad.jpg',:size => '120x23')
                  -elsif @country_code.to_s.downcase == 'gb'
                    %span= image_tag('contact_uk.jpg',:size => '120x23')
                  -else
                    %span= image_tag('contact_int.jpg',:size => '120x23')
                  / %a#close_cart(href="javascript:void(0)") close x
                  %a#close_cart.close_cart(href="javascript:void(0)") close x
                  %span#cart_title_msg(style="margin:30px;")
              .span-24
                .cart_content
                  = render :partial => '/carts/cart'
          .span-24
            #main_page_content
              #main_notice_alert
                - if notice
                  %div.notice= notice
                - elsif alert
                  %div.alert= alert
              = yield
      //= render :partial => '/layouts/currency_set'
      = render partial: '/layouts/remarketing_bottom'
      .progress_img1
        .image_container{style: 'text-align: center;'}
          = image_tag('mirraw-loader1.png', alt: 'loading', width: 96, height: 96, class: 'mirraw-loader')
      - if CRITEO_ACCOUNT_ID[@country_code].present? && (controller_name == 'store'.freeze && (['catalog2'.freeze, 'search1'.freeze, 'collection'.freeze, 'best_sellers'.freeze].to_set.include?(action_name)))
        = render partial: 'layouts/criteo_tags'
      -country_footer = ((['in','ca','gb'].exclude? @country_code.to_s.downcase) ? 'int' : @country_code.to_s.downcase)
      -unless current_account.present? && current_account.designer?
        - if @integration_status == "new"
          - static_footer_url = '/layouts/static_footer'
          - if params[:controller] == 'orders' && params[:action] == 'new'
            = render :partial => static_footer_url
          - else
            - static_footer = RequestStore.cache_fetch("#{static_footer_url}/static_footer_#{country_footer}", :expires_in => 1.day) do
              - render :partial => static_footer_url
            = raw static_footer
        - else
          #footer
            = render :partial => '/layouts/footer'
    - if @integration_status == "new" && subscription_pages?
      //= javascript_include_tag 'subscriptions', defer: true
      - if cookies[:user_subscribed].blank?
        = render partial: 'layouts/subscription'
    
    - unless current_page?(root_url)
      - if critical_pages?
        :javascript
          loadJsArr.push(["#{asset_path('jquery-ui.min.js')}", 2]);
      - else
        = javascript_include_tag 'jquery-ui.min'

    - if current_page?(root_url) || (RESPONSIVE_ACTIONS.keys.include?(params[:controller]) && RESPONSIVE_ACTIONS[params[:controller]].include?(params[:action]))    
      = javascript_include_tag 'application_new'
    - else
      = javascript_include_tag 'application'

    - if @integration_status == "new"
      - if critical_pages?
        :javascript
          loadJsArr.push(["#{asset_path('subscriptions.js')}", 5]);
      - else
        = javascript_include_tag 'subscriptions', defer: true

    %p#back-top
      %a
        %span

    = yield :footer_js
    - unless params[:controller] == "orders" && params[:action] == "show" && Rails.env.production?
      %img{src: "https://sp.analytics.yahoo.com/spp.pl?a=**************&.yp=428972"}
    - if Rails.env.production?
      - if ENABLE_JS == '1'
        / = render :partial => '/layouts/givtertracker'
        / = render :partial => '/layouts/pa'
        / = render :partial => '/layouts/webengage'
      = render :partial => '/layouts/freshdesk' unless (["accounts/sessions", "accounts/registrations","orders"].include?(params[:controller]) && params[:action] == "new")

    = render :partial => '/layouts/ga_bottom'
    /[if lte IE 9]
      %script{:defer => "defer"}
        (function() {
        var methods = ["assert", "clear", "count", "debug", "error", "exception",
        "group",  "info", "log", "select", "table", "time", "trace", "warn"];
        var length = methods.length;
        var console = (window.console = window.console || {});
        var method;
        var noop = function() {};
        while (length--) {
        method = methods[length];
        \// define undefined methods as noops to prevent errors
        if (!console[method])
        console[method] = noop;
        }
        })();

    - if critical_pages?
      :javascript
        var UnbxdSiteName="#{Mirraw::Application.config.unbxd[:UNBXD_SITE_KEY]}";
        loadJsArr.push(['//d21gpk1vhmjuf5.cloudfront.net/unbxdAnalytics.js', 7]);

    - else
      %script{:defer => "defer"}
        var UnbxdSiteName="#{Mirraw::Application.config.unbxd[:UNBXD_SITE_KEY]}";
        var s=document.createElement("script");
        s.src="//d21gpk1vhmjuf5.cloudfront.net/unbxdAnalytics.js";
        s.type="text/javascript";
        document.getElementsByTagName('head').item(0).appendChild(s);

    :javascript
      var global_tracking = {
        country_code: "#{@country_code}",
        ecomm_pagetype: "#{@ecomm_pagetype}",
        ecomm_category: "#{@ecomm_category}"
      }
      var load_now_lazy_custom = function(element) {
        if (element.tagName == 'PICTURE') {
          var children = element.children;
          for(var i = 0; i < children.length; i++) {
            self = children[i];
            if (self.tagName == 'SOURCE' && self.dataset.srcset){
              self.setAttribute('srcset', self.dataset.srcset)
            } else if (self.tagName == 'IMG' && self.dataset.original) {
              self.setAttribute('src', self.dataset.original)
              self.style.display = 'block'
            }
          };
        } else if (element.tagName == 'IMG' && element.dataset.original) {
          element.setAttribute('src', element.dataset.original)
          element.style.display = 'block'
        }
      }
      var assign_laziness = function(no_lazy){
        divs = document.querySelectorAll('picture.lazy-detect,img.lazy-detect');
        for(i = 0; i < divs.length; i++) {
          element = divs[i];
          element.classList.remove('lazy-detect');
          if (no_lazy !== true) {
            var row_number = parseInt((i / 4)) + 1;
            if(row_number <= 2) {
              load_now_lazy_custom(element);
            } else if (row_number <= 6) {
              element.classList.add('lazy-custom');
            } else {
              element.classList.add('lazy');
            }
          } else {
            load_now_lazy_custom(element);
          }
        }
      }
      assign_laziness();
      addEvent(window, 'load', function(){
        if (loadJsArr.length > 0) {
          loadJsArr = loadJsArr.sort(function(a,b){ return a[1]-b[1]});
          loadScriptSync(loadJsArr[0]);
        };
        $(".lazy").lazyload({
          threshold: 900,
          effect: "fadeIn"
        });
      });

    - if ENV['BOXXAI_URL'].present?
      :javascript
        var BOXX_CUSTOMER_ID = "";
        addEvent(window, 'load', function(){
          loadScript("#{ENV['BOXXAI_URL']}", 'async', function(){});
        });

    -if ENV['SS2_KEY'].present?
      #ss_098786_234239_238479_190541
      :javascript
        var __uzdbm_a = '#{@shieldsquare_response.pid}';
        addEvent(window, 'load', function(){
          loadScript("https://cdn.perfdrive.com/static/jscall_min.js", 'async', function(){});
        });

  -if false #ENABLE_WIZGO["enable"] == true && @country_code != 'IN' && !(controller_name == 'orders' && action_name == 'new')  //To continue with whatsapp just uncomment this condition
    %div{ class: 'chat' ,:style =>" position:fixed;bottom: 0; right:0;margin-bottom:1.3%;margin-left:89%"}
      %a.chat{ :href => "https://api.whatsapp.com/send?phone=#{ENABLE_WIZGO["whatsapp_number"]}&text=Hello" , target:'w-icon'  }
        %figure.chat
          %figcaption.chat{:style => 'font-size:15px;font-weight:bold;text-align:left'}Live Chat
          =image_tag 'wl.png', style: 'background:none;', alt: 'w-icon', width: '25%', target: '_blank'

  
  - if ENV['FREDDY_CHATBOT'] == '1' && current_page?(root_url)
    %script{:defer => "defer", :type => "text/javascript"}
      (function (d, w, c) {
      if(!d.getElementById("spd-busns-spt")) {
      var n = d.getElementsByTagName('script')[0],s = d.createElement('script'); 
      var loaded = false;
      s.id = "spd-busns-spt";
      s.src = "https://cdn.freshbots.ai/assets/share/js/fbotsChat.min.js";
      s.setAttribute("data-prdct-hash", "72739a6abc259fc06bd3aae6d959178cf1ee37c7");
      s.setAttribute("data-region", "us");
      s.setAttribute("data-ext-client-id", "759b2b82f27311e8a65c0242f5162543");
      if (c) {
      s.onreadystatechange = s.onload = function () {
      if (!loaded) { c(); }
      loaded = true; 
      };
      }
      n.parentNode.insertBefore(s, n);
      }
      })(document, window);
