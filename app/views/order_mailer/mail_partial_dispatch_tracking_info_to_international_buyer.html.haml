%html
  %body
    %div{:style => "font-family: Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif; color: #000000; font-size: 14px; line-height: 1.5em; padding: 20px;"}
      %p Namaste from Mirraw!
      %p Good news! Some items from your order are ready and have been shipped. 
      %p We’re working hard to process the remaining items and will update you as soon as they are ready to be dispatched.
      %strong Shipped Items:
      %br
      %br
      =render :partial => "partial_dispatch_order_items"
      %br
      - shipper_name = @shipment.shipper.name
      %strong Tracking Details : #{@shipment.shipper.name.capitalize}
      %br
      %br
      %table
        %tbody
          %tr
            %td{:bgcolor => "#FFFFFF", :width => "700"}
              %table
                %tbody
                  %tr
                    %td
                      = "Your order has been shipped by #{shipper_name} with tracking number #{@shipment.number}."
                      %p
                        = link_to "Track Your Order", (@order.get_shipper_tracking_link(shipper_name, @shipment.number)[0].presence || "http://www.track-trace.com/"), target: "_blank"
                      %p
                        You can view complete order here:
                        = link_to @order.number, order_url(:number => @order.number)
      %p We truly appreciate your patience and support. If you have any questions, just reply to this email—we’re here to help!
      %p Best Regards,
      %p Team Mirraw
      %p
        %a{href: "https://www.mirraw.com"} www.mirraw.com
      %p Contact Details:
      %ul{ style: "list-style: none; padding: 0; margin: 0;" }
      -['US', 'GB', 'CA', 'IN'].each do |country_code|
        %li{ style: "list-style: none;" }
          %span{ style: "display:inline-block; width:18px; height:25px; background-image:url(#{wicked_pdf_asset_base64('flags.png')}); background-repeat:no-repeat; background-position:#{get_flag_position(country_code)}px 0px; vertical-align: middle; margin-right: 5px;" }
          = "#{Country.country_name(country_code)}: #{Country.get_helpline_number(country_code)}"
      %li{ style: "list-style: none;" }
        All other countries: +1 949 464 5941
      %br