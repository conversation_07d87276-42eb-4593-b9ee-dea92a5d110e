%html{:xmlns => "http://www.w3.org/1999/xhtml"}
  %head
  %body
    %br
    %br
    %div{:style => "margin:0;padding:0"}
      %table{:align => "center", :cellpadding => "0", :cellspacing => "0", :width => "auto"}
        %tbody
          - if !@after_partial_dispatch
            %tr
              %td{:style => "padding:18px 0px 20px 0px;border-top: 6px solid #b11f2c"}
                %table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "702"}
                  %tbody
                    %tr
                      %td{:align => "left", :style => "padding-bottom:8px;padding-left:20px", :valign => "bottom"}
                        = link_to 'http://www.mirraw.com' do
                          %img{:alt => "Mirraw", :border => "0", :height => "70", :src => "https://www.mirraw.com/assets/logo-red.png", :style => "display:block;margin:0", :width => "150"}/
                      %td{:align => "right", :style => "padding-bottom:29px;padding-right: 20px", :valign => "bottom"}
                        %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:black;font-size:20px;line-height:1em !important"}
                          Tracking Information
                %table{:align => "center", :bgcolor => "#FFFFFF", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "702"}
                  %tbody
                    %tr
                      %td{:colspan => "3"}
                        %img{:alt => "", :border => "0", :height => "5", :src => "http://www.mirraw.com/assets/email_top.gif", :style => "display:block;margin:0", :width => "702"}
                    %tr
                      %td{:bgcolor => "#FFFFFF", :width => "700"}
                        %table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0"}
                          %tbody
                            %tr
                              %td{:style => "padding:20px 0 20px 0"}
                                = render :partial => 'order_header', locals: {track_flag: true}
                            %tr
                              %td{:colspan => "2", :style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif; font-size: 14px;padding:5px 0 20px 20px"}
                                = "Your order has been shipped by #{@order.courier_company} with tracking number #{@order.tracking_number}."
                                %p
                                You can view complete order here:
                                = link_to @order.number, order_url(:number => @order.number)
                                %p
                                Below is the link which would help you to track the order and know the exact status of the order.
                                %br
                                = @order.get_shipper_tracking_link(@order.courier_company, @order.tracking_number)[0].presence || "http://www.track-trace.com/"
                                %p
                                Ideally it takes 5-6 working days for the product to be delivered once it is shipped.
                                %p
                                  ="If you have any questions about this order, feel free to get back. Let us know if you need any help. You can call up at #{@order.contact_number} or email us on "
                                  =@order.international? ? ' <EMAIL> ' : ' <EMAIL> '
                                %br
                                %br

                                = render :partial => 'buyer_order_items'
                                %table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "700"}
                                  %tbody
                                    %tr
                                      %td{:style => "padding-top:15px;border-bottom:1px solid #cccccc;font-size:1px;line-height:1px"}
          -else
            %p Namaste from Mirraw! 

            %p 
              Great news! The remaining products from your order 
              = link_to @order.number, order_url(:number => @order.number)
              have been shipped, and they’ll be arriving at your doorstep soon.

            %p 
              Tracking Details:
              = @order.get_shipper_tracking_link(@order.courier_company, @order.tracking_number)[0].presence || "http://www.track-trace.com/"

            %p We truly appreciate your patience and support. Stay excited—your order is almost there!

            %p If you have any questions, feel free to reach out to <NAME_EMAIL>. We’re happy to assist you!

            %p Best Regards,

            %p Team Mirraw

            %p www.mirraw.com

            %p Contact Us Anytime! 

            %ul{ style: "list-style: none; padding: 0; margin: 0;" }
            -['US', 'GB', 'CA', 'IN'].each do |country_code|
              %li{ style: "list-style: none;" }
                %span{ style: "display:inline-block; width:18px; height:25px; background-image:url(#{wicked_pdf_asset_base64('flags.png')}); background-repeat:no-repeat; background-position:#{get_flag_position(country_code)}px 0px; vertical-align: middle; margin-right: 5px;" }
                = "#{Country.country_name(country_code)}: #{Country.get_helpline_number(country_code)}"
            %li{ style: "list-style: none;" }
              All other countries: +1 949 464 5941
            %br
          %tr
            %td{:colspan => "3"}
              %div{style: 'background-color:#faf0e6;color:black;margin: 0 10px 0 10px;padding: 10px 10px 10px 10px;border: 1px solid #F1B578; border-radius: 2px;',align: "center"}
                Thank you for shopping at Mirraw.
                %br
                %br
                We have an easy exchange/return policy.
                %br
                %br
                %br
                -if @order.user_id.present?
                  =link_to "Return", user_orders_url(@order.user_id),:style => "background-color:#121513; padding: 10px 50px; border-radius: 3%; color: #ececec; text-decoration: none; font-weight: bold;"
                  %br
                  %br
                %tr
                  %td{:colspan => "3"}
                    = render :partial => 'design_suggestions'

            = render :partial => 'mailer_footer'