%table{:align => "center", :border => "0", :cellpadding => "0", :cellspacing => "0", :width => "700"}
  %tbody
    %tr
      %td{:align => "canter", :bgcolor => "#ADB1B1", :style => "text-align: center;padding:6px 0 8px 0px;background:#222426", :valign => "top"}
        %div{:style => "color:white;font-size:13px;font-weight:bold;line-height:1em !important;font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif"}
          - all_line_items = @designer_order.line_items.sane_items
          / - etas = all_line_items.collect {|i| i.eta }
          /-date = @designer_order.confirmed_at.try(:in_time_zone).presence || @designer_order.created_at.in_time_zone
          / = "Items Purchased - To be delivered by " + date.advance(:days => @designer_order.get_vendor_delivery_days).in_time_zone.strftime('%d %b %Y')
          = "Items Purchased - To be dispatched by " + @designer_order.get_vendor_dispatch_days.in_time_zone.strftime('%d %b %Y')
    %tr
      %td{:align => "left", :valign => "top"}
%table{:cellpadding => "2", :cellspacing => "2", :border => "0", :style => "text-align: left; font-family: Lucida Grande,Lucida Sans,Lucida Sans Unicode,Arial,Helvetica,Verdana,sans-serif; color: rgb(0, 0, 0); font-size: 12px; line-height: 1em ! important;height: 50px;", :width => "100%"}
  %tbody
    %tr
      %th{:width => "45%"} Item
      %th{:width => "30%"} Notes  
      %th{:width => "10%"} Quantity
      %th{:width => "15%"} Price

    %tr
      %td{:colspan => "4", :style => "padding-top:1px;border-bottom:5px solid #cccccc;font-size:1px;line-height:1px"}
    -total_amount = 0
    -cod_charge = 0
    - for item in all_line_items
      %tr
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"} 
            - if item.design.designer.name == 'Aapno Rajasthan' && item.design.addon_types.collect(&:name).include?('Rakhi Gifts')
              - if item.line_item_addons.present?
                = link_to truncate(item.title, :length => 18) + "- " + item.line_item_addons.first.try(:addon_type_value).try(:description), designer_design_url(item.design.designer, item.design)
              - else
                = link_to truncate(item.title, :length => 18) + "- " + item.design.design_code.to_s + '_only', designer_design_url(item.design.designer, item.design)
            - elsif item.design.design_code.present?
              = link_to truncate(item.title, :length => 18) + "- " + item.design.design_code.to_s, designer_design_url(item.design.designer, item.design) 
            - else
              = link_to truncate(item.title, :length => 18) + "- M" + item.design.id.to_s, designer_design_url(item.design.designer, item.design)
          %img{src: design_image_data(item), width: '130px', height:'150px',style:'margin-top:2px;'} 
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
          - line_item_note = ''
          - if item.note? && (item.note.match('Rakhi Schedule Delivery') || item.note.match('Discount'))
            - line_item_note = item.note.sub('Rakhi Schedule Delivery','').gsub(/Discount(.*?)off/,"").gsub("|  |","").to_s
          - else
            - line_item_note = item.note.to_s
          = (item.note? ? line_item_note : 'none')
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}   
            = item.quantity
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"} 
            - if buyer_mailer
              = get_actual_price_in_currency_with_symbol(item_snapshot_price = item.snapshot_price(RETURN_SCALED), @order.currency_rate, @order.currency_code)
            -elsif @order.international? || @designer_order.ship_to == 'mirraw'
              =(item_snapshot_price = (item.snapshot_price(RETURN_NORMAL) * @designer_order.get_payout_ratio).round(2))
            -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
              =(item_snapshot_price = @order.international_cod_price(item.snapshot_price))
            -else
              =(item_snapshot_price = item.snapshot_price(RETURN_NORMAL))
            -total_amount += (item_snapshot_price.to_f * item.quantity)
      - for addon in item.line_item_addons
        %tr
          %td
            %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:15px"}
              = '+' + addon.addon_type_value.name
          %td
            %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
              none
          %td
          %td
            %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
              - if addon.snapshot_price != 0
                - if buyer_mailer
                  = get_actual_price_in_currency_with_symbol((addon.snapshot_price * item.quantity), @order.currency_rate, @order.currency_code)
                - else
                  = addon.snapshot_price * item.quantity
              - else
                FREE
    %tr
      %td{:colspan => "4", :style => "padding-top:5px;border-bottom:2px solid #cccccc;font-size:1px;line-height:1px;width:100%"}   
    %tr
      %td{:colspan => "2"}
      %td 
        %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:50px"} Discounts
      %td
        %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
          - discount = @designer_order.discount.present? ? @designer_order.discount : 0 
          - if buyer_mailer
            - if @designer_order.order.cod? && @designer_order.shipment.present?
              - cod_charge = (@designer_order.shipment.cod_charge.present?) ? @designer_order.shipment.cod_charge: 0
              - total_amount = ((total_amount / @order.currency_rate)* @order.currency_rate_market_value).round(2)
              - discount = (((total_amount + cod_charge) - @designer_order.shipment.price).abs).round(2)
              = get_actual_price_in_currency_with_symbol(discount, @order.currency_rate_market_value, @order.currency_code)
          - else
            = "-" + discount.to_s

    - if buyer_mailer && cod_charge>0
      %tr
        %td{:colspan => "4", :style => "padding-top:5px;border-bottom:2px solid #cccccc;font-size:1px;line-height:1px;width:100%"}
      %tr
        %td{:colspan => "2"}
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:50px"} COD Charge
        %td
          %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
          = get_actual_price_in_currency_with_symbol(cod_charge, @order.currency_rate_market_value, @order.currency_code)

    %tr
      %td{:colspan => "4", :style => "padding-top:5px;border-bottom:2px solid #cccccc;font-size:1px;line-height:1px;width:100%"}       
    %tr
      %td{:colspan => "2"}
      %td 
        %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:50px"} Total
      %td
        %div{:style => "font-family:Lucida Grande, Lucida Sans, Lucida Sans Unicode, Arial, Helvetica, Verdana, sans-serif;color:#000000;font-size:12px;line-height:1em;padding-left:5px"}
          - if buyer_mailer
            - if @designer_order.order.cod? && @designer_order.shipment.present?
              = get_actual_price_in_currency_with_symbol(@designer_order.shipment.price, @order.currency_rate_market_value, @order.currency_code)
            - else
              = get_actual_price_in_currency_with_symbol((@designer_order.total + @designer_order.shipping), @order.currency_rate, @order.currency_code)
          - else
            = (total_amount + @designer_order.shipping).round(2)