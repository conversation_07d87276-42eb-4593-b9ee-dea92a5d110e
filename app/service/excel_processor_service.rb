class ExcelProcessorService
    require 'roo'
    require 'rubyXL'
  
    def initialize(params, headers_mapping, amazon_file_path)
      @params = params
      @headers_mapping = headers_mapping
      @amazon_file_path = amazon_file_path
    end
  
    def process
      myntra_data_rows = read_myntra_excel
      return unless myntra_data_rows.any?
  
      update_amazon_excel(myntra_data_rows)
      puts "Data successfully mapped and updated in #{@amazon_file_path}"
      @amazon_file_path
    rescue StandardError => e
      Rails.logger.error "Error processing Excel: #{e.message}"
      nil
    end
  
    private
  
    def read_myntra_excel
      myntra_file = @params[:myntra_file].tempfile.path
      myntra = Roo::Excelx.new(myntra_file)
      myntra.default_sheet = myntra.sheets[1]
      myntra_headers = myntra.row(3)
  
      return [] unless myntra_headers.any?
  
      (4..myntra.last_row).map { |i| Hash[myntra_headers.zip(myntra.row(i))] }
    end
  
    def update_amazon_excel(myntra_data_rows)
      workbook = RubyXL::Parser.parse(@amazon_file_path)
      sheet = workbook[1]
      amazon_headers = sheet[0].cells.map { |cell| cell ? cell.value.to_s.strip : "" }
      next_row = find_next_empty_row(sheet)
  
      myntra_data_rows.each do |row|
        amazon_row = sheet[next_row] || sheet.add_row
        amazon_headers.each_with_index do |amazon_header, col|
          myntra_header = @headers_mapping.key(amazon_header)
          mapped_value = myntra_header ? row[myntra_header].to_s : ""
          
          sheet.add_cell(next_row, col, mapped_value)
        end
        next_row += 1
      end
  
      workbook.write(@amazon_file_path)
    end
  
    def find_next_empty_row(sheet)
      last_filled_row = sheet.sheet_data.size - 1
      while last_filled_row >= 0
        last_filled_row -= 1
      end
      [last_filled_row + 1, 2].max 
    end
  end
  