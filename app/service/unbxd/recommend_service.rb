module Unbxd
  class RecommendService < Unbxd::BaseService
    attr_accessor :payload, :unbxed_response, :params

    def call
      begin
        if check_valid_params == true
          @payload = generate_payload
          @unbxed_response = send_unbxed_recommendation_request
          return @unbxed_response
        else
          return check_valid_params
        end
      rescue => error
        # ExceptionNotify.sidekiq_delay.notify_exceptions("Exception for Unbxed Recommendation"," error message for Unbxed Recommendation: #{error}",@payload)
      end
    end

    def generate_payload
      @params['widget_type'] = @params[:widget_type].present? ? @params[:widget_type] : 'None'

      unbxed_query_params = {
        'uid': @params[:uid],
        'pageType': @params[:page_type],
        'widget': @params[:widget]
      }

      additonal_param = {}
      additonal_param['id'] = @params['id'] if @params['id'].present?
      additonal_param['catlevel1Name'] =  @params['catlevel1Name'] if @params['catlevel1Name'].present?
      unbxed_query_params = unbxed_query_params.merge(additonal_param) if additonal_param.present?

      return unbxed_query_params
      
    end

    def handling_recommendation_response_callback
      if @response.code.to_i == 200
        return {'success' => 0, 'data' => @unbxed_response} if @unbxed_response['error'].present?
          
        final_result = {'success' => 1}.with_indifferent_access
        response_widget = @params['widget'].downcase
        if @unbxed_response["#{response_widget}"]['count'].eql?(0)
          final_result = final_result.merge({'message' => 'no recommendations', 'num_products' => @unbxed_response["#{response_widget}"]['count']})
        else
          recommendations = generate_design_metadata()
          final_result = final_result.merge({'data': recommendations})
        end
      else
        final_result = {'success' => 0, 'message' => 'status-code-unbxd:' + @response.code.to_s}
      end
      final_result['api_url'] = @search_api_url
      final_result['widget_title'] = @unbxed_response["#{response_widget}"]['widgetTitle']
      final_result['widget'] = @params['widget']
      return final_result
    end
    
    def check_valid_params
      return {} if @params.empty?
      if !@params.key?(:page_type)
        return {"success"=> "0", "message"=> "page_type missing"}
      elsif @params['page_type'] == 'CART' && !@params.key?(:id)
        return {"success"=> "0", "message"=> "pid missing"}
      elsif !@params.key?(:uid)
        return {"success"=> "0", "message"=> "uid missing"}
      else 
        return true
      end
    end

    def send_unbxed_recommendation_request
      search_base_url = "#{ENV['UNBXD_API_URL']}/#{ENV['UNBXD_API_KEY']}/#{ENV['UNBXD_SITE_KEY']}/items?#{URI.encode_www_form(@payload)}"
      @search_api_url = URI.unescape(search_base_url)
      get_request(@search_api_url, "handling_recommendation_response_callback")
    end
    
  end
end