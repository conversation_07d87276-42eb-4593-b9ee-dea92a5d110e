class HelpCenterQueryNotificationService
  def initialize(params)
    @params = params
  end

  def notify_support_team
    if valid_query_parameters?
      send_notification_email
      'Query notification sent successfully to the support team.'
    else
      'Invalid query parameters.'
    end
  end

  private

  def valid_query_parameters?
    keys_to_check = [:options, :user_email_id, :message_content]
    all_keys_present = keys_to_check.all? { |key| @params[key].present? }
  end

  def send_notification_email
    subject = build_email_subject
    body = build_email_body
    from_email = @params[:email_id_help_center]
    MirrawAdminMailer.help_center_query_notification_email(subject, body,from_email).deliver_now
  end

  def build_email_subject
    subject = "Query: #{@params[:issue_text_help_center]}"
    subject << " > Order No. #{@params[:order_number_text]}" if @params[:order_number_text].present?
    subject << " > Coupon No. #{@params[:coupon_id_field]}" if @params[:coupon_id_field].present?
    subject
  end

  def build_email_body
    message_content = @params[:message_content][0]
    user_email_id = @params[:user_email_id]
    "User Email ID: #{user_email_id}\nMessage: #{message_content}"
  end
end
  