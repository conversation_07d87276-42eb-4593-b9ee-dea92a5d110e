class VideoUploadService
  def initialize(email, csv_file_path)
    @csv_file_path = csv_file_path
    @email = email
  end

  def run
    missing_designs = []
    csv_content = open(@csv_file_path).read
    CSV.parse(csv_content, headers: true, header_converters: ->(header) { header.strip }) do |row|
      design_id = row['design_id'].strip.to_i if row['design_id'].present?
      video_link = row['video_link'] if row['video_link'].present?

      begin
        design = Design.find(design_id)
        current_account = Account.where(email: @email).first
        if current_account.admin? || (current_account.designer? && design.designer == current_account.designer)
          if video_link.present?
            ext = File.extname(URI.parse(video_link).path)
            if %w[.mp4 .webm .mov].include?(ext.downcase)
              SingleVideoUploadJob.perform_async(current_account.email, design.id, video_link)
            else
              missing_designs << { design_id: design_id, reason: 'Missing video link OR format is invalid' }
            end
          else
            missing_designs << { design_id: design_id, reason: 'Missing video link OR format is invalid' }
          end
        else
          missing_designs << { design_id: design_id, reason: 'Unauthorized access' }
        end
      rescue ActiveRecord::RecordNotFound
        missing_designs << { design_id: design_id, reason: 'Design ID not found' }
      end
    end

    DesignerMailer.missing_designs_email(missing_designs, @email).deliver_now if missing_designs.any?
  end
end
