class BulkDesignUpdateService
  def initialize(csv_file_path)
    @csv_file_path = csv_file_path
    @data = fetch_valid_properties
    @design_attributes = BULK_DESIGN_EDITABLE_FIELDS
    @update_status = {:successful_designs=>[],:failed_designs=>[], :missing_designs=>[], :already_updated=>[]}
  end

  def run
    process_csv
  end

  def process_csv
    CSV.new(open(@csv_file_path), headers:true).each do |row|
      begin
        design = Design.find(row[0])
        prepare_design_updates(row, design) if design.present?
      rescue
        @update_status[:missing_designs] << row[0].to_s.strip
      end
    end
    get_designs_update_status
  end

  def prepare_design_updates(row,design)  
    designable_type,existing_property_values,valid_designable_attributes = initialize_attributes(design,design.designable_type)
    row.to_a[1..-1].each do |header, cell_value|
      next if header.to_s.strip.empty? || cell_value.to_s.strip.empty?  
      header = header.to_s.downcase
      return if populate_update_attributes(header,design,cell_value,designable_type,existing_property_values,valid_designable_attributes) == "invalid_attributes"
    end
    @design_to_update.values.all? { |v| v.empty? } ? @update_status[:already_updated] << design.id : update_design_attributes(design)
  end

  def initialize_attributes(design,designable_type)
    @design_to_update = {:default_attributes=> {}, :designable=> {}, :property_values=> []}
    designable_type = design.designable_type.to_sym
    existing_property_values = design.property_values.joins(:property).pluck('properties.name', :property_value_id).to_h
    valid_designable_attributes = designable_type.to_s.constantize.columns.map(&:name) - %w(id created_at updated_at)
    return designable_type,existing_property_values,valid_designable_attributes
  end

  def populate_update_attributes(header,design,cell_value,designable_type,existing_property_values,valid_designable_attributes)
    if @design_attributes.include?(header)
      design[header] != cell_value && set_default_attribute(header,cell_value)  
    elsif @data[designable_type].include?(header.to_sym) && (new_property_value_id = @data[designable_type][header.to_sym][cell_value.gsub('-', '_').to_sym] || nil)
      existing_property_values[header] != new_property_value_id && set_property_value(designable_type,header,cell_value,existing_property_values,new_property_value_id)
    elsif valid_designable_attributes.include?(header)
      design.designable[header]!=cell_value && set_designable_attribute(header,cell_value)
    else
      @update_status[:failed_designs] << design.id
      return "invalid_attributes"
    end
  end

  def set_default_attribute(header,cell_value)
    @design_to_update[:default_attributes][header] = cell_value
  end

  def set_property_value(designable_type,header,cell_value,existing_property_values,new_property_value_id)
    @design_to_update[:property_values] << {:existing_id=>existing_property_values[header],:new_id=>new_property_value_id}
  end

  def set_designable_attribute(header,cell_value)
    @design_to_update[:designable][header] = cell_value
  end

  def get_designs_update_status
    messages = []
    messages << "Design updates failed for design ids: #{ @update_status[:failed_designs].join(', ') }" if @update_status[:failed_designs].present?
    messages << "Design updates successful for design ids: #{ @update_status[:successful_designs].join(', ') }" if @update_status[:successful_designs].present?
    messages << "Designs not found: #{ @update_status[:missing_designs].join(', ') }" if @update_status[:missing_designs].present?
    messages << "Designs already updated: #{ @update_status[:already_updated].join(', ') }" if @update_status[:already_updated].present?
    body = messages.join("\n")
    notify_update_status(body)
  end

  def fetch_valid_properties
    data = {}
    designable_types=BulkUpload.meta_data.keys
    designable_types.each do |designable_type|
      data[designable_type] = Property.get_property_value_mapping(BulkUpload.meta_data[designable_type][:designs_property_values].to_a)
      data[designable_type] = data[designable_type].merge(OptionType.get_option_type_values(BulkUpload.meta_data[designable_type][:variants].to_a))
    end
    data
  end

  def update_design_attributes(design)
    begin
      ActiveRecord::Base.transaction do
        design.update!(@design_to_update[:default_attributes]) if @design_to_update[:default_attributes].any?
        design.designable.update!(@design_to_update[:designable]) if @design_to_update[:designable].any?
        if @design_to_update[:property_values].any?
          @design_to_update[:property_values].each do |field|
            field[:existing_id].nil? ? DesignsPropertyValue.import([DesignsPropertyValue.new(design_id: design.id, property_value_id: field[:new_id])]) : DesignsPropertyValue.where(design_id: design.id, property_value_id: field[:existing_id]).update_all(property_value_id: field[:new_id])
          end
        end
        @update_status[:successful_designs] << design.id if @design_to_update.values.any?(&:present?)
      end
    rescue => e
      @update_status[:failed_designs] << design.id
    end
  end

  def notify_update_status(subject="Status for design updates",body)
    MirrawAdminMailer.send_updated_data_status(subject,body).deliver_now
  end
end