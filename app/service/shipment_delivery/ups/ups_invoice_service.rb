require 'ups/ups_api'
module ShipmentDelivery::UPS
  class UPSInvoiceService
    def initialize(items, order_number, shipment_id, shipping_charges = 0, total = 0, discount = 0 ,invoice_data: {})
      @order = Order.where(number: order_number).first
      @items = refactor_item_hash(items)
      @shipment = Shipment.find shipment_id
      @total = total
      @shipping_charges = shipping_charges
      @discount = discount
      @invoice_data = invoice_data
    end

    def generate_invoice
      no_igst_couriers = IGST_PAYMENT_COMMERCIAL.split(',')
      date = ''
      currency_code = 'INR'
      shipper_name = @shipment.shipper.name.try(:downcase)
      new_number = true
      international = false
      market_rate = 1
      dhl_csb = shipper_name == 'dhl' && !@shipment.csb_used
      commercial_status = COMMERCIAL_FOR_AUTOMATION == 'true' && shipper_name != 'delhivery'
      if @shipment.designer_order_id.present?
        @total = @items.sum { |i| i[:total_price] }
      elsif commercial_status
        commercial, paypal_rate, currency_code = @order.get_commercial_values(false, shipper_name)
        market_rate = ((cc = CurrencyConvert.where { (symbol == currency_code) | (iso_code == currency_code) }.first).exchange_rate.presence || cc.market_rate)
      end
      value_of_goods = (@total * market_rate).round(2)
      e_way_bill_status = value_of_goods >= 50_000 && (@items.collect { |i| i[:designable_type].to_s.downcase }.to_set - ['jewellery']).any?
      bill_data = {
        'Reason for Transportation' => 'Export',
        'GSTIN of Recipient' => 'URD',
        'Place of Delivery' => @order.pincode,
        'Invoice or Challan Date' => Time.current.strftime('%d/%m/%y'),
        'HSN Code' => @items.collect { |i| i[:hsn_code].to_s[0..3] }.join(','),
        'Transport Document Number' => @shipment.number,
        'Vehicle Number' => ''
      } if e_way_bill_status
      if (designer_order = @shipment.designer_order).present?
        _, _, _, _, _, shipping_pincode, = DesignerOrder.get_warehouse_billing_address(designer_order.warehouse_address_id)
        new_number = false
        @invoice_data = Shipment.calculate_domestic_invoice(@order, @items, false, designer_order) if @invoice_data.blank?
        tcs_applicable = DesignerOrder.is_tcs_applicable(designer_order, @order)
        tds_applicable = DesignerOrder.is_tds_applicable(designer_order, @order)
        tcs_value = tcs_applicable ? (@invoice_data[:total_taxable_value] / 100.0).round(2) : 0
        tds_value = tds_applicable ? (@invoice_data[:total_taxable_value] * 0.1 / 100.0).round(2) : 0
        designer_order.update_columns(gst_tax: @invoice_data[:gst_total].to_i, tcs_tax: tcs_value, tds_tax: tds_value)
        pdf_content = ActionController::Base.new.render_to_string(
          template: '/shipments/invoice_designer',
          layout: false,
          locals: { :@order => @order, :@items => @items, :@shipment => @shipment, mirraw_domestic: false, invoice_data: @invoice_data, domestic_sor: designer_order.domestic_sor?(@order) }
        )
        if e_way_bill_status
          bill_data['Invoice or Challan Date'] = (designer_order.pickup.presence || designer_order.completed_at.presence || designer_order.created_at).strftime('%d/ %m/ %Y')
          bill_data['Invoice or Challan Number'] = designer_order.invoice_number
          bill_data['Reason for Transportation'] = 'Supply'
          if (designer_order.ship_to.blank? && @order.international?) || (designer_order.ship_to == 'mirraw')
            bill_data['GSTIN of Recipient'] = MIRRAW_GST_NUMBER
            bill_data['Place of Delivery'] = shipping_pincode
          end
        end
      else
        international = true
        irn_number = nil
        irn_barcode = nil
        error_msg = nil
        if DISABLE_ADMIN_FUCTIONALITY['irn_feature'] && ([market_rate.to_f, currency_code.to_s] & [1.0, 'INR']).blank?
          begin
            OrderMailer.report_mailer("IRN Request #{@order.number} / #{@shipment.id}", "Please find below items details below: <br>Market Rate: #{market_rate.to_f}<br> Currency Code: #{currency_code}<br><br>#{@items}", { 'to_email' => [DEPARTMENT_HEAD_EMAILS['accounts']], 'from_email_with_name' => '<EMAIL>' }, {}).deliver
            generate_irn = GenerateIrnNumber.new(@order, @items, @shipment, market_rate.to_f, currency_code)
            response = generate_irn.generate_forward_irn
            if response[:error] == false
              irn_number = response[:irn_number]
              irn_barcode = response[:irn_barcode]
              @shipment.update_columns(irn_number: irn_number, gst_barcode: irn_barcode)
            else
              error_msg = response[:error_msg]
            end
          rescue => e
            error_msg = "#{e.message} ===> #{e.backtrace}"
          end
          if error_msg.present?
            OrderMailer.report_mailer("Error While Creating IRN Invoice #{@order.number}", "Failed to generate IRN number due to below reason <br>#{error_msg}", { 'to_email' => [DEPARTMENT_HEAD_EMAILS['accounts']], 'from_email_with_name' => '<EMAIL>' }, {}).deliver
            ExceptionNotify.sidekiq_delay.notify_exceptions('IRN NUMBER Generation Error',error_msg, { params: "Order: #{@order.number} ====> Shipment: #{@shipment.id} ====> items: #{@items}" })
          end
        end
        if commercial_status
          shipment_detail = {}
          shipment_detail['currency_code'] = currency_code
          shipment_detail['shipper_name'] = shipper_name
          shipment_detail['awb_number']   = @shipment.number
          shipment_detail['total_price']  = @total.round(2)
          shipment_detail['dhl_csb'] = dhl_csb
          @order.other_details['invoice_paypal_rate'] = paypal_rate
        else
          commercial = false
        end
        date = @order.get_invoice_number
        bill_data['Invoice or Challan Number'] = date if e_way_bill_status
        if (SHIPPER_LIST_INTERNATIONAL.map(&:downcase).include? shipper_name.downcase) && commercial
          Shipment.generate_invoice_inr(@order, @shipment, shipment_detail, @items, date, market_rate)
          pdf_content = ActionController::Base.new.render_to_string(
            template: '/shipments/commercial_invoice',
            layout: false,
            locals: { :@order => @order, :@items => @items, :@shipment_detail => shipment_detail, :@date => date, :@market_rate => market_rate, no_igst_couriers: no_igst_couriers, :@irn_number => irn_number, :@gst_barcode => irn_barcode }
          )
        elsif shipper_name == 'delhivery'
          @invoice_data = Shipment.calculate_domestic_invoice(@order, @items, true)
          pdf_content = ActionController::Base.new.render_to_string(
            template: '/shipments/invoice_designer',
            layout: false,
            locals: { :@order => @order, :@items => @items, :@shipment => @shipment, :@market_rate => 1, mirraw_domestic: true, invoice_number: date, invoice_data: @invoice_data, domestic_sor: false }
          )
          bill_data['Reason for Transportation'] = 'Supply' if e_way_bill_status
        else
          pdf_content = ActionController::Base.new.render_to_string(
            template: '/shipments/invoice',
            layout: false,
            locals: { :@order => @order, :@items => @items, :@date => date }
          )
        end
        pdf_content += ActionController::Base.new.render_to_string(
          template: '/shipments/annexure.html.haml',
          layout: false,
          locals: { invoice_date: @shipment.created_at, invoice_number: date, delivery_term: (@order.shipping.to_i > 0 ? 'C&F' : 'FOB') }
        ) if %w(dhl skynet).include?(shipper_name) && !@shipment.csb_used?
        @shipment.invoice_data['fob_value']         = @total.round(2)
        @shipment.invoice_data['packaging_charges'] = @shipping_charges
        @shipment.invoice_data['currency_code']     = currency_code
        @shipment.invoice_number                    = date
        @shipment.exchange_rate                     = market_rate
        @shipment.shipment_type                     = 'COD' if @order.international? && @order.cod?
      end
      if e_way_bill_status
        bill_data['Value of Goods'] = "\u20B9 #{value_of_goods} " + ("/ #{@total} #{currency_code}" unless currency_code == 'INR').to_s
        pdf_content += ShipmentsController.generate_e_waybill(bill_data)
      end
      invoice = WickedPdf.new.pdf_from_string(pdf_content, encoding: 'UTF-8')
      @shipment.add_invoice(invoice)
      if @shipment.save!
        if international
          @items.each do |item|
            s_item = ShipmentInvoiceItem.where(line_item_id: item[:line_item_id],description: item[:name], shipment_id: @shipment.id).first_or_create
            s_item.update_attributes(hsn_code: item[:hsn_code], rate: item[:price], discount: item[:item_discount], discounted_rate: item[:item_discounted_price], taxable_value: item[:taxable_value], gst_rate: item[:gst_rate], gst_tax: item[:gst_tax], total_amount: item[:total_price], product_type: item[:designable_type], currency: currency_code, gst_paid: dhl_csb || no_igst_couriers.exclude?(shipper_name), quantity: item[:quantity], weight: item[:weight])
          end
        end
        if @order.order_notification.key?("invoice_#{date}")
          @order.order_notification["invoice_#{date}"] = @shipment.invoice.url
          @order.save
        end
      end
    end

    def refactor_item_hash(items)
      items.map{|item| item.with_indifferent_access}
    end

  end
end