require 'ups/ups_api'
module ShipmentDelivery::UPS
  class UP<PERSON><PERSON><PERSON><PERSON>abe<PERSON>
    def initialize(number, items, total_weight, packaging_type, packer_id, current_account_id, length, width, height)
      @order = Order.preload(:order_addon, line_items: :line_item_addons).where(number: number).first
      @items = items
      @total_weight = total_weight
      @packaging_type = packaging_type
      @packer_id = packer_id
      @current_account_id = current_account_id
      @length = length
      @width = width
      @height = height
    end

    def generate_ups_label
      OrderMailer.report_mailer("UPS Service Parameters","REQ : Order No:. #{@order.number} -- Items:. #{@items} -- Total Weight: #{@total_weight}, Packaging Details:. #{@packaging_type} -- Packer ID:. #{@packer_id} -- Length: #{@length} -- Width:. #{@width} -- Height:. #{@height}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
      company_name, shipping_telephone, shipping_address_1, _, shipping_city, shipping_pincode, _, shipping_state_code = @order.get_warehouse_shipping_address
      state_code_countries = 'US', 'IN', 'CA', 'AE'
      street_address = @order.multi_line_address(35)
      shipper_ups = Shipper.where('lower(name) =?', 'ups').second
      country_code = Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
      if COMMERCIAL_FOR_AUTOMATION == 'true'
        commercial, paypal_rate, currency_code = @order.get_commercial_values(false, 'ups')
      else
        commercial = false
        paypal_rate = 1
        currency_code = 'INR'
      end

      selected_items = []
      @items.each { |_key, i| selected_items << i if i[:selected].present? }
      total_items = 0
      selected_items.each do |item|
        total_items += item[:quantity].to_i
      end
      reference, shipping_amt, order_total_price, tax_amount = @order.get_order_invoice_details(commercial, shipper_ups.id, selected_items.collect { |i| i[:item_id] })
      shipping_charges = shipping_amt / total_items
      total_tax = tax_amount / total_items
      selected_items_group = selected_items.group_by { |item| item[:name] }
      market_rate = ((cc = CurrencyConvert.where { (symbol == currency_code) | (iso_code == currency_code) }.first).exchange_rate.presence || cc.market_rate)
      invoice_data = {
        items_id: [],
        order_total_price: order_total_price,
        discount: @order.get_total_discount,
        market_rate: market_rate,
        commercial: commercial,
        paypal_rate: paypal_rate,
        shipper_name: 'ups'
      }
      selected_items_weight_total = @total_weight.to_f
      items_price_total           = 0
      items_weight_total          = 0
      invoice_items               = []

      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges, total_tax: total_tax)
        items_weight = (selected_items_weight_total / total_items).to_f * items_count
        items_price_total  += items_price
        items_weight_total += items_weight
      end

      dso = DesignerOrder.where(order_id: @order.id)
      line_items = []
      dso.each do |dos|
        line_items << LineItem.where(designer_order_id: dos.id)
      end
      designable_type = ""
      line_items.each do |li|
        li.each do |line|
          d_type = line.designable_type
          d_type = (['other','kid','islamic'].include?(line.designable_type.try(:downcase)) || line.designable_type == nil) ? Category.where(id: line.category_id)[0].name : line.designable_type
          designable_type << d_type
          designable_type << " "
        end
      end
      query = {
        'additionaladdressvalidation' => 'city',
      }

      shipper_address = {
        :AddressLine => "101 to 110 1st floor,BuildingNo 75,",
        :AddressLine => "Indian Corporation complex,Mankoli",
        :AddressLine => "Naka,Dapode, Bhiwandi, Maharashtra",
        :City => shipping_city,
        :StateProvinceCode => shipping_state_code,
        :PostalCode => shipping_pincode,
        :CountryCode => 'IN' 
      }

      ship_to = {
        :Name => @order.name,
        :AttentionName => @order.name,
        :Phone => {:Number => @order.billing_phone},
        :Address => {
          :AddressLine => [street_address[0], street_address[1]].compact,
          :City => @order.city,
          :StateProvinceCode => (state_code_countries.include?(country_code) ? @order.state_code : @order.buyer_state),
          :PostalCode => @order.pincode,
          :CountryCode => country_code,
        }
      }

      ship_from = {
        :Name => company_name,
        :AttentionName => company_name,
        :Phone => {:Number => shipping_telephone},
        :Address => shipper_address
      }
      shipment_service = {
        :InternationalForms => {
          :FormType => "01",
          :AdditionalDocumentIndicator => {},
          :Contacts => {
            :SoldTo => ship_to,
          },
          :Product => product_hash(invoice_items,country_code),
          :InvoiceNumber =>"",
          :InvoiceDate=> Date.today.strftime('%Y%m%d'),
          :TermsOfShipment => "CFR",
          :ReasonForExport => "SALE",
          :DeclarationStatement => "TEST",
          :CurrencyCode => currency_code,
          :OverridePaperlessIndicator => "Y",
        }

      }


      payment_information = {
        :ShipmentCharge => {
          :Type => '01',
          :BillShipper => {:AccountNumber => 'A373X4'}
        }
      }

      packages = [
        {
          :Description => 'Express Box',
          :Dimensions => {
              :UnitOfMeasurement => {
                  :Code => 'CM'
              },
              :Length => @length.to_s,
              :Width => @width.to_s,
              :Height => @height.to_s
          },
          :Packaging => {
              :Code => '02'
          },
          :PackageWeight => {
              :UnitOfMeasurement => {
                  :Code => 'KGS'
              },
              :Weight => @total_weight.to_f.to_s
          },
        }
      ]

      shipper = {
        :Name => company_name,
        :AttentionName => company_name,
        :ShipperNumber => 'A373X4',
        :Phone => {:Number => shipping_telephone},
        :Address => shipper_address,
        :CompanyDisplayableName => company_name
      }
      shipment = {
        :Description => designable_type[0, 50],
        :Shipper => shipper,
        :ShipTo => ship_to,
        :ShipFrom => ship_from,
        :PaymentInformation => payment_information,
        :Service => {:Code => '65', :Description => 'Express Saver'},
        :ShipmentServiceOptions => shipment_service,
        :Package => packages
      }

      shipment_request = { 
        :ShipmentRequest => {
          :Request => {:RequestOption => 'nonvalidate'},
          :Shipment => shipment,
          :LabelSpecification => {:LabelImageFormat => {:Code => 'GIF'} }
      }
    }

      begin
        ups = UPSApi.new(@order)
        response = ups.create_shipment(shipment_request, query)
        OrderMailer.report_mailer("UPS Api REQ RES","REQ : #{shipment_request} RES : #{response}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
        if ["Success"].include?(response['ShipmentResponse']['Response']['ResponseStatus']['Description'])
          ups_gif_image = response['ShipmentResponse']['ShipmentResults']['PackageResults'][0]['ShippingLabel']['GraphicImage']
          ups_base64_gif_image = "data:image/gif;base64,"+ups_gif_image
          pdf_data = WickedPdf.new.pdf_from_string("<img src='"+ups_base64_gif_image+"'/>",{:orientation => 'Landscape'})
          File.open('label.pdf','wb') do |pdf|
            pdf.write pdf_data
            pdf.close
          end
          label_pdf = pdf_data
          package_details = response['ShipmentResponse']['ShipmentResults']['PackageResults']
          awb_number = package_details[0]['TrackingNumber']
          @shipment = Shipment.new(
            number: awb_number,
            shipper: shipper_ups,
            price: (items_price_total * paypal_rate).to_i,
            weight: items_weight_total,
            line_item_ids: invoice_data[:items_id],
            order_id: @order.id,
            packaging_type: "02",
            invoicer_id: @current_account_id,
            packer_id: @packer_id,
            mirraw_reference: reference
          )
          if @shipment.save
            @order.update_attributes(tracking_number: @shipment.number, courier_company: @shipment.shipper.name)
          end
          @shipment.sidekiq_delay(queue: 'critical').add_label(label_pdf)
          SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ShipmentDelivery::UPS::UPSInvoiceService", "generate_invoice", {invoice_items: invoice_items, order_number: @order.number, shipment_id: @shipment.id, shipping_charges: shipping_charges, items_price_total: items_price_total, skip_object_creation: true})
          #invoice = ShipmentDelivery::UPS::UPSInvoiceService.new(invoice_items, @order.number, @shipment.id, @upload_invoice, shipping_charges, items_price_total)
          #invoice.sidekiq_delay(queue: 'critical').generate_invoice
          # gon.shipment_id = @shipment.id
          @order.remove_tags_skip_callback('shipment_error')
        else
          ExceptionNotify.sidekiq_delay.notify_exceptions('UPS Shipment Debug','UPS Label Generation Failed.', { payload: shipment_request, response: response })
          raise (notifications = response[:process_shipment_reply][:notifications]).is_a?(Array) ? notifications.map(&:message).join(', ') : notifications[:message]
        end
      rescue => error
        @order.add_tags_skip_callback('shipment_error')
        ExceptionNotify.sidekiq_delay.notify_exceptions('UPS Shipment Debug','UPS Label Generation Failed.', { error: error.inspect })
      end

    end

    def total_price_per_item(invoice_items)
      total_price_hash = {}
      invoice_items.each_with_index do |item,index|
        total_price_hash["#{index}"] = item[:total_price]
      end
      return total_price_hash
    end

    def product_hash(invoice_items,country_code)
      product = []
      total_price = total_price_per_item(invoice_items)
      @items.each do |key,value|
        product << {
          "Description": @items[key]["name"],
          "Unit":{
              "Number": "#{key.to_i+1}",
              "UnitOfMeasurement": {
                  "Code": "PCS",
                  "Description": "Pieces",
  
              },
              "Value": "#{total_price[key]}",
          },
          "OriginCountryCode": "#{country_code}"
        }
      end
      return product
    end

  end
end
