require 'fedex/fedex_api'
module ShipmentDelivery::Fedex
  class FedexOrderLabel

    CURRENCY_CODE = {'GBP' => 'UKL', 'SGD' => 'SID', 'CHF' => 'SFR'}
    LABEL_DETAILS_HASH = {'labelFormatType' => 'COMMON2D', 'labelOrder'=> "SHIPPING_LABEL_FIRST", 'imageType' => 'PDF', 'labelStockType' => 'PAPER_85X11_TOP_HALF_LABEL',      
      "customerSpecifiedDetail"=> {
      "maskedData"=> [
        "TRANSPORTATION_CHARGES_PAYOR_ACCOUNT_NUMBER",
        "DUTIES_AND_TAXES_PAYOR_ACCOUNT_NUMBER"
        ]
    } }

    def initialize(number, items, total_weight, packaging_type, packer_id, current_account_id, upload_invoice = false)
      @order = Order.preload(:order_addon, line_items: :line_item_addons).where(number: number).first
      @items = items
      @total_weight = total_weight
      @packaging_type = packaging_type
      @packer_id = packer_id
      @upload_invoice = upload_invoice
      @current_account_id = current_account_id
      @shipper_fedex = Shipper.where('lower(name) =?', 'fedex').first
    end

    def generate_fedex_label
      shipment_req = get_shipment_request_hash      
      begin
        fedex_api = FedexApi.new
        # THIS WAS ADDED FOR THE FEDEX UPLOAD FEATURE 
        response = fedex_api.create_shipment(shipment_req)
        post_shipment_generation_process(response)
      rescue => error
        @order.add_notes_without_callback(error.inspect, "Fedex Shipment Creation Error")
        fedex_api.notify('Fedex Shipment Error', 'Fedex Create Shipment and Label Generation Failed.', shipment_req, response, error)
      end
    end

    def post_shipment_generation_process(response)
      pieceResponses = response[:output][:transactionShipments].first[:pieceResponses].first
      awb_number, courier_label_image = pieceResponses[:trackingNumber], pieceResponses[:packageDocuments].first[:encodedLabel]
      shipment = Shipment.new(
        number: awb_number,
        shipper: @shipper_fedex,
        price: (@items_price_total * @paypal_rate).to_i,
        weight: @items_weight_total,
        line_item_ids: @invoice_data[:items_id],
        order_id: @order.id,
        packaging_type: @packaging_type,
        invoicer_id: @current_account_id,
        packer_id: @packer_id,
        mirraw_reference: @reference,
      )
      if shipment.save
        @order.update_attributes(tracking_number: shipment.number, courier_company: shipment.shipper.name)
      end
      label_image = Base64.decode64(courier_label_image)
      shipment.add_label(label_image)
      SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ShipmentDelivery::Invoice", 
                                                                                "generate_invoice", 
                                                                                {invoice_items: @invoice_items, 
                                                                                  order_number: @order.number, 
                                                                                  shipment_id: shipment.id, 
                                                                                  upload_invoice: @upload_invoice, 
                                                                                  shipping_charges: @shipping_charges, 
                                                                                  items_price_total: @items_price_total, 
                                                                                  skip_object_creation: true}
                                                                              )
      @order.remove_tags_skip_callback('shipment_error')
    end

    def get_shipment_request_hash
      @commercial, @paypal_rate, @currency_code = if COMMERCIAL_FOR_AUTOMATION == 'true'
                                                     @order.get_commercial_values(false, 'fedex')
                                                  else
                                                    [false, 1, 'INR']
                                                  end

      @env = (Rails.env.production? || Rails.env.admin?)? 'international' : 'development'
      shipment_request = {
        'shipTimestamp' => DateTime.current.strftime('%Y-%m-%dT%H:%M:%S%Z'),
        'shipper' => shipper_hash,
        'recipients' => recipients_array_object,
        'pickupType' => 'USE_SCHEDULED_PICKUP',
        'serviceType' => 'FEDEX_INTERNATIONAL_PRIORITY',
        'packagingType' => @packaging_type,
        'preferredCurrency' => 'USD',
        'shippingChargesPayment' => shipping_charges_payment_hash,
        'customsClearanceDetail' => customs_clearance_detail_hash,
        'labelSpecification' => LABEL_DETAILS_HASH,
        'totalPackageCount' => 1,
        'requestedPackageLineItems' => package_line_items_hash
      } 
      shipment_request = shipment_request.merge({'shipmentSpecialServices' => { 
        'specialServiceTypes' => ["ELECTRONIC_TRADE_DOCUMENTS"], 
        'etdDetail' => {'attributes' => ["POST_SHIPMENT_UPLOAD_REQUESTED"]}
        }
      }) if @upload_invoice

      final_shipment_request = {
        "labelResponseOptions"=> "LABEL",
        'requestedShipment' => shipment_request, 
        "accountNumber"=> {
          "value"=> Shipment.fedex_account_number(@env)
        }
      }
      return final_shipment_request
    end

    def package_line_items_hash
      customer_reference_details = []
      customer_references = { 'CUSTOMER_REFERENCE' => @reference, 'P_O_NUMBER' => Time.current.strftime('%d%m%y'), 'DEPARTMENT_NUMBER' => "CS5/N/FOB/U/E/M/0/#{Time.current.strftime('%d%m%y')}", 'INVOICE_NUMBER' => @order.get_invoice_number }
      customer_references.each do |name, val|
        customer_reference_details << { 'customerReferenceType' => name, 'value' => val }
      end
      [{
        'sequenceNumber' => 1,
        'customerReferences' => customer_reference_details,
        'weight' => { 'units' => 'KG', 'value' => @items_weight_total }
      }]
    end

    def customs_clearance_detail_hash
      commodities, @invoice_items, selected_items, selected_items_weight_total, @items_price_total, @items_weight_total = [], [], [], @total_weight.to_f, 0, 0
      label_currency = CURRENCY_CODE[@currency_code] || @currency_code
      @items.each { |_key, i| selected_items << i if i[:selected].present? }
      total_items = 0
      selected_items.each do |item|
        total_items += item[:quantity].to_i
      end
      @reference, shipping_amt, order_total_price, tax_amount = @order.get_order_invoice_details(@commercial, @shipper_fedex.id, selected_items.collect { |i| i[:item_id] })
      @shipping_charges = shipping_amt / total_items
      @total_tax = tax_amount / total_items
      selected_items_group = selected_items.group_by { |item| item[:name] }
      market_rate = ((cc = CurrencyConvert.where { (symbol = @currency_code) | (iso_code = @currency_code) }.first).exchange_rate.presence || cc.market_rate)
      @invoice_data = {
        items_id: [],
        order_total_price: order_total_price,
        discount: @order.get_total_discount,
        market_rate: market_rate,
        commercial: @commercial,
        paypal_rate: @paypal_rate,
        shipper_name: 'fedex'
      }
      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, @invoice_items, @invoice_data, product_name, @shipping_charges, total_tax: @total_tax)
        items_weight = (selected_items_weight_total / total_items).to_f * items_count
        commodity = {
          'numberOfPieces' => items_count,
          'description' => product_name.tr('-', ' ').camelize,
          'countryOfManufacture' => 'IN',
          'harmonizedCode' => hsn_code,
          'weight' => { 'units' => 'KG', 'value' => items_weight },
          'quantity' => items_count, 'quantityUnits' => 'EA',
          'unitPrice' => { 'currency' => label_currency, 'amount' => item_discounted_price },
          'customsValue' => { 'currency' => label_currency, 'amount' => items_price }
        }

        @items_price_total  += items_price
        @items_weight_total += items_weight
        commodities << commodity
      end
      {
        'dutiesPayment' => { 'paymentType' => 'RECIPIENT' },
        'customsValue' => { 'currency' => label_currency, 'amount' => @items_price_total.round(2) },
        'commercialInvoice' => { 
          'shipmentPurpose' => 'SOLD', 
          'customerReferences' => [{ 'customerReferenceType' => 'CUSTOMER_REFERENCE', 'value' => @reference }], 
          'termsOfSale' => 'FOB' },
          'commodities' => commodities
      }
    end

    def shipper_hash
      company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, _, shipping_state_code = @order.get_warehouse_shipping_address
      {
        'tins' => [{ 'tinType' => 'BUSINESS_NATIONAL', 'number' => MIRRAW_GST_NUMBER, 'usage' => 'ANY' }],
        'contact' =>
              {
                'personName' => 'Anup Nair',
                'companyName' => company_name,
                'phoneNumber' => shipping_telephone,
                'emailAddress' => '<EMAIL>'
              },
        'address' =>
              {
                'streetLines' => shipping_address_1.split( /, */ )  << shipping_address_2,
                'city' => shipping_city,
                'stateOrProvinceCode' => shipping_state_code,
                'postalCode' => shipping_pincode,
                'countryCode' => 'IN'
              }
      }
    end

    def recipients_array_object 
      country_code = Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
      street_address = @order.multi_line_address(35)
      [{
        'contact' =>
            {
              'personName' => @order.name,
              'phoneNumber' => @order.phone,
              'emailAddress' => @order.email
            },
        'address' =>
          {
            'streetLines' => [street_address[0], street_address[1]].compact,
            'city' => @order.city,
            'stateOrProvinceCode' => (["US", "IN", "CA", "AE"].include?(country_code) ? @order.state_code : ""),
            'postalCode' => @order.pincode,
            'countryCode' => country_code,
            'residential' => 'false'
          }
      }] 
    end

    def shipping_charges_payment_hash
       {
        'paymentType' => 'SENDER',
        'payor' =>  {
          'responsibleParty' => {
            'accountNumber' => {'value' => Shipment.fedex_account_number(@env)},
            'address' => { 'countryCode' => 'IN' }
          }
        }
      }
    end 
  end
end
