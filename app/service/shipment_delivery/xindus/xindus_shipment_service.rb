require 'xindus/xindus_api'
module ShipmentDelivery::Xindus
  class XindusShipmentService 

    def initialize(number, input_data, invoice_items, items_price_total, reference, currency_code, item_ids, shipping_charges)
        @order = Order.preload(:order_addon, line_items: :line_item_addons).where(number: number).first
        @input_data = input_data
        @invoice_items = invoice_items
        @items_price_total = items_price_total
        @reference = reference
        @currency_code = currency_code
        @item_ids = item_ids
        @shipping_charges = shipping_charges   
    end


    def shipment_generation_process
        xindus_api = XindusApi.new
        shipment_req = get_shipment_request_hash
        begin
          response = xindus_api.create_shipment_api(shipment_req)
          post_shipment_generation_process(response)
        rescue => exception
          @order.add_notes_without_callback(exception.inspect, "Xindus Shipment Creation Error")
          xindus_api.notify('Xindus Shipment Error', 'Xindus Create Shipment and Label Generation Failed.', shipment_req, response, exception)
        end
    end

    def post_shipment_generation_process(response)
      shipper = Shipper.where('lower(name) = ?','xindus').first
      awb_number = response
      shipment = Shipment.new(
        number: awb_number,
        shipper: shipper,
        price: @items_price_total,
        weight: @input_data[:total_weight],
        line_item_ids: @item_ids,
        order_id: @order.id,
        packaging_type: @input_data[:packaging_type],
        invoicer_id: @input_data[:packer_id],
        packer_id: @input_data[:packer_id],
        mirraw_reference: @reference,
      )
      if shipment.save
        @order.update_attributes(tracking_number: shipment.number, courier_company: shipment.shipper.name)
        ShipmentDelivery::Xindus::XindusShipmentService.sidekiq_delay.payment_api_call(shipment.id, awb_number)
      end
      SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ShipmentDelivery::Invoice", 
                                                                                "generate_invoice", 
                                                                                {invoice_items: @invoice_items, 
                                                                                  order_number: @order.number, 
                                                                                  shipment_id: shipment.id,
                                                                                  upload_invoice: false,  
                                                                                  shipping_charges: @shipping_charges, 
                                                                                  items_price_total: @items_price_total, 
                                                                                  skip_object_creation: true}
                                                                              )
    end


    def self.label_and_invoice_generation(id,awb_number)
      xindus_api = XindusApi.new
      courier_label_image = xindus_api.shipping_label_api(awb_number) 
      label_image = Base64.decode64(courier_label_image)
      courier_invoice_image = xindus_api.shipment_invoice_api(awb_number) 
      invoice_image = Base64.decode64(courier_invoice_image)
      obj_invoice = CombinePDF.parse(invoice_image)
      obj_label = CombinePDF.parse(label_image)
      obj_invoice << obj_label 
      shipment = Shipment.find id
      shipment.add_label(obj_invoice.to_pdf)
    end 

    def self.payment_api_call(id,awb_number)
      begin
        xindus_api = XindusApi.new   
        payment_response = xindus_api.payment_api(awb_number)
        ShipmentDelivery::Xindus::XindusShipmentService.sidekiq_delay.label_and_invoice_generation(id,awb_number)
      rescue => error
        xindus_api.notify('Xindus Shipment Error', 'Xindus Payment Api Failed.', {}, {}, error)
        raise error
      end
    end 

    def get_shipment_request_hash
      shipment_req_hash = {
        "shippingmethod" => "AP",
        "terms" => "DDU",
        "exportreference" => @reference,
        "purpose" => "Sold",
        "shippingcurrency" => @currency_code,
        "taxtype" => "LUT",
        "service" => "CSB V",
        "iec" => "0313074984",
        "shipmentreferences" => @reference,
        "status" => "SHIPMENT_CREATED",
        "orderlineitems" => order_box_details,
        "shipperaddress" => shipper_hash,
        "recieveraddress" => reciever_hash,
        "billingaddress" => sender_hash,
        "ioraddress" => sender_hash
      }
    end

    def shipper_hash
      company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, shipping_state_code = @order.get_warehouse_shipping_address
      {
        "name" => company_name ,
        "email" => "<EMAIL>",
        "phone" => shipping_telephone,
        "address" => shipping_address_1 << shipping_address_2,
        "city" => shipping_city,
        "zip" => shipping_pincode,
        "state" => shipping_state ,
        "country" => "IN"   
      }
    end  

    def reciever_hash
      country_code = Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
      {
        "name" => @order.name,
        "email" => @order.email,
        "phone" => @order.phone,
        "address" => @order.street,
        "city" => @order.city,
        "zip" => @order.pincode,
        "state" => @order.state_code,
        "country" => country_code
      }
    end

    def sender_hash
      country_code = Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
      {
        "name" => @order.billing_name,
        "email" => @order.billing_email,
        "phone" => @order.billing_phone,
        "address" => @order.billing_street,
        "city" => @order.billing_city,
        "zip" => @order.billing_pincode,
        "state" => @order.billing_state,
        "country" => country_code
      }
    end 

    def order_box_details
      [
        { 
          "weight" => @input_data[:total_weight] ,
          "uom" =>"cms",
          "width" => @input_data[:shipment_width],
          "length"=> @input_data[:shipment_length],
          "height"=> @input_data[:shipment_height],
          "boxId" => "1",                   #consider single box per shipment
          "orderProducts" => product_details
        }
      ]
    end

    def product_details
      commodities = []
      @invoice_items.each_with_index do |item,index|
        difference = 8 - item[:hsn_code].length
        diff = "0" * difference
        hsn_code = "#{diff}#{item[:hsn_code]}"
        commodity =   {
            "category" => item[:category_name], 
            "description" => item[:name], #category 
            "ehsn" => hsn_code,
            "ihsn"=> "",
            "quantity" => item[:quantity].to_s,
            "weight" => item[:weight].to_s,
            "district" => "Mumbai",
            "state" => "Maharashtra",
            "countryoforigin" => "IN",          
            "unitprice" => item[:item_discounted_price].to_s
        }
        commodities << commodity
      end
      commodities
    end
    
  end
end

