require 'csv'
require 'open-uri'
class DesignGradingUploadService
	def initialize(grading_tag_id,csv_file_path, user_email)
    @grading_tag_id = grading_tag_id
    @csv_file_path = csv_file_path
    @user_email = user_email
	end

	def extract_design_ids_and_ranks_from_csv
    design_data = []
    csv_content = open(@csv_file_path).read
    CSV.parse(csv_content, headers: true,header_converters: ->(header) { header.strip }) do |row|
      design_id = row['design_ids'].strip.to_i if row['design_ids'].present?
      rank = row['ranks'].strip.to_i if row['ranks'].present?

      if design_id.present? && rank.present?
        design_data << { design_id: design_id, rank: rank }
      end
    end
    design_data.uniq { |data| data[:design_id] }
  end
  
  def run
    begin
      manage_design_grading_process
      send_grading_status_email("Design Grading completed successfully for Grading Tag id-#{@grading_tag_id}. Grading was performed by #{@user_email}")
    rescue => exception
      send_grading_status_email("Some Exception occurred while Design Grading for #{@grading_tag_id}. Grading was attempted by #{@user_email}. Error Message - #{exception}")
    end
  end


	def manage_design_grading_process
    design_data = extract_design_ids_and_ranks_from_csv
    grading_tag = GradingTag.find_by(id: @grading_tag_id)
    existing_design_grading_tags = DesignGradingTag.where(grading_tag_id: @grading_tag_id)
    existing_design_ids = existing_design_grading_tags.pluck(:design_id)
   
    # Delete the existing DesignGradingTag records
    existing_design_grading_tags.delete_all

    design_data.each do |data|
      DesignGradingTag.create!(
        design_id: data[:design_id],
        grading_tag_id: @grading_tag_id,
        rank: data[:rank]
      )
    end

    all_design_ids = existing_design_ids + design_data.map { |design| design[:design_id] }
    design_reindex(all_design_ids.uniq)
  end

  def design_reindex(design_ids)
      Sunspot.index Design.where(id: design_ids)
      Design.country_code = "IN"
      Sunspot.session.queue.process
  end

  def send_grading_status_email(status)
    MirrawAdminMailer.send_progress_notification(status).deliver_now!
  end
end