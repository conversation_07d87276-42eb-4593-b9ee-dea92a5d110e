class WarehouseOrdersService
    DEFAULT_DATE_RANGE = -> { Designer::NO_MONTHS.months.ago.beginning_of_day..Date.today.end_of_day }
  
    def initialize(designer, params)
      @designer = designer
      @params = params
    end
  
    def call
      base_query
      apply_filters
      handle_export_or_pagination
    end
  
    private
  
    def base_query
      @warehouse_orders = @designer.warehouse_orders
        .preload(:shipper, :rtv_shipments, {
          warehouse_line_items: [
            :warehouse_order, 
            { design: :images }, 
            { variant: { option_type_values: :option_type } }
          ]
        })
        .order('warehouse_orders.created_at desc')
    end
  
    def apply_filters
      filter_by_design
      filter_by_state
      filter_by_order_number
      filter_by_date_range
    end
  
    def filter_by_design
      return unless @params[:design_id].present?
      @warehouse_orders = @warehouse_orders
        .joins(:warehouse_line_items)
        .where(warehouse_line_items: { design_id: @params[:design_id] })
        .distinct
    end
  
    def filter_by_state
      return unless @params[:state].present?
      @warehouse_orders = @warehouse_orders.where(state: @params[:state])
    end
  
    def filter_by_order_number
      return unless @params[:warehouse_order_number].present?
      @warehouse_orders = @warehouse_orders.where(number: @params[:warehouse_order_number])
    end
  
    def filter_by_date_range
      return if @params['track_order_num'].present?
      date_range = parse_daterange(@params['daterange_selector'])
      @warehouse_orders = @warehouse_orders.where(created_at: date_range) if date_range
    end
  
    def parse_daterange(daterange)
      return DEFAULT_DATE_RANGE.call unless daterange.present?
  
      start_date, end_date = daterange.split(" - ")
      from = Date.strptime(start_date.strip, "%d %B, %Y").beginning_of_day
      to = Date.strptime(end_date.strip, "%d %B, %Y").end_of_day
      from..to
    rescue
      DEFAULT_DATE_RANGE.call
    end
  
    def handle_export_or_pagination
      if @params[:export].present?
        { csv_data: generate_warehouse_order_csv(@warehouse_orders), filename: "warehouse_orders_#{Date.today}.csv" }
      else
        @warehouse_orders.paginate(page: @params[:page], per_page: 10)
      end
    end
  
    def generate_warehouse_order_csv(warehouse_orders)
        CSV.generate(headers: true) do |csv|
          csv << [
            "WHO Created Date",
            "Dispatch Date",
            "WHO Number",
            "Design ID",
            "Ordered Quantity",
            "Good Received",
            "RTV",
            "Used in Orders",
            "Pending"
          ]
    
          warehouse_orders.includes(warehouse_line_items: :design).each do |order|
            order_date = order.created_at.strftime("%d-%m-%Y %I:%M %p")
            dispatch_date = order.dispatched_on.strftime("%d-%m-%Y %I:%M %p")
            who_number = order.number
    
            order.warehouse_line_items.each do |line_item|
              ordered_qty = order.completed? ? (line_item.quantity || 0) : 0
              design_id = line_item.design_id
              total_qty = line_item.quantity || 0
              rtv = line_item.rtv_quantity || 0
              used = line_item.quantity_used || 0
              pending = ordered_qty - rtv - used
              good_received = ordered_qty - rtv
    
              csv << [
                order_date,
                dispatch_date,
                who_number,
                design_id,
                total_qty,
                good_received,
                rtv,
                used,
                pending
              ]
            end
          end
        end
      end
  end