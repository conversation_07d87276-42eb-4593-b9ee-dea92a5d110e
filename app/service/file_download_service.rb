class FileDownloadService
  def self.convert_url_to_uploaded_file(file_url, filename)
    file_url = file_url.gsub(/dl=0/, 'dl=1')
    file = OpenURI.open_uri(file_url)  

    temp_file = Tempfile.new([filename, ".xlsx"])
    temp_file.binmode
    temp_file.write(file.read)
    temp_file.rewind

    ActionDispatch::Http::UploadedFile.new(
      tempfile: temp_file,
      filename: filename,
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
  rescue StandardError => e
    e
  end
end
