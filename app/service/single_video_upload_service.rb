class SingleVideoUploadService

    def initialize(email, design_id, link)
      @email = email
      @design_id = design_id
      @link = link
    end
  
    def run
      directory = Fog::Storage.new(
        provider: 'AWS',
        aws_access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
        region: ENV.fetch('AWS_REGION')
      ).directories.new(key: ENV.fetch('S3_BUCKET'))
      begin
        input_url = @link.gsub(/dl=0/, 'dl=1')
        downloaded_file = Down.download(input_url)
        output_path = Rails.root.join('tmp', "compressed_#{SecureRandom.uuid}.mp4")

        movie = FFMPEG::Movie.new(downloaded_file.path)

        min_size = 6 * 1024 * 1024   # 6 MB in bytes
        max_size = 10 * 1024 * 1024  # 10 MB in bytes
        current_size = Float::INFINITY
        crf_value = 28

        begin
          movie.transcode(output_path.to_s, {
            video_codec: 'libx264',
            audio_codec: 'aac',
            custom: %W[
              -crf #{crf_value}
              -preset fast
              -vf scale=1280:-2
              -movflags +faststart
              -f mp4
            ]
          })
        
          current_size = File.size(output_path)

          if current_size > max_size
            crf_value += 2  # Increase compression to reduce size
          elsif current_size < min_size
            crf_value -= 2  # Decrease compression to increase quality/size
          end
        end until current_size.between?(min_size, max_size) || crf_value >= 51 || crf_value <= 18


        @design = Design.find @design_id
        @design.update_columns(has_video: true, video_link: @link)
        filename = "product-video/#{@design.id}.mp4"
        existing_file = directory.files.get(filename)
        if existing_file.present?
          existing_file.destroy
        end
        file = directory.files.create(
          key: filename,
          body: File.open(output_path, 'rb'),
          public: true,
          content_type: 'video/mp4'
        )
        File.delete(output_path)
      rescue StandardError => e
        DesignerMailer.send_progress_notification("Design Video Upload Failed for design #{@design_id} due to #{e.message}",@email).deliver_now!
      end
    end
end