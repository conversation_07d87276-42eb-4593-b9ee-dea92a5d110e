module SmsNotification
  class NotificationService

    def self.notify(phone, template, template_base = 'TEMPLATE_BASED',  provider = '24x7')
      case provider
      when '24x7'
        SmsNotification::TwentyFourSevenService.new(phone, template, template_base).deliver
      when 'alert_box'
        SmsNotification::AlertBox.new(phone, template).deliver
      end
    end

    def self.notify_later(phone, template, template_base = 'TEMPLATE_BASED', provider = '24x7')
      SmsNotification::NotificationService.sidekiq_delay(queue: 'critical').notify(phone, template, template_base, provider)
    end

  end

end