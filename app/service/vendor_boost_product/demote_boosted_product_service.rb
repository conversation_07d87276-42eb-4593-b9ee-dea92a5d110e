module VendorBoostProduct
  class DemoteBoostedProductService
    def initialize(boosted_design_id)
      @boosted_design = BoostedDesign.find_by(id: boosted_design_id)
      return unless @boosted_design

      @design = @boosted_design.design
      @category = @boosted_design.category
    end

    def call
      return false unless @boosted_design
      
      ActiveRecord::Base.transaction do
        @boosted_design.expire
        update_design_rank
        design_reindex
        BatchIndexJob.perform_async
      end

      true
    rescue StandardError => e
      Rails.logger.error("DemoteBoostedProductService failed: #{e.message}")
      false
    end

      
    def update_design_rank
      previous_states = JSON.parse(@boosted_design.previous_states || '{}')
      grading_tags = GradingTag.where(grading_taggable_type: "Category", grading_taggable_id: @boosted_design.category_id)
  
      grading_tags.each do |grading_tag|
        design_grading_tag = grading_tag.design_grading_tags.find_by(design_id: @boosted_design.design_id)
        if design_grading_tag
          if previous_states[grading_tag.name]
            design_grading_tag.update!(rank: previous_states[grading_tag.name])
          else
            design_grading_tag.destroy!
          end
        end
      end
  
      Rails.logger.info("Successfully unranked design #{@boosted_design.design_id}")
      @boosted_design.destroy! # later Will Implement Analytics for this

    rescue StandardError => e
      Rails.logger.error("Failed to unrank design #{@boosted_design.design_id}: #{e.message}")
      ExceptionNotify.sidekiq_delay.notify_exceptions('Critical Error: Failed to Demote Boosted Desing', "#{@boosted_design.design_id}: #{e.message}", {})
    end


    def design_reindex
      Sunspot.index(@design)
      Sunspot.commit
    end

  end
end
