module VendorBoostProduct
  class EnqueueProductService
    def initialize(design_id)
      @design = Design.find_by(id: design_id)
      return unless @design
  
      @designer = @design.designer
      @boost_config = @designer.designer_boost_config
      @category = @design.boostable_category
    end
  
    def call
      return false unless @design && @category && @design.eligible_for_boost?
  
      ActiveRecord::Base.transaction do
        boosted_design = BoostedDesign.create!(
          design: @design,
          designer: @designer,
          category_id: @category.id,
          boost_fee: @boost_config.boost_fee,
        )
        VendorBoostProduct::CreateAdjustmentService.new(boosted_design).call
      end
      true
    rescue ActiveRecord::RecordInvalid => e
      message = "Product Boost Enqueue failed for design_id: #{@design.id} due to validation: #{e.message}"
      Rails.logger.error(message)
      ExceptionNotify.sidekiq_delay.notify_exceptions('BoostProductService failed', message, {})
      false
    rescue StandardError => e
      @design.boosted_designs.active.destroy_all if @design.currently_boosted?
      Rails.logger.error("Product Boost Enqueue failed: #{e.message}")
      ExceptionNotify.sidekiq_delay.notify_exceptions('BoostProductService failed', "#{e.message}", {})
      false
    end
  end
  
end
