# Helper code for designs views
module Designs<PERSON><PERSON><PERSON>

  def check_for_message(addon_type_val,is_anarkali)
    ( (is_anarkali || (['Lehenga','SalwarKameez'].include?(@design.designable_type))) && (['Semi Stitched Material','Standard Stitching','Custom Stitching'].include?(addon_type_val)) ) ||
    ( (@design.designable_type == 'Saree') && (['Regular Blouse Stitching','Custom Blouse Stitching', 'Pre-Stitched Saree','Shapewear'].include?(addon_type_val)) )
  end

  def allowed_roles
    %w(admin marketing development senior_marketing
       accounts_admin vendor_team stitching seo super_admin senior_vendor_team business_analyst)
  end

  def grading_access
    %w(senior_marketing marketing super_admin)
      .include?(current_account.role.try(:name))
  end

  def can_show_design_details?
    @can_show_design_details ||= account_signed_in? && current_account.role.present? && (allowed_roles.include?(current_account.role.name))
  end

  def preload_design_performance_metric_detail(designs)
    if can_show_design_details?
      ActiveRecord::Associations::Preloader.new.preload(designs, :performance_metric_values, PerformanceMetricValue.where(metric_name: 'sales_30'))
    end
  end

  def design_category_url(design)
    category = design.categories.first
    category_name = category.parent.try(:name) || category.name
    category_name.present? ? store_search_path(kind: category_name.downcase) : root_path
  end

  def get_designable_data(designable,design_spec_hash,spec_group)
    designable.attributes.except("id", "created_at", "updated_at").each do |key, val|
      if val.present?
        key = key == 'blouse_image' ? 'Blouse as per image' : key.humanize
        key.downcase!
        val = val == true ? 'Yes' : val.titleize
        spec_val = spec_group.find{|spec| key.include?(spec)}
        target_key = spec_val.presence || 'other_data'
        next if (key_value = DUPLICATE_DESIGNABLE[designable.class.to_s].try(:[], key.gsub(' ', '_'))).present? && design_spec_hash[target_key].try(:[], key_value.to_s.downcase).present?
        if (p_val = design_spec_hash[target_key][key]).present?
          p_val.concat(", #{val}") if p_val.exclude?(val)
        else
          design_spec_hash[target_key][key] = val
        end
      end
    end
  end

  def update_spec_hash_with_empty_dupatta_if_na(spec_hash,design)
    spec_hash.transform_values do |properties|
      (properties.is_a?(Hash) && properties.keys.any? { |key| key.match(/dupatta|bottom/i) } && (!design.categories.map { |category| category.name.downcase }.include?('kurta-sets') || properties.values.map(&:downcase).include?('na'))) ? {} : properties
    end
  end

  def get_property_data(properties,design_spec_hash,spec_group)
    design_spec_hash[:seo_keys] = {}
    properties.group_by(&:property_id).each do |name, values|
      value = ''
      values.each{|v| value.concat(",#{v.p_name || v.name}")}
      v_name = values.first.property.name
      p_name = values.first.property.p_name.downcase
      value = value.sub(',','').gsub(/[,]+$/, '').split(',').uniq.join(',')
      if (spec_val = spec_group.select{|spec| v_name.include?(spec)}).present?
        design_spec_hash[spec_val.first][p_name]= value
      elsif ['stitching','occasion'].include?(v_name.to_s.downcase)
        design_spec_hash[v_name.to_s.downcase.to_sym] = value
      else
        if (p_val = design_spec_hash['other_data'][p_name]).present?
          p_val.concat(", #{value}") if p_val.exclude?(value)
        else
          design_spec_hash['other_data'][p_name] = value
        end
      end
      if ['color', 'top_color', 'saree_color', 'kameez_color', 'lehenga_color'].include?(v_name)
        design_spec_hash[:seo_keys]['color'] = value
      end
    end
  end

  def get_spec_data(design,category,spec_group=[])
    ActiveRecord::Associations::Preloader.new.preload(design, :property_values => :property) ; nil
    design_spec_hash = {'other_data' => {}}
    properties,designable = design.property_values, design.designable
    spec_group.each{|spec| design_spec_hash[spec] = Hash.new('') }
    design_spec_hash['other_data'].default = ''
    design.attributes.extract!("region","accessories","pattern","embellish").each do |key, val|
      design_spec_hash['other_data'][key.downcase] = val  if val.present?
    end
    get_property_data(properties, design_spec_hash, spec_group) if properties.present?
    get_designable_data(designable, design_spec_hash, spec_group) if designable.present?
    design_spec_hash = update_spec_hash_with_empty_dupatta_if_na(design_spec_hash,design)
    return design_spec_hash
  end

  def get_key_spec_data(design_spec_hash,design)
    design_spec_hash = update_spec_hash_with_empty_dupatta_if_na(design_spec_hash,design)
    Design::DESIGN_KEY_SPEC_SUB_GROUP[@design.designable_type.to_s].to_a.collect do |spec, properties|
      property_hash = properties.collect do |property|
        property_value = design_spec_hash[spec].to_h.find{|p_name, p_value| p_name.downcase.include?(property)}.try(:second)
        [property, property_value] if property_value.present?
      end.compact.to_h
      [spec, property_hash] if property_hash.present?
    end.compact.to_h
  end

  def get_youtube_video_link(viewable_block)
    if (video = viewable_block.video).present?
      youtube_link = video.try(:active_video_links).try(:first).try(:link)
      youtube_link.split('watch?v=')[1] if youtube_link.present?
    end
  end
  
  def check_for_label(atv_name)
    case atv_name
      when 'pre-stitched saree'
       return 'Recommended'
      when 'shapewear'
       return 'New'
      else 
       return ""
    end
  end
  
  def plus_size_info_message(icon,class_name,atv_name)
    if ['Regular Blouse Stitching','Custom Blouse Stitching'].include?(atv_name)
      content_tag :div, :class => class_name do
        content_tag :a, icon, href: 'javascript:void(0)', title: render(partial: '/shared/message_for_plus_size'), data: {toggle: 'tooltip',html: true, placement: 'right',container: 'body'}, class: 'info_message'
      end 
    end
  end
  
  def generate_drop_down(addon_option_values)
    addon_values=addon_option_values.first
    addon_option_type_id=addon_values.addon_option_type['id']
    default_select_value=[addon_values.addon_option_type['p_name'],"0"]
    select_tag "atv_#{addon_values.addon_type_value_id.to_s}_option_values_#{addon_option_type_id.to_s}", 
    options_for_select([default_select_value]+addon_option_values.collect{|u| [u.p_name, u.id]}),{:class=> " aov_atv_#{addon_values.addon_type_value_id.to_s} form-control aov_select "}
  end

  def plus_size_color_options(addon_option_values,id_name,class_name)
    option_type=addon_option_values.first.addon_option_type
    content_tag :div, id: id_name, class: class_name do
      concat("#{option_type['p_name']} Additional #{@symbol} #{(option_type['price'] / @conversion_rate).round(2)}")
      concat(render(partial: '/designs/plus_size_color_pallete', locals: {addon_option_values: addon_option_values}))
    end
  end
  def count_of_design_by_region(design_id)
    domestic_count = LineItem.where(design_id: design_id, snapshot_country_code: 'IN').where.not(designer_order_id: nil).count

    international_count = LineItem.where(design_id: design_id).where.not(snapshot_country_code: 'IN',designer_order_id: nil).count

    {
      domestic_count: domestic_count,
      international_count: international_count
    }
  end

  def display_business_name_with_address(designer,include_name= true)
    details = []
    details << (designer.business_name.presence || designer.name) if include_name
    details += [
      designer.business_street.presence || designer.street,
      designer.business_city.presence || designer.city,
      designer.business_state.presence || designer.state,
      designer.business_country.presence || designer.country,
      designer.business_pincode.presence || designer.pincode
    ]
    details.map(&:to_s).map(&:titleize).join(", ")
  end
end
