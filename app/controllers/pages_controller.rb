class PagesController < ApplicationController
  layout :determine_layout, only: :designer_help_center
  load_and_authorize_resource :support_text, only: :designer_help_center

  def about
    @integration_status = "new"
    @seo = SeoList.where(label: 'about_page').first
  end

  def bx
    @integration_status = "new"
  end

  def terms
    @integration_status = "new"
  end

  def privacy
    @integration_status = "new"
  end

  def contact
  end

  def text_me
  end

  def terms_and_conditions_vendors
  end

  def prohibited
  end

  def vendor_agreement
    @t_rate = 45
    unless request.post?
      @designer = current_account.designer if current_account.present? && current_account.designer?
      redirect_to root_path and return unless @designer.present?
      @t_rate = @designer.transaction_rate || 45
    else
      designer = current_account.designer
      @t_rate = designer.transaction_rate || 45
      designer.account.update_column(:terms_of_service, true)
      redirect_to edit_designer_path(designer), notice: 'Please setup your profile.'
    end
  end

  def sell
    @integration_status = "new"
  end
  def bulk_order_inquiry
    @integration_status = "new"
  end
  # define the franchise function
  def franchise
    @integration_status = "new"
    @seo = SeoList.where(label: 'franchise_page').first
  end

  def sell_page_form
    params.merge!(is_app_request? ? {source: 'app', authenticity_token: 'app'} : {source: 'web'})
    if is_app_request? || session[:email].blank?
      response = Account.build_designer_account(params)
      if is_app_request?
        if (account = response[:designer_account]).present?
          account.skip_confirmation!
          token = login_without_confirmation(account)
          response.merge!({'access-token'=> token})
        end
        render(json: response)
      else
        session[:email] = params[:_accounts_registrations_email] if response[:status]
        redirect_to page_path(:sell), notice: response[:notice]
      end
    end
  end

  def show_designer
    @designer = Designer.find_by_id params[:id]
    head :no_content if @designer.blank?
  end

  def seller_app_login
    account = Account.find_by_email(designer_login_params[:email])
    render json: { error: 'Account does not exist' }, status: :unprocessable_entity and return unless account
    if account.valid_password?(designer_login_params[:password])
      token = login_without_confirmation(account)
      render json: {'access-token'=>token, 'designer_id'=>account.designer.id}, status: 200
    else
      render json: { error: 'Password is invalid' }, status: :unprocessable_entity
    end
  end

  def seller_app_logout
    sign_out(current_account)
    head :ok
  end

  def designer_discounts
    @integration_status = "new"
    @designer_discounts = Designer.active_additional_discount_with_all.order("additional_discount_percent DESC").limit(36)
  end

  def careers
  end

  def rakhi  
    render :layout => false 
  end

  def landing
    @integration_status = "new"
    @landing = Landing.where(label: params[:landing],category_landing: true).first
    if @landing.present?
      @nav_tabs = @landing.nav_tabs.includes(:graded_nav_blocks).collapsed.graded
      @tabs = @landing.nav_tabs.normal.graded.includes(:graded_nav_blocks)
      @widgets = @landing.widgets.includes(:graded_nav_blocks).graded
      if @landing.side_menu?
        side_menu_sort = request.xhr? ? params[:side_menu_sort].to_i : @landing.side_menu_sort
        if params[:landing].include?('gemstones')
          gemstone_property=Property.where(name: 'gemstones').first
          @menu_mapping = Property.get_property_value_mapping(['gemstones'], bulk_upload: false)[:gemstones].map{|k,v| [v,k.to_s]}.to_h
          case side_menu_sort
          when 0
            @menu_list = gemstone_property.get_designs_by_count(params[:landing])
          when 1
            @menu_list = gemstone_property.get_designs_by_name(params[:landing])
          end
          @menu_list_name = "Gemstones"
        else
        @menu_mapping = Category.get_name_to_ids
        category = Category.find_by_namei(params[:landing])
          case side_menu_sort
          when 0
            @menu_list = category.get_menu_list_by_count(params[:landing])
          when 1
            @menu_list = category.get_menu_list_by_name(params[:landing])
          end
          @menu_list_name = "Categories"
        end
      end
      @seo = SeoList.where(label: params[:landing]+'_landing').first
      respond_to do |format|
        if request.xhr?
          format.html {render partial: 'pages/side_menu'}
        else
          format.html
        end
      end
    else
      redirect_to root_url
    end
  end

  def checkcod
    @location = Location.where(:pincode => params[:pincode]).first
    @pincode = params[:pincode]
    render :file => '/pages/cod.js', :formats => [:js]
  end

  def track
    @integration_status = "new"
  end

  def checkgharpay
    @pincode = params[:pincode]
    courier = Courier.where(:pincode => params[:pincode]).first
    if courier
      @exists = courier.cbd == 'Y'
    else
      @exists = false
    end
    @total_ok = false
    if @cart.items_total_price > GHARPAY_MIN_ORDER
      @total_ok = true
    end

    render :file => '/pages/gharpay.js', :formats => [:js]
  end

  def show_order
    if params[:number].present?
      order_number = params[:number].upcase.gsub(/[^A-Z0-9]/,'')
      order = Order.where(number: order_number)
      if order.present?
        redirect_to order_url(params[:number])
      else
        redirect_to request.referer.present? ? :back : root_url, notice: "This order number '#{order_number}' does not exists."
      end
    else
      redirect_to '/404'
    end
  end

  def wholesale_enquiry
    # @wholesale = Wholesale.new
    redirect_to '/404'
  end

  def add_to_contact
  end

  def stitching_information
    @integration_status = "new"
  end

  def process_help_center_query
    input_params = request.params[:mobile_params] || params.presence
    message = HelpCenterQueryNotificationService.new(input_params).notify_support_team
    respond_to do |format|
      if input_params[:from_mobile] == 'true'
        format.json { render json: { notice: message } }
      else
        format.html { redirect_to request.env["HTTP_REFERER"], notice: message }
      end
    end
  end

  def post_freshdesk_ticket
    input_params = request.params[:mobile_params] || params.presence
    if input_params[:options].present? && input_params[:options].present?
      subject = 'Query: '+ input_params[:issue_text_help_center]
      subject << " > Order No. #{input_params[:order_number_text]}" if input_params[:order_number_text].present?
      subject << " > Coupon No. #{input_params[:coupon_id_field]}" if input_params[:coupon_id_field].present?
      input_params[:order_number_text]='Query' unless input_params[:order_number_text].present?
      freshdesk_api_url  = "https://mirraw.freshdesk.com/api/v2/tickets"
      headers = {'Authorization' => Base64.encode64(FRESHDESK_API_KEY),'Content-Type' => 'application/json'}
      if input_params[:email_id_help_center].present? && input_params[:message_content].present?
        json_payload = { status: 2,
                        priority: 1,
                        description: input_params[:message_content][0],
                        subject: subject,
                        email: input_params[:email_id_help_center],
                        custom_fields: {followup: 'No', order_number: input_params[:order_number_text]}}
        if input_params[:designer_ticket] == 'true'
          json_payload[:group_id] = 3000004532
          json_payload[:responder_id] = 3011800663
          json_payload[:custom_fields][:country_name] = 'India'
        else
          # issue_type = input_params[:type_of_issue_help_center].to_s
          json_payload[:custom_fields][:country_name] = ([input_params[:country].try(:strip),session[:country][:name]] & (Order::PRIORITY_COUNTRIES + Order::NON_PRIORITY_COUNTRIES)).first.presence || 'Other'
          json_payload[:custom_fields].merge!(:tagging => input_params[:options], :refund => input_params[:sub_option_1])
          if(input_params[:sub_option_2]).present?
             json_payload[:custom_fields].merge!(return: input_params[:sub_option_2])
          end
        end
        options = {headers: headers, body: json_payload.to_json}
        response = HTTParty.post(freshdesk_api_url,options)
        if response.parsed_response['errors'].present?    
          ExceptionNotify.sidekiq_delay.notify_exceptions('Freshdesk Ticket not created',(response.parsed_response['description'].presence || 'Ticket was not posted on Freshdesk'), { error: response.parsed_response['errors'].inspect, params: input_params })
          message = 'Mail could not be sent please send as an <NAME_EMAIL> for the same.'
        else
          message = 'Ticket Has been created successfully We will get back to you.'
          input_params[:ticket_id] = response.parsed_response['id'] if response.parsed_response['id'].present?
          FreshdeskTicket.create_update_ticket(input_params)
        end
      else
        message = 'Email Id and message can not be blank'
      end
    else
      message='Invalid parameters'
    end
    if input_params[:from_mobile]=='true'
      render json: {message: message}
    else
      redirect_to (request.env["HTTP_REFERER"] || (input_params[:designer_ticket] == 'true' ? designer_help_center_path : pages_faq_path)), notice: message
    end
  end

  def help_center
    @help_center_region = %w(inr rs).include?(@symbol.downcase) ? "domestic" : "international"
    @seo = SeoList.where(label: 'help_center_page').first
    if @help_center_region == "domestic"
      @headers = HelpCenterHeader.preload(domestic_support_texts: :child_support_texts)
      @support_text_of = "domestic"
    else
      @headers = HelpCenterHeader.preload(international_support_texts: :child_support_texts)
      @support_text_of = "international"
    end
    gon.account_present = current_account.present?
    gon.email = current_account.try(:email)
    @integration_status = "new"
  end

  def designer_help_center
    if current_account.present? && (current_account.designer? || current_account.admin?)
      @designer = current_account.designer
      @integration_status = "new"
      @faqs = SupportText.designer_faq.select('id,view_flag,category_of_faq,order_for_category,priority,question,answer').
            where('category_of_faq is not null and order_for_category is not null').
            order(:order_for_category,:priority)
      @most_asked = @faqs.to_a.select{|f| f.view_flag}
      questions = {}
      @faqs.each{|faq| questions[faq.question] = faq.id}
      gon.questions = questions
      @faqs = @faqs.group_by(&:category_of_faq)
    else
      flash[:alert] = 'You need to login first'
      redirect_to sellers_sign_in_path
    end
  end

  def faq
    @integration_status ='new'
    @seo = SeoList.where(label: 'faq_page').first
    @faq_region = %w(inr rs).include?(@symbol.downcase) ? "domestic" : "international"
    @quick_links  = SupportText.select('id,category_of_faq,order_for_category,priority,question,answer').
          where('category_of_faq is not null and order_for_category is not null').
          where.not(category_of_faq: 'Help Center').
          where(visible_to_designer: false).
          where(@faq_region +" = true").
          order(:order_for_category)
    gon.questions = Rails.cache.fetch("faq_gon_question_#{@faq_region}", expires_in: CACHE_EXPIRES_IN) do
      questions = {}
      @quick_links.collect{|hash| questions[hash.question] = hash.answer}
      questions
    end

    if params[:category].present?
      gon.selected_category = params[:category].to_i
    end

    if params[:question].present?
      gon.faq_question = SupportText.where(id: params[:question]).first.try(:question)
    end
  end

  def offer_tnc
    @integration_status = "new"
  end

  def price_match_guarantee_tnc
    @integration_status = "new"
  end

  def set_offer_message
    session[:offer_message] = Zlib.crc32 @offer_message if @offer_message.present?
    render nothing: true
  end

  def mirraw_contact_details
    if current_account.present? && (current_account.designer? || current_account.admin?)
      @designer = Designer.where('cached_slug = ? or id = ?', params[:designer_id], params[:designer_id].to_i).first
      render layout: 'seller'
    else
      flash[:alert] = 'You need to login first'
      redirect_to sellers_sign_in_path
    end
  end

  def create_ticket
    if current_account.present? && (current_account.designer? || current_account.admin?)
      @designer = Designer.find_by(cached_slug: params[:designer_id]) || Designer.find_by(id: params[:designer_id].to_i)
      @tickets = Ticket.where(created_by_id: current_account.id,department: 'seller')
      if params[:start_date].present? && params[:end_date].present?
        start_date = Date.parse(params[:start_date])
        end_date = Date.parse(params[:end_date])
        @tickets = @tickets.where(created_at: start_date.beginning_of_day..end_date.end_of_day)
      elsif params[:state].present?
        @tickets = @tickets.where(state: params[:state])
      end
      @tickets = @tickets.order(created_at: :desc).paginate(page: params[:page], per_page: 10)
      render layout: 'seller'
    else
      flash[:alert] = 'You need to login first'
      redirect_to sellers_sign_in_path
    end
  end

  private
  def action_missing(m)
    redirect_to '/404'
  end

  def determine_layout
    current_account.try(:designer?) ? 'seller' : 'admin'
  end

  def login_without_confirmation(account)
    sign_in(account)
    account.designer.try(:monetize_token)
  end

  def designer_register_params
    params.permit('_accounts_registrations_email', 'name', 'source', '_accounts_registrations_password', 'contact_number', '_accounts_registrations_password_confirmation', 'authenticity_token', 'category'=>[])
  end

  def designer_login_params
    params.permit('email', 'password')
  end
end
