
class MirrawAdmin::DesignController < MirrawAdmin::BaseController
  def collection_search
    @designs = []  # Initialize an empty array for design search results

    if params[:search_type] == 'collection' && params[:search_title].present?
      @search_title = params[:search_title]
      @collection_designs = Design.tagged_with(@search_title, on: :collections).pluck(:id)
      @design_count = @collection_designs.count
    elsif params[:search_type] == 'catalogue' && params[:search_title].present?
      @search_title = params[:search_title]
      @catalogue_designs = Design.tagged_with(@search_title, on: :catalogues).pluck(:id)
      @design_count = @catalogue_designs.count
    elsif params[:design_id].present?  # Search by Design ID
      @design_id = params[:design_id]
      design = Design.find_by(id: @design_id)
      if design
        @designs << design
        @collection_tags = design.collection_list  # Get the collections this design is tagged in
        @catalogue_tags = design.catalogue_list  # Get the catalogues this design is tagged in
      end
    end
  end

  # Remove Designs from Collection or Catalogue
  def remove_designs_from_collection
    remove_type = params[:remove_type] || 'collection'  # Default to collection
    if params[:collection_title].present? && params[:design_ids].present?
      title = params[:collection_title]
      design_ids = params[:design_ids].split(/\r?\n|,/)
      designs = Design.where(id: design_ids)
      designs.each do |design|
        if remove_type == 'collection'
          design.collection_list.remove(title)
        elsif remove_type == 'catalogue'
          design.catalogue_list.remove(title)
        end
        design.save
      end
      flash[:notice] = "Designs have been removed from #{remove_type} successfully."
    else
      flash[:alert] = "Please provide both a title and design IDs."
    end
    redirect_to collection_search_mirraw_admin_design_index_path
  end

  def upload_design_discount

  end

  def update_design_discount
    csv_file = params[:csv_file]
    filename = "design_update-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
    if csv_file.present? && csv_file.content_type == 'text/csv'
      begin
        directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
        fog_object = directories.files.create(
          key: filename,
          body: csv_file.read,
          public: true
        )
        csv_file_path = fog_object.public_url
        SidekiqDelayClassSpecificGenericJob.perform_async("UpdateDesignService","run",{csv_file_path: csv_file_path,skip_object_creation: true})
        flash[:success] = "CSV file Uploaded successfully for designs. It will take some time to reflect the changes"
        redirect_to upload_design_discount_mirraw_admin_design_index_path
      rescue => exception
        flash[:notice] = "Please upload a valid CSV file."
        redirect_to upload_design_discount_mirraw_admin_design_index_path
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
      redirect_to upload_design_discount_mirraw_admin_design_index_path
    end
  end 

  def update_variant_price
    csv_file = params[:csv_file]
    filename = "variant_update-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
    if csv_file.present? && csv_file.content_type == 'text/csv'
      begin
        directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
        fog_object = directories.files.create(
          key: filename,
          body: csv_file.read,
          public: true
        )
        csv_file_path = fog_object.public_url
        SidekiqDelayClassSpecificGenericJob.perform_async("UpdateVariantService","run",{csv_file_path: csv_file_path,skip_object_creation: true})
        flash[:success] = "CSV file Uploaded successfully for designs. It will take some time to reflect the changes"
        redirect_to upload_design_discount_mirraw_admin_design_index_path
      rescue => exception
        flash[:notice] = "Please upload a valid CSV file."
        redirect_to upload_design_discount_mirraw_admin_design_index_path
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
      redirect_to upload_design_discount_mirraw_admin_design_index_path
    end
  end

  def upload_rpv_csv
    csv_file = params[:csv_file]
    if csv_file.present? && csv_file.content_type == 'text/csv'
        directories = AwsOperations.get_directory(bucket: 'manual-grading-bucket', new_connection: false)
        filename = "grading-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
        AwsOperations.create_aws_file(filename,csv_file)
        csv_file_path = AwsOperations.get_aws_file_path(filename)
        SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("RevenuePerViewService", 
                                                                                "run", 
                                                                                { csv_file_path: csv_file_path,
                                                                                  current_account: current_account.id,
                                                                                  skip_object_creation: true}
                                                                              )
        flash[:success] = "CSV file Uploaded successfully. It will take some time to reflect the changes"
        redirect_to manual_grading_mirraw_admin_gradings_url
    else
        flash[:notice] = "Please upload a valid CSV file."
        redirect_to manual_grading_mirraw_admin_gradings_url
    end
  end
  
  def mark_design_premium
    if params[:design_ids].present? && params[:premium]
      premium_value = params[:premium]
      design_ids = params[:design_ids].split(/\r?\n|,/)
    elsif params[:designer_id].present? && params[:premium]
      designer_id = params[:designer_id]
      premium_value = params[:premium]
    end
    if design_ids.present?
      update_count = Design.where(id: design_ids).update_all(premium: premium_value)
    elsif designer_id.present?
      update_count = Design.where(designer_id: designer_id).update_all(premium: premium_value)
    else
      return render 'mark_design_premium'
    end
    flash[:notice] = update_count > 0 ? "Designs have been marked premium: #{premium_value}." : "Failed to update premium status."
    render 'mark_design_premium'
  end

  def add_custom_tags_to_design_create
    csv_file = params[:csv_file]
    tag_id = params[:tag]
    scope_name = params[:scope]
    action = params[:action_type]
    if csv_file.present? && csv_file.content_type == 'text/csv'
      filename = "design_update-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
      begin
        directories = AwsOperations.get_directory(bucket: 'test-lightning-deal', new_connection: true)
        fog_object = directories.files.create(
          key: filename,
          body: csv_file.read,
          public: true
        )
        csv_file_path = fog_object.public_url
        SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("UpdateCustomTagsService", "process_csv", { csv_file_path: csv_file_path, tag_id: tag_id, scope: scope_name, action: action, skip_object_creation: true })
        flash[:success] = "CSV file Uploaded successfully for designs. It will take some time to reflect the changes"
      rescue => exception
        flash[:notice] = "Please upload a valid CSV file."
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
    end
    render 'add_custom_tags_to_design'
  end
  
  def update_designs_bulk
    csv_file = params[:csv_file]
    if csv_file.present? && csv_file.content_type == 'text/csv'
      filename = "design_values_update-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
      begin
        directories = AwsOperations.get_directory(bucket: 'test-lightning-deal', new_connection: true)
        fog_object = directories.files.create(
          key: filename,
          body: csv_file.read,
          public: false
        )
        csv_file_path = fog_object.url(Time.now + 86400)
        BulkDesignUpdateJob.perform_async(csv_file_path)
        flash[:success] = "CSV file Uploaded successfully for designs. It will take some time to reflect the changes"
      rescue => exception
        flash[:notice] = "#{exception.message}"
      end
    else
      flash[:notice] = "Please upload a valid CSV file."
    end
    redirect_to upload_design_discount_mirraw_admin_design_index_path
  end

  def add_custom_tags_to_design
    
  end
end
