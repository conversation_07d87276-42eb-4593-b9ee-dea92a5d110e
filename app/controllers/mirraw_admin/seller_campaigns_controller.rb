class MirrawAdmin::SellerCampaignsController < MirrawAdmin::BaseController
    
  before_action :set_seller_campaign, only: %i[show ]

  def campaign_portal
    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]).beginning_of_day
      end_date = Date.parse(params[:end_date]).end_of_day
      @seller_campaigns = SellerCampaign.where('start_date >= ? AND end_date <= ?', start_date, end_date).paginate(page: params[:page], per_page: 10)
    else
      @seller_campaigns = SellerCampaign.order(created_at: :desc).paginate(page: params[:page], per_page: 10)
    end
  end

  def show
    @seller_campaign = SellerCampaign.find(params[:id])
    @designer_campaign_participations = @seller_campaign.designer_campaign_participations
  end

  def new
    @seller_campaign = SellerCampaign.new
  end

  def create
    @seller_campaign = SellerCampaign.new(seller_campaign_params)
    if @seller_campaign.save
      flash[:notice] = "Seller campaign created successfully."
      redirect_to campaign_portal_mirraw_admin_seller_campaigns_path
    else
      flash.now[:alert] = @seller_campaign.errors.full_messages.to_sentence
      render :new
    end
  end

  def destroy
    @seller_campaign = SellerCampaign.find(params[:id])
    @seller_campaign.destroy
    redirect_to campaign_portal_mirraw_admin_seller_campaigns_path, notice: 'Seller Campaign successfully deleted.'
  end

  private

  def set_seller_campaign
    @seller_campaign = SellerCampaign.find params[:id]
  end

  def seller_campaign_params
    params.require(:seller_campaign).permit(:name, :description, :start_date, :end_date)
  end
end