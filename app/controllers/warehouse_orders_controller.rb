class WarehouseOrdersController < ApplicationController
  before_filter :authenticate_account!
  authorize_resource
  layout 'admin'
  before_filter :ensure_admin_account, except: [:warehouse_order_csv_report]
  def create_warehouse_order
    if params[:design_ids].present?
      design_ids = params[:design_ids][0].split(',').map(&:to_i)
      design = Design.preload(variants: [:option_types,:option_type_values]).published.where(id: design_ids).
      joins(:designer).where(designer: { unicommerce_enabled: [false, nil]})
      @designs = []
      design.each do |d|
        d.variants.present? ? design_ids.count(d.id).times{@designs << d} : @designs << d
      end
      @not_found_designs = (design_ids - @designs.collect(&:id))
    end
  end

  def warehouse_cancel_orders
    @warehouse_start_date = params[:warehouse_start_date].present? ? params[:warehouse_start_date].to_date.beginning_of_day : (3.month.ago).to_date.beginning_of_day
    @warehouse_end_date = params[:warehouse_end_date].present? ? params[:warehouse_end_date].to_date.end_of_day : (DateTime.now).to_date.end_of_day

    query = LineItem.select('line_items.id, line_items.design_id, orders.number AS order_number, line_items.quantity AS used_quantity, warehouse_orders.number AS wo_number, designer_orders.id AS dos_id, designer_orders.state AS dos_state, line_items.available_in_warehouse, orders.created_at AS order_date, designer_orders.designer_id AS designer_id, line_items.reverse_product_received_by, line_items.reverse_product_received_on').joins(:warehouse_orders, order: :designer_orders).where(designer_orders: { state: ['canceled', 'buyer_returned', 'rto'] }).where(line_items: { available_in_warehouse: true })

    if params[:order_number].present?
      query = query.where(['orders.number = ?',params[:order_number]])
    elsif params[:design_id].present?
      query = query.where(['line_items.design_id = ?',params[:design_id]])
    else
      query = query.where("orders.created_at BETWEEN ? AND ?", @warehouse_start_date, @warehouse_end_date)
    end
    @line_items_used_from_warehouse_order = query.distinct.paginate(page: params[:page], per_page: 20)
  end

  def submit_warehouse_order
    warehouse_data = WarehouseOrder.build_warehouse_line_items(params,current_account.id)
    if warehouse_data[:error].present?
      @errors = warehouse_data[:error]
      @designs = warehouse_data[:designs]
    else
      @warehouse_orders = warehouse_data[:warehouse_orders]
      @designs = warehouse_data[:designs]
    end
    redirect_to warehouse_order_index_path, notice: @errors
  end

  #warehouse_orders/:order_number/order_detail
  def show
    @warehouse_order = WarehouseOrder.where(number: params[:number]).preload(warehouse_line_items: [rack_lists_warehouse_line_items: :rack_list, variant: :option_type_values, design: :images, warehouse_size_items: [:stitching_sent_by, :tailoring_bag_relations, :size_bucket, rack_lists_warehouse_size_items: :rack_list]]).first
    @line_items_used_from_warehouse_order = LineItem.select('design_id, orders.number as order_number, quantity as used_quantity , orders.created_at::DATE as order_date, orders.state as order_state').joins(:order, :warehouse_orders).where(warehouse_orders: {number: params[:number]}).order('orders.created_at desc').paginate(page: params[:page],per_page: 10)
    @rtv_shipments = @warehouse_order.try(:rtv_shipments)
    click_post_obj = ClickpostReturnToVendor.new
    shipper_array = click_post_obj.fetch_recommended_shipper(nil,@warehouse_order)[@warehouse_order.id]
    @shippers = Shipper.where(clickpost_shipper_id: shipper_array).pluck("name", :id).to_h
    redirect_to root_url,notice: 'Warehouse Order Not Found' unless @warehouse_order.present?
  end

  def warehouse_order_csv_report
    if @warehouse_order = WarehouseOrder.find_by_number(params[:number])
      ActiveRecord::Associations::Preloader.new.preload(@warehouse_order , line_items: :order)
      send_data @warehouse_order.quantity_used_in_order_report(format: :csv), filename: "#{@warehouse_order.number}_#{Time.now.strftime('%m_%d_%H_%M_%S')}_report.csv"
    else
      redirect_to root_url,notice: 'Warehouse Order Not Found'
    end
  end

  def index
    state = params[:state].present? ? ['state = ?',params[:state]] : ''
    number = params[:order_number].present? ? ['number = ?',params[:order_number]] : ''
    joins,design_id = [],''
    if params[:design_id].present?
      joins = :warehouse_line_items
      design_id =  ['warehouse_line_items.design_id = ?',params[:design_id]]
    end
    @warehouse_start_date = params[:warehouse_start_date].present? ? (DateTime.parse(params[:warehouse_start_date]).to_date.beginning_of_day) : (1.month.ago).to_date.beginning_of_day
    @warehouse_end_date = params[:warehouse_end_date].present? ? (DateTime.parse(params[:warehouse_end_date]).to_date.end_of_day)  : (DateTime.now).to_date.end_of_day

    if params[:generate_csv] == "true"
      parameters = {state: state, number: number, joins: joins, design_id: design_id, warehouse_start_date: @warehouse_start_date, warehouse_end_date: @warehouse_end_date}
      WarehouseOrder.sidekiq_delay(queue: 'critical')
                    .generate_warehouse_order_csv(current_account.email, parameters)  
    end
    @warehouse_orders = WarehouseOrder.where('warehouse_orders.created_at between ? and  ?',@warehouse_start_date,@warehouse_end_date).where(state).where(number).joins(joins).preload(:warehouse_line_items,:designer,:order_by_account).where(design_id).order('warehouse_orders.id desc').paginate(page: params[:page], per_page: 15)

  end

  def update_quantity
    return_message = {message: 'Quantity not found'}
    if (quantity = params[:quantity].to_i) > 0
      wli = WarehouseLineItem.preload(:warehouse_order).find_by_id(params[:id])
      if %w(completed cancelled).include?(wli.warehouse_order.state)
        return_message[:message] = 'Warehouse Order in Completed Or Cancelled state'
      else
        wli.update_quantity_post_order(quantity,params[:rtv_quantity].to_i)
        return_message[:message] = 'Changed successfully'
      end
    end
    render json: return_message
  end

  def update_rtv_quantity
    if (tailoring_bag = TailoringBagRelation.find_by_id(params[:tailor_bag_id])).present? && (rtv = params[:rtv_qty].to_i) > 0 && rtv <= tailoring_bag.inscanned_count
      tailoring_bag.update_attributes(rtv_count: rtv)
      render json: {remaining_qty: (tailoring_bag.inscanned_count - rtv)}
    else
      render json: {error: 'Wrong Input !'}
    end
  end

  def assign_to_orders
    if (tailoring_bag = TailoringBagRelation.find_by_id(params[:tailor_bag_id])).present? && (size_item = WarehouseSizeItem.find_by_id(params[:size_id])).present? && (tailoring_bag.inscanned_count - tailoring_bag.rtv_count).to_i > 0
      size_item.assign_warehouse_size_to_design(tailoring_bag)
      render json: {message: 'This Size will be available to take orders.'}
    else
      render json: {error: 'No available quantity to assign'}
    end
  end

  def generate_buckets
    @wli = WarehouseLineItem.find_by_id(params[:wli_id])
    @warehouse_order = @wli.warehouse_order
    where_clause = {id: @wli.design.get_eligible_size_buckets}
    SizeBucket.where(where_clause).all.each {|bucket| @wli.warehouse_size_items.build(warehouse_line_item_id: @wli.id, size_bucket_id: bucket.id)}
  end

  def update_warehouse_size
    wli = WarehouseLineItem.find_by_id(params[:warehouse_line_item][:id])
    param_hash = warehouse_item_params
    wli.assign_attributes(param_hash)
    wli.save!
    redirect_to warehouse_order_show_path(number: wli.warehouse_order.number), notice: 'Quantities of sizes updated successfully'
  end

  def merge_racks
    new_rack = RackList.where(code: params[:new_rack_code], description: 'warehouse_items').first
    wo = WarehouseOrder.where(number: params[:number]).preload(:rack_lists_warehouse_line_items).first
    if new_rack.present? && [new_rack.id] != wo.rack_lists_warehouse_line_items.collect(&:rack_list_id).uniq.compact && (quantity_in_rack = wo.rack_lists_warehouse_line_items.compact.sum(&:quantity_present)).present? && (new_rack.max_capacity - new_rack.space_filled) >= quantity_in_rack
      rack_quantities = {}
      lost_quantity_in_rack = wo.rack_lists_warehouse_line_items.compact.sum(&:quantity_lost)
      initial_quantity_in_rack = wo.rack_lists_warehouse_line_items.compact.sum(&:quantity_stored)
      new_rack_list_item = wo.rack_lists_warehouse_line_items.first
      # wo.rack_lists_warehouse_line_items.each{|li| rack_quantities[li.rack_list_id] += li.quantity_present}
      wo.rack_lists_warehouse_line_items.each do |li|
        if !rack_quantities[li.rack_list_id].present?
          rack_quantities[li.rack_list_id] = li.quantity_present
        else
          rack_quantities[li.rack_list_id] += li.quantity_present
        end
      end
      RackList.where(id: rack_quantities.keys).each{|rack| rack.update_column(:space_filled, rack.space_filled - rack_quantities[rack.id])}
      new_rack.reload
      new_rack_list_item.update_columns(rack_list_id: new_rack.id, quantity_present: quantity_in_rack, quantity_stored: initial_quantity_in_rack, quantity_lost: lost_quantity_in_rack)
      new_rack.update_column(:space_filled,quantity_in_rack + new_rack.space_filled)
      remaining_rlwli = wo.rack_lists_warehouse_line_items.where.not(id: new_rack_list_item.id)
      # remaining_rlwli.each{|r| r.warehouse_line_item_joins.update_all(rack_lists_warehouse_line_item_id:  new_rack_list_item.id)}
      remaining_rlwli.each do |r|
        r.warehouse_line_item_joins.each do |wlij|
          li = wlij.line_item
          li.update_column(:rack_list_code, new_rack.code)
          wlij.update_column(:rack_lists_warehouse_line_item_id,  new_rack_list_item.id)
        end
      end
      wo.rack_lists_warehouse_line_items.where.not(id: new_rack_list_item.id).delete_all
      render json: {head: :ok}
    else
      render json: {msg: 'Please give another rack code'}
    end
  end

  def mark_item_received
    WarehouseLineItem.where(id: params[:id]).update_all(status: 'received')
    render json: {head: :ok}
  end

  def mark_warehouse_order_complete
    warehouse_order = WarehouseOrder.find_by_id(params[:id])
    if warehouse_order.try(:can_reached_warehouse?)
      if RackList.select('(max_capacity - space_filled) remaining_space').where('max_capacity > space_filled').where(description: 'warehouse_items').collect{|rl| rl['remaining_space'].to_i}.sum > 10
        if params[:assign_rack] == "true"
          warehouse_order.assign_racks_to_items
          notice = 'Racks Assigned Successfully'
        else
          warehouse_order.reached_warehouse!
          notice = 'Order Marked Complete'
        end
        render json: {head: :ok, msg: notice}
      else
        render json: {error: 'Cannot be assigned to rack due to low rack space.Assign New Racks to the warehouse'}
      end
    else
      render json: {error: 'Order cannot be completed !'}
    end
  end

  def mark_warehouse_order_cancel
    warehouse_order = WarehouseOrder.find_by_id(params[:id])
    warehouse_order.cancel_warehouse_order! if warehouse_order.present? && warehouse_order.can_cancel_warehouse_order?
    render json: {head: :ok}
  end

  def manage_warehouse_inventory   
    design_id={}
    if params[:design_id].present?
      design_id[:id] = params[:design_id].strip
    end
    @designs = Design.preload(:images,variants: :option_type_values).
            joins('left outer join variants on variants.design_id = designs.id').
            where('designs.in_stock_warehouse > 0 OR variants.in_stock_warehouse > 0').
            where(design_id).
            group('designs.id').
            paginate(page: params[:page], per_page: 10)
  end

  def update_warehouse_inventory
    response = {message: 'An error occured'}
    if params[:variant_id].present?
      if (variant = Variant.where(id: params[:variant_id]).first).present?
        response[:message] = WarehouseOrder.update_item_quantity_panel(variant,params[:in_stock_warehouse].to_i,params[:reordering_percent],params[:ordering_quantity])
      end
    elsif (design = Design.where(id: params[:design_id]).first).present?
      response[:message] = WarehouseOrder.update_item_quantity_panel(design,params[:in_stock_warehouse].to_i,params[:reordering_percent],params[:ordering_quantity])
    end
    render json: response
  end

  def manage_warehouse_orders
    order_number = {}
    if params[:order_number].present?
      order_number = {orders: {number: "#{params[:order_number]}"}}
    end
    @line_items = LineItem.preload(:order, designer_order: [line_items: :designer], variant: :option_type_values, line_item_addons: [:size_chart, :addon_type_value]).joins(designer_order: :order).where(available_in_warehouse: true,received: nil,status: nil).where('line_items.created_at > ?', Designer::NO_MONTHS.months.ago).where.not(designer_orders: {state: ['canceled', 'vendor_canceled']}).where(orders: {state: ['sane', 'partial_dispatch']}).where(order_number).paginate(per_page: 40,page: params[:page])
    @warehouse_stock = WarehouseLineItem.select('design_id, variant_id, sum(quantity- quantity_used) as  remain').joins(:warehouse_order).where('state = ? and design_id in (?) and (variant_id is null or variant_id in (?)) and (quantity - quantity_used) > ?', 'completed', @line_items.map(&:design_id), @line_items.map(&:variant_id).compact, 0).group(:design_id, :variant_id).map{|i| [[i.design_id, i.variant_id].compact.join('-'), i.remain]}.to_h
  end

  def warehouse_code_sticker
    @warehouse_item = params[:type].constantize.preload(:warehouse_order,rack_lists_warehouse_line_items: :rack_list).where(id: params[:id]).first
    @warehouse_line_item = params[:type] == 'WarehouseLineItem' ? @warehouse_item : @warehouse_item.warehouse_line_item
    if @warehouse_line_item.present? &&  (@rack_lists_wli = @warehouse_item.rack_lists_warehouse_line_items).present?
      respond_to do |format|
        format.html
        format.pdf do
          render layout: false,
                 pdf: "#{@warehouse_item.id}_#{@warehouse_line_item.design_id}_#{Time.zone.now.strftime('%e%m%M')}",
                 template: 'warehouse_orders/warehouse_code_sticker.html',
                 orientation: 'Landscape'
        end
      end
    else
      redirect_to '/404', :notice => "No designer order found"
    end
  end

  def create_rtv
    options = get_rtv_params(params)
    if params[:number].present?
      w_rtv_shipment, message = create_warehouse_rtv_shipment(options)
    else
      w_rtv_shipment, message = create_warehouse_rtv_shipment(options)
      rtv_shipment = w_rtv_shipment.rtv_shipment
      shipper = Shipper.find_by_name params[:shipper_name]
      warehouse_order = WarehouseOrder.find params[:id].to_i
      warehouse_order_line_items = warehouse_order.warehouse_line_items
      return_items = warehouse_order_line_items.map do |item|
        id = item.id
        value = params["rtv_quantity_#{id}"].to_i
        { id => value }
      end      
      SidekiqDelayClassSpecificGenericJob.set({queue: 'critical'}).perform_async("ClickpostReturnToVendor","create_clickpost_rtv_shipment_for_sor",{"#{DesignerOrder.class}": nil,weight: params[:weight].to_i,rtv_items: return_items,account_id: current_account.id,ref_no: params[:reference_number], skip_object_creation: true}, {"#{shipper.class}": shipper.id}, {"#{warehouse_order.class}": warehouse_order.id},{"#{rtv_shipment.class}": rtv_shipment.id})
    end
    render json: {message: message}
  end

  def get_warehouse_rack_locations
    rack_lists = RackList.warehouse_rack_quantities(params[:design_id], params[:variant_id]).collect{|rack_list| "#{rack_list[:code]}- Quantity: #{rack_list[:quantity_present]} </br>"}
    render json: {message: rack_lists}
  end

  private
  def get_rtv_params(params)
    warehouse_order = WarehouseOrder.find_by_id(params[:id])
    params[:shipper_name] = params[:courier_name] if params[:shipper_name].downcase == 'other'
    wlis = params.map{|keys, item| keys.match(/rtv_quantity_(.*)$/); [$1.to_i, item] if item.present?}.compact.to_h.except(0)
    options = params.slice(:number, :shipper_name, :reason, :weight, :done_by, :rtv_quantities)
    options.merge!({rtv_quantities: wlis, done_by: current_account, warehouse_order: warehouse_order})
  end

  def create_warehouse_rtv_shipment(options)
    message =  'Rtv Shipment created Successfully'
    w_rtv_shipment = WarehouseRtvShipment.create(options)
    message = w_rtv_shipment.errors.join(', ') if w_rtv_shipment.errors.present?
    [w_rtv_shipment, message]
  end

  def ensure_admin_account
    unless current_account.present? && %w(accounts_admin admin operations dispatch super_admin senior_vendor_team business_analyst).include?(current_account.role.try(:name))
      redirect_to root_url, notice: 'Cannot access this page' 
    end
  end

  def warehouse_item_params
    params.require(:warehouse_line_item).permit(warehouse_size_items_attributes: [:size_bucket_id, :quantity])
  end
end
