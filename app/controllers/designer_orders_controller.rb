class DesignerOrdersController < ApplicationController
  include DesignerOrdersHelper
  before_filter :authenticate_account!, :except => [:add_tracking_num, :mark_pickup_done, :dispatch_orders_in_bulk, :check_duplicate_tracking_num, :upload_qc_issue, :generate_combined_awb_unpacks, :get_encrypted_token]
  load_and_authorize_resource :designer, :except => [:add_tracking_num, :mark_pickup_done, :check_duplicate_tracking_num,:add_notes,:add_secondary_tracking_details, :upload_qc_issue, :generate_combined_awb_unpacks, :get_encrypted_token]
  authorize_resource :designer_order, :through => :designer, :except => [:add_tracking_num, :mark_pickup_done, :dispatch_orders_in_bulk,  :add_notes,:add_secondary_tracking_details,:upload_qc_issue, :generate_combined_awb_unpacks, :get_encrypted_token]
  skip_before_filter :verify_authenticity_token, only: [:add_notes, :change_rack,:add_secondary_tracking_details, :upload_qc_issue]
  layout 'seller', only: [:index,:payments,:export_orders,:warehouse_orders]
  # GET /designer_orders
  # GET /designer_orders.json
  rescue_from ActiveRecord::RecordNotFound do |exception|
    action_called = params[:action]
    if ['payments'].include?(action_called)
      redirect_to '/404', :notice => "Designer not found"
    end
  end

  def index
    dates = Date.parse(params['daterange_selector'].split('-')[0]).beginning_of_day..Date.parse(params['daterange_selector'].split('-')[1]).end_of_day rescue Designer::NO_MONTHS.months.ago.beginning_of_day..Date.today.end_of_day
    designer_order_ids = @designer.designer_orders
    order_by_condition = 'designer_orders.id DESC'
    condition_for_pending_line_items = "(state = 'pending' and line_items.available_in_warehouse is not true) OR (state = 'replacement_pending' and line_items.rtv_quantity is null)"
    condition = case params['type_of_filter']
                when 'Order Status'
                  params['track_order_num'] = nil
                  case params[:options_for_filter]
                  when 'All Orders'
                    "designer_orders.state <> 'new' and confirmed_at is not null and (designer_orders.state <> 'canceled' OR (designer_orders.state = 'canceled' and cancel_reason in ('Out of Stock','Vendor canceled')))"
                  when 'canceled'
                    "((cancel_reason in ('Out of Stock','Vendor canceled') and state = 'canceled') or designer_orders.state = 'vendor_canceled') and confirmed_at is not null"
                  else
                    if params[:options_for_filter] == 'pending'
                      designer_order_ids = designer_order_ids.joins(:line_items).where(condition_for_pending_line_items).group(:id)
                      order_by_condition = 'designer_orders.state DESC, ' + order_by_condition
                      states_for_filter = ['pending', 'replacement_pending']
                    else
                      states_for_filter = params[:options_for_filter] 
                    end
                    {state: states_for_filter}
                  end
                when 'Order Number'
                  designer_order_ids = designer_order_ids.joins(:order)
                  {orders: {number: params['track_order_num'].strip.upcase}}
                when 'Tracking Number'
                  {tracking_num: params['track_order_num']}
                when 'Gharpay Orders'
                  designer_order_ids = designer_order_ids.joins(:order)
                  {:orders => {:pay_type => GHARPAY, :state => "pending"}}
                when 'SLA critical'
                  designer_order_ids = designer_order_ids.sla_violated.all
                  order_by_condition = 'designer_orders.confirmed_at DESC' ; {}
                else
                  designer_order_ids = designer_order_ids.joins(:line_items).where(condition_for_pending_line_items).group(:id)
                  order_by_condition = 'designer_orders.state DESC, ' + order_by_condition
                  {state: ['pending', 'replacement_pending']}
                end
    if params[:shipment_state].present?
      designer_order_ids = designer_order_ids.joins(:shipment)
      condition = {shipment: {:shipment_state => params[:shipment_state]}}
    end
    date_range = params['daterange_selector'].present? && params['track_order_num'].blank? ? {created_at: dates} : (params['track_order_num'].present? ? nil : {created_at: Designer::NO_MONTHS.months.ago.beginning_of_day..Date.today.end_of_day})
    date_range = nil if params['type_of_filter'] == 'SLA critical'

    designer_order_ids = designer_order_ids.where(condition).where(date_range).reorder(order_by_condition)# if params[:order_num].blank?
    @designer_orders = designer_order_ids.preload(:delivery_nps_info,:order, :rtv_shipments, :priority_shipper, shipment: [:optional_pickups], :line_items => [[:design => :images], :line_item_addons => :addon_type_value])
                        .paginate(:page => params[:page], :per_page => 10)
    @pickup = @designer.pickups.where(:pickup_date=> Date.today, :pickup_created => false).first
    @cod_designer_shippers = DesignerShipper.where(cod: true,designer_id: @designer.id).uniq.pluck(:shipper_id) if current_account.admin?
    if @pickup.present? && @pickup.status == 'pending' && current_account.designer?
      @shipment_count = Shipment.where('created_at BETWEEN ? and ?', Date.today.beginning_of_day, DateTime.now).where(designer_order_id: @designer_orders.map(&:id)).count
    end
    if params[:commit] == 'Email CSV'
      flash[:notice] = "File will be downloaded and emailed at #{current_account.email}"
      DesignerOrder.sidekiq_delay(queue: 'critical').download_csv(designer_order_ids.map(&:id).uniq, (current_account.designer? ? @designer.email : current_account.email),(params['track_order_num'].blank? && params[:options_for_filter] == 'pending' ? true : false))
    end
    gon.international_ship = DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw'].to_a.map(&:upcase) + ['RAPID DELIVERY', 'FEDEX-MIRRAW']
    gon.domestic_ship      = DOMESTIC_PREPAID_COURIER_AUTOMATION['customer'].to_a.map(&:upcase)

    if params[:track_order_num].present?
      @return_result = ReturnDesignerOrder.joins(designer_order: :order).where(orders: { number: params[:track_order_num] },designer_orders: { designer_id: @designer.id })
    end

    if params[:type_of_filter] == "Order Status"
      @return_result = ReturnDesignerOrder.joins(designer_order: :order).where(designer_orders: { state: state = params[:options_for_filter]},designer_orders: { designer_id: @designer.id })
    end
    
    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @designer_orders }
    end
  end

  def warehouse_orders
    service = WarehouseOrdersService.new(@designer, params)
    result = service.call
  
    if params[:export].present?
      send_data result[:csv_data], filename: result[:filename]
    else
      @warehouse_orders = result
    end
  end

  def payments
    if params[:commit] == 'Know My GST'
      DesignerOrder.sidekiq_delay(queue: 'critical').get_gst_report(@designer.id, (current_account.designer ? current_account.designer.email : current_account.email))
      flash[:notice] = 'Report will be emailed you very shortly.'
      redirect_to (request.env["HTTP_REFERER"] || designers_payments_path(@designer))
    end
    payout_type = {'B2B' => "ship_to = 'mirraw'", 'B2C' => "ship_to <> 'mirraw'"}
    if params[:payout_date].present?
      payout_date = Date.parse(params[:payout_date])
      @designer_orders = @designer.designer_orders.where(designer_payout_status: params[:payout_status]).where('designer_orders.designer_payout_date BETWEEN ? and ?', payout_date.beginning_of_day, payout_date.end_of_day).where(payout_type[params[:payout_type]])
      @adjustments = @designer.adjustments.where(status:params[:payout_status],payout_date: payout_date.beginning_of_day..payout_date.end_of_day).where{notes.not_eq("Negative Commission")}.preload(:payment_order)
    else
      @designer_orders = @designer.designer_orders.where(:state => 'completed').where('designer_orders.designer_payout_status = ? OR designer_orders.designer_payout_status IS NULL', 'unpaid').where(payout_type[params[:payout_type]])
      @adjustments = @designer.adjustments.where(:status => 'unpaid').where{notes.not_eq("Negative Commission")}.preload(:payment_order)
    end
    #@designer_orders = @designer_orders.select('designer_orders.*, shipments.system_shipment_charges as system_shipment_charges').includes(:shipment)

    @dates=@designer.designer_orders.where('designer_orders.designer_payout_status IN (?) ', ['paid', 'processing']).pluck(:designer_payout_date).compact.map{|d| d.strftime('%d-%b-%Y') if d.present?}.uniq
    payout_versions = @designer_orders.pluck(:designer_payout_notes).try(:uniq).try(:compact).to_a.map{|i| i.to_s.match(/.{12}$/).to_s}
    # payout_versions = @designer_orders.collect(&:designer_payout_notes).try(:uniq).try(:compact).to_a.map{|i| i.to_s.match(/.{12}$/).to_s}
    payouts = @designer.payout_managements.select('utr_number,gst_release_amount, payout_amount').where(payout_version: payout_versions).uniq
    @utr_number = payouts.collect(&:utr_number).join(', ')
    @gst_release_amount = payouts.collect(&:gst_release_amount).compact.sum
    @actual_payout = payouts.collect(&:payout_amount).compact.sum rescue 0
    # @total = @designer_orders.to_a.sum(&:total)
    # @hold_amount = @designer_orders.to_a.select{|i| i.gst_status = 'hold'}.sum(&:gst_tax)
    # @payout = @designer_orders.to_a.sum(&:payout) - @hold_amount
    # @system_shipment_charges = @designer_orders.to_a.collect(&:mirraw_shipping_cost).compact.sum
    @dos_totals = @designer_orders.select("sum(total) as total, sum(payout) as payout, sum(case when gst_status = 'hold' then gst_tax else 0 end) as hold_amount, sum(case when ship_to <> 'mirraw' then tcs_tax else 0 end) as tcs_amount, sum(case when ship_to <> 'mirraw' then tds_tax else 0 end) as tds_amount, sum(mirraw_shipping_cost) as system_shipment_charges").reorder('').group(:designer_id).first
    @designer_orders = @designer_orders.select('gst_tax,tcs_tax,tds_tax,gst_status,total,mirraw_shipping_cost,discount,payout,designer_payout_status,designer_payout_notes,designer_payout_date,id,order_id,designer_id,ship_to,designer_payout_notes,created_at,pickup').preload(:payment_order).paginate(page: params[:page], per_page: 50)

  end

  def export_orders
    @integration_status = 'new'
    @start_date = params[:start_date].present? ? (DateTime.parse(params[:start_date]).to_date).strftime("%A, %d %B, %Y") : (7.days.ago.beginning_of_day).strftime("%A, %d %B, %Y")
    @end_date = params[:end_date].present? ? (DateTime.parse(params[:end_date]).to_date + 1.day).strftime("%A, %d %B, %Y")  : 1.day.from_now.strftime("%A, %d %B, %Y")
    @designer_orders = @designer.designer_orders.joins(:order).preload(:payment_order,line_items: [design: :categories]).where('orders.confirmed_at >=? AND orders.confirmed_at <=? and orders.country <> ? and payout <> ?', @start_date , @end_date, 'India', 0).where(:state => 'completed').paginate(page: params[:page], per_page: 50)
  end

  # GET /designer_orders/1
  # GET /designer_orders/1.json
  def show
    @designer_order = @designer.designer_orders.find(params[:id])
    @order = @designer_order.order
    @hide_header = true
    @hide_footer = true
    @show_invoice_from_admin_panel = true
    if params[:rtv].present?
      @rtv = true
    end
    respond_to do |format|
      format.html # show.html.erb
      format.pdf do
        render :layout => false,
               :pdf => @order.number,
               :template => 'designer_orders/show.html'
      end
    end
  end

  def show_warehouse_order
    @warehouse_order = @designer.warehouse_orders.find_by_id(params[:id])
    @warehouse_order_invoice = WarehouseOrderInvoice.new(@warehouse_order)
    respond_to do |format|
      format.html { render :layout => false} # show.html.erb
      format.pdf do
        render :layout => false,
               :pdf => @warehouse_order.number,
               :template => 'designer_orders/show_warehouse_order.html'
      end
    end
  end


  def create_rtv_invoice
    notice = 'Successful'
    if params[:rtv_items].present?
      shipper = Shipper.find params[:shipper_id].to_i
      designer_order = DesignerOrder.find params[:id].to_i
      SidekiqDelayClassSpecificGenericJob.set({queue: 'critical'}).perform_async("ClickpostReturnToVendor","create_clickpost_rtv_shipment",{"#{designer_order.class}": designer_order.id,weight: params[:rtv_weight],rtv_items: params[:rtv_items],account_id: current_account.id,ref_no: params[:reference_number], skip_object_creation: true}, {"#{shipper.class}": shipper.id})
    end
    redirect_to designers_rtv_invoice_url, notice: notice
  end

  def new_rtv_invoice
    if(@designer_order = DesignerOrder.preload(:designer,line_items: [:design, :rtv_shipments]).find_by_id(params[:id])).present?
      @integration_status = 'new'
      @line_items = @designer_order.line_items
      @rtv_shipments = @line_items.collect(&:rtv_shipments).flatten.uniq.select{|rtv_ship| rtv_ship.shipment_type == 'rtv'}
      click_post_obj = ClickpostReturnToVendor.new
      shipper_array = click_post_obj.fetch_recommended_shipper(@designer_order,nil)[@designer_order.id]
      @shippers = Shipper.where(clickpost_shipper_id: shipper_array).pluck("upper(name)", :id).to_h
    else
      flash[:notice] = 'Designer order not found!'
    end
  end

  def mark_received
    designer_order = DesignerOrder.find(params[:id])
    recent_designer_order = DesignerOrder.where("inward_bag_id IS NOT NULL").order(created_at: :desc).first
    if designer_order.update(inward_bag_id: recent_designer_order.inward_bag_id, ship_to: 'mirraw')
      redirect_to order_order_detail_path(designer_order.order), notice: 'Update inward bag successfully!'
    else
      redirect_to order_order_detail_path(designer_order.order), alert: 'Failed to mark inward bag.'
    end
  end

  def show_mailer
    @designer_order = @designer.designer_orders.find(params[:id])
    @order = @designer_order.order
    @designer = @designer_order.designer
    render "order_mailer/dispatch_order_to_designer", :layout => false
  end

  # GET /designer_orders/new
  # GET /designer_orders/new.json
  def new
    @designer_order = DesignerOrder.new

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @designer_order }
    end
  end

  # GET /designer_orders/1/edit
  def edit
    @designer_order = DesignerOrder.find(params[:id])
  end

  # POST /designer_orders
  # POST /designer_orders.json
  def create
    @designer_order = DesignerOrder.new(params[:designer_order])

    respond_to do |format|
      if @designer_order.save
        format.html { redirect_to @designer_order, notice: 'Designer order was successfully created.' }
        format.json { render json: @designer_order, status: :created, location: @designer_order }
      else
        format.html { render action: "new" }
        format.json { render json: @designer_order.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /designer_orders/1
  # PUT /designer_orders/1.json
  def update
    @designer_order = DesignerOrder.find(params[:id])

    respond_to do |format|
      if @designer_order.update_attributes(params[:designer_order])
        format.html { redirect_to designer_designer_orders_path(@designer), notice: 'Package is scheduled now.'}
        format.json { head :ok }
      else
        format.html { redirect_to designer_designer_orders_path(@designer), notice: 'Error occured. Contact Mirraw please.' }
        format.json { render json: @designer_order.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /designer_orders/1
  # DELETE /designer_orders/1.json
  def destroy
    @designer_order = DesignerOrder.find(params[:id])
    @designer_order.destroy

    respond_to do |format|
      format.html { redirect_to designer_orders_url }
      format.json { head :ok }
    end
  end

  def add_tracking_num
    @designer_order = DesignerOrder.find(params[:id])

    # if tracking is url then store as is
    if params[:tracking].starts_with?("http://")
      # This is URL
      @designer_order.tracking = params[:tracking]
    else
    # else it is aramex tracking code
      @designer_order.tracking_num = params[:tracking]
      @designer_order.tracking = ARAMEX_TRACKING_URL + params[:tracking]
    end


    respond_to do |format|
      if @designer_order.save!
        format.js
        format.html { redirect_to @designer_order, notice: 'Tracking num was successfully created.' }
      end
    end
  end

  def add_notes
    @designer_order = DesignerOrder.find(params[:designer_order_id])
    if @designer_order.present? && params[:notes_id].present?
      @designer_order.add_notes_without_callback(params[:notes_id], current_account.try(:role).try(:name), current_account)
      LineItem.bulk_add_into_scan('DesignerOrder', @designer_order.id, 'Notes', current_account.id, params[:notes_id])
      render :json => {:notes => @designer_order.notes}
    else
      render :json => {:error => 'Designer order not found || notes blank'}
    end
  end

  def add_secondary_tracking_details
    @designer_order = DesignerOrder.find(params[:designer_order_id])
    if @designer_order.present?
      @designer_order.recent_tracking_number = params[:tracking_number]
      @designer_order.order_quality_event!(:add, 'InvalidTrackingNumber') if params[:reason] == 'Due to Inscan Failure' && params[:tracking_number].try(:downcase).try(:gsub,' ','').try(:gsub,/\t/,'').try(:gsub,/\n/,'') != @designer_order.tracking_num.try(:downcase).try(:gsub,' ','').try(:gsub,/\t/,'').try(:gsub,/\n/,'')
      @designer_order.add_notes_without_callback("Updated Tracking Number : #{params[:tracking_number]} #{params[:reason]}", 'dispatch', current_account)
      if @designer_order.save!
        render :json => {:notes => @designer_order.notes}
        if (dns = @designer_order.delivery_nps_info).present?
          dns.update_attributes(revised_delivery_date: Time.current,actual_delivery_date: Time.current)
        end
      else
        render :json => {:error => 'Error adding details'}
      end
    else
      render :json => {:error => 'Designer order not found || details blank'}  
    end
  end

  def add_tags
    @designer_order = DesignerOrder.find(params[:designer_order_id])
    @designer_order.tag_list.add(params[:tags])
    @designer_order.add_notes_without_callback("Added tag #{params[:tags]}", 'other', current_account)
    @designer_order.save!
    respond_to do |format|
      format.js
    end
  end

  def arrange_pickup
    begin
      pickup_date = Date.parse(params[:pickup_date])
    rescue ArgumentError
      redirect_to :back, notice: 'Enter A Valid Date'
      return
    end
    if OptionalPickup.where(designer_id: params[:designer_id], shipment_id: params[:shipment_id] ,pickup_date: pickup_date.beginning_of_day..pickup_date.end_of_day).present?
      redirect_to :back, notice: 'Optional Pickup already exists'
    else
      OptionalPickup.create(designer_id: params[:designer_id], shipment_id: params[:shipment_id], office_close_time: "#{params[:close_time_hr]} : #{params[:close_time_min]}", pickup_date: pickup_date)
      redirect_to :back, notice: 'Successfully Created'
    end
  end

  def check_duplicate_tracking_num
    if DesignerOrder.where{pickup >= 8.months.ago}.where('LOWER(tracking_num) = ? or LOWER(recent_tracking_number) = ?',params[:tracking].downcase,params[:tracking].downcase).count == 0
      render json: {success: 'not duplicate'}
    else
      render json: {error_text: "Tracking Number : #{params[:tracking]} Is Already Present For Other Order. Please Provide Fresh Tracking Number."}
    end
  end

  def mark_warehouse_order_dispatched
    message = 'Sorry Warehouse Order Could Not Be Dispatched'
    warehouse_order = WarehouseOrder.includes(:warehouse_line_items).find_by_id(params[:warehouse_order_id])
    if warehouse_order.present? && warehouse_order.state == 'pending'
      quantity = []
      (1..params[:box_number].to_i).each{|x| quantity << params["Box_#{x}"].to_i}
      total = quantity.sum()
      if warehouse_order.warehouse_line_items.inject(0){|sum,x| sum+=x.quantity } == total.to_i
        warehouse_order.shipper_id = Shipper.where('lower(name) = ? ',params[:shipper_name].try(:downcase)).first.try(:id)
        warehouse_order.tracking_number = params[:tracking]
        warehouse_order.box_quantity =
        {
          total_box: params[:box_number],
          quantity: quantity
        }
        warehouse_order.dispatched_by_designer!
        WarehouseOrderInvoice.new(warehouse_order).save_invoice
        message = 'Successfully Dispatched'
      else
        message = 'The Sum of quantity in each box should be equal to total quantity'
      end
    end
    render json: {success: message,total_box: params[:box_number],quantity: quantity }
  end

  def dispatch_orders_in_bulk
    notice = if params[:designer_order_id].blank? || !DesignerOrder.where(id: params[:designer_order_id]).exists?
      'No such orders found !'
      elsif params[:state] == 'dispatched' && (shipper = Shipper.find_by_id(params[:shipper_id])).blank?
        'Shipper not found !'
      elsif params[:state] == 'dispatched' && params[:tracking_number].blank? && !(is_rapid_delivery = (shipper.try(:name).try(:downcase) == 'rapid delivery')) && DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw'].exclude?(shipper.name.try(:downcase))
        'Tracking number should be present !'
      elsif params[:state] == 'invoice_uploaded' && params[:file].blank?
        'Please upload signed invoice !'
      else
        ''
      end
    redirect_to (request.env["HTTP_REFERER"] || designer_bulk_dispatch_path(@designer)), notice: notice and return if notice.present?
    if params[:state] == 'dispatched'
      invoice_number_dos = DesignerOrder.select('id,invoice_number,designer_id, warehouse_address_id').where.not(invoice_number: nil).where(id: params[:designer_order_id]).last
      invoice_number = invoice_number_dos.try(:invoice_number).presence || @designer.get_invoice_number
      DesignerOrder.where(id: params[:designer_order_id]).update_all(invoice_number: invoice_number,shipper_id: shipper.id)
      DesignerOrder.where{created_at >= 5.days.ago}.where.not(id: params[:designer_order_id]).where(state: 'pending', invoice_number: invoice_number).update_all(invoice_number: nil)
      if params[:dispatch_through_clickpost] == 'true'
        SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ClickPostAutomation", "create_forward_pickup", {}, params[:designer_order_id])
        #click_post = ClickPostAutomation.new
        #click_post.sidekiq_delay(queue: 'critical')
        #          .create_forward_pickup(params[:designer_order_id])
        notice = 'Orders will be moved to pickedup state in some time.'
      else
        tracking = if is_rapid_delivery
          wa_ids = invoice_number_dos.collect(&:warehouse_address_id).uniq
          rapid_request = @designer.get_rapid_delivery_tracking_num(nil, wa_ids)
          rapid_request[:oid] = invoice_number
          rapid_request[:weight] = LineItem.sane_items.where(designer_order_id: params[:designer_order_id]).count/10.0
          rapid_request[:amt] = DesignerOrder.where(id: params[:designer_order_id]).sum(:total)
          HTTParty.post(Mirraw::Application.config.rapid_delivery[:tracking_num_url], body:rapid_request).parsed_response
        else
          params[:tracking_number]
        end
        if DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw'].include?(shipper.name.try(:downcase))
          SidekiqDelayGenericJob.set(queue: 'critical').perform_async("Shipment", nil, "dispatch_bulk_automated_shipment", {"#{shipper.class}": shipper.id}, {"#{@designer.class}": @designer.id}, invoice_number, params[:designer_order_id])
          #Shipment.sidekiq_delay(queue: 'critical')
          #        .dispatch_bulk_automated_shipment(
          #          shipper, @designer,
          #          invoice_number,
          #          params[:designer_order_id]
          #        )
          notice = 'Orders will be moved to pickedup state in some time.'
        elsif tracking.blank? || (tracking.present? && (tracking.downcase.include?('error') || tracking.length >= 254 || Shipment.where{created_at >= 6.months.ago}.where(designer_order_id: nil, order_id: nil).where(number: tracking).count != 0))
          notice = "Invalid Tracking Number ! Please contact operations team"
        elsif tracking.present?
          response = Shipment.create_non_automated(params[:designer_order_id], params[:shipper_id], tracking, !!is_rapid_delivery, true, invoice_number)
          notice = is_rapid_delivery ? 'Orders will be moved to pickedup state in some time.' : 'Orders will be moved to dispatched state in some time.'
        end
      end
      redirect_to (request.env["HTTP_REFERER"]|| designer_bulk_dispatch_path(@designer, options_for_filter: 'dispatched')), notice: notice and return
    elsif params[:state] == 'invoice_uploaded' && (dos = DesignerOrder.find_by_id params[:designer_order_id]).present? && dos.state == 'dispatched'
      dos.build_designer_invoice(from_date: Date.today.beginning_of_month,to_date: Date.today.end_of_month,invoice_file: params[:file],designer_id: dos.designer_id)
      dos.designer_uploaded_invoice!
      # dos.sidekiq_delay(queue: 'critical')
      #    .apply_bulk_shipment_invoice('invoice_uploaded')
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(dos.class.to_s, dos.id,"apply_bulk_shipment_invoice",'invoice_uploaded')
      notice = 'Invoice will be Uploaded in some time.'
    elsif params[:state] == 'completed' && (dos = DesignerOrder.find_by_id params[:designer_order_id]).present?
      dos.mark_for_payout! if dos.can_mark_for_payout?
      if dos.can_payment_from_buyer?
        dos.payment_from_buyer
        # dos.sidekiq_delay(queue: 'critical')
        #    .apply_bulk_shipment_invoice('completed')
        SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(dos.class.to_s, dos.id,"apply_bulk_shipment_invoice",'completed')
      end
      notice = 'Orders will be moved to completed state in some time.'
    end
    redirect_to (request.env["HTTP_REFERER"]|| designer_bulk_dispatch_path(@designer)), notice: notice
  end

  def mark_pickup_done
    @designer_order = DesignerOrder.where(id: params[:designer_order_id]).preload(:order).first
    shipper = Shipper.find_by_id(params[:shipper_id])
    redirect_to (request.env["HTTP_REFERER"] || designer_designer_orders_path(@designer_order.designer)), notice: 'No such order found !' and return unless @designer_order.present?
    if !@designer_order.order.international? &&  @designer_order.line_items.any?{|li| li.inhouse_pre_stitching}
      if @designer_order.get_line_items_stitching_data.include?('Items Require Stitching')
        notice_message = 'Stitching not done'
        redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), :notice => notice_message
        return
      end
    end
    if params[:state] == 'dispatched' || params[:cod].present?
      # @designer_order.sidekiq_delay(queue: 'low')
      #                .check_track_and_add_events(
      #                  shipper.try(:name),params[:tracking], params[:cod]
      #                )
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@designer_order.class.to_s, @designer_order.id,"check_track_and_add_events",shipper.try(:name),params[:tracking], params[:cod])
      if @designer_order.invoice_number.blank?
        @designer_order.update_invoice_number
      else
        DesignerOrder.where{created_at >= 5.days.ago}.where.not(id: @designer_order.id).where(state: 'pending', invoice_number: @designer_order.invoice_number).update_all(invoice_number: nil)
      end
    end
    if (params[:state] == 'dispatched' || params[:cod].present?) && @designer_order.clickpost_serviceable.present?
      notice_message = 'Order will be moved to pickedup state.'
      if (@designer_order.shipment_status == 'pending' || (@designer_order.shipment_status == 'failed' && @designer_order.shipment.blank?) || params[:shipment_reallocation]=='true' || (@designer_order.replacement_pending? && (['created','failed','pickedup'].include? @designer_order.shipment_status)))
        @designer_order.update_columns(shipper_id: shipper.id,shipment_status: 'progress',shipment_error: nil)
        #click_post = ClickPostAutomation.new(@designer_order.order, @designer_order)
        if @designer_order.replacement_pending?
          #click_post.sidekiq_delay(queue: 'critical').create_replacement_shipment
          SidekiqDelayClassSpecificGenericJob.set(queue: 'label').perform_async("ClickPostAutomation", "create_replacement_shipment", {"#{@designer_order.order.class}": @designer_order.order.id, "#{@designer_order.class}": @designer_order.id})
        else
          SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ClickPostAutomation", "create_forward_pickup", {"#{@designer_order.order.class}": @designer_order.order.id, "#{@designer_order.class}": @designer_order.id})
          #click_post.sidekiq_delay(queue: 'critical').create_forward_pickup
        end
        notice_message = "Shipment creation in progress"
      end
      redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), notice: notice_message
      return
    end
    if params[:cod].present? && @designer_order.order.international? == false
      shipper_name = params[:shipper_id].present? ? shipper.name : 'Delhivery'
      @designer_order.create_automated_shipment(shipper_name,false,current_account)
      notice_message = 'COD Label creation in progress please wait'
      redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), :notice => notice_message
      return
    elsif (@designer_order.order.international? || @designer_order.ship_to == 'mirraw') && params[:state] == 'dispatched'
      notice_message = ''
      if params[:shipper_id].present? && shipper.present?
        is_replacement_shipment = @designer_order.replacement_pending?
        response = { error: true , error_text: 'Invalid Tracking Number. Tracking Number too long.'}
        if (params[:tracking].present? || (is_rapid_delivery = (shipper.name.try(:downcase) == 'rapid delivery')))
          tracking = is_rapid_delivery ? @designer_order.designer.get_rapid_delivery_tracking_num(@designer_order) : params[:tracking]
          if tracking.present? && tracking.downcase.exclude?('error') && tracking.length <= 254
            response = is_replacement_shipment ? Shipment.create_replacement_shipment(@designer_order.id, params[:shipper_id], tracking) : Shipment.create_non_automated(@designer_order.id, params[:shipper_id], tracking, is_rapid_delivery)
          end
        elsif (is_rapid_delivery = DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw'].include?(shipper.name.try(:downcase)))
          response = {}
          # @designer_order.sidekiq_delay(queue: 'critical')
          #                .create_automated_shipment(params[:shipper_id], true)
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(@designer_order.class.to_s, @designer_order.id,"create_automated_shipment",params[:shipper_id],true)
        end
        if response[:error]
          notice_message = "There was an error with your order please contact operations and notify of error #{response[:error_text]}"
        else
          notice_message = is_rapid_delivery && !is_replacement_shipment ? 'Order will be moved to pickedup state.' : 'Order will be moved to dispatched state'
        end
      else
        notice_message = 'Details missing for order ' + @designer_order.order.number
      end
      redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), :notice => notice_message
      return
    elsif params[:state] == 'dispatched'
      shipper_name = shipper.try(:name)
      if shipper_name.present?
        if DOMESTIC_PREPAID_COURIER_AUTOMATION['customer'].include?(shipper_name.downcase)
          notice_message = @designer_order.create_automated_shipment(shipper_name,false,current_account)
          redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), :notice => notice_message
          return
        elsif params[:tracking].present?
          if @designer_order.state == "pending" || params[:shipment_reallocation]
            @designer_order.tracking_partner = shipper_name
            @designer_order.shipper_id = params[:shipper_id]
            @designer_order.tracking_num = params[:tracking] if params[:tracking]
            @designer_order.gst_tax, @designer_order.tcs_tax, @designer_order.tds_tax = @designer_order.get_tcs_tds_and_gst_value
            @designer_order.dispatched_by_designer! if @designer_order.state == "pending"
            @designer_order.got_awb! if @designer_order.state == "pickedup"
            if @designer_order.save!
              notice_message = 'Order has been moved to dispatched state'
            end
          elsif !params[:shipment_reallocation]
            notice_message = "Cannot dispatch through this Shipper"
            @designer_order.failed_shipment(notice_message)
            @designer_order.save
          end
        else
          redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), :notice => "Please enter tracking details before marking it dispatched."
          return
        end
      end
    elsif params[:state] == 'pickedup'
      @designer_order.pickup = DateTime.current
      @designer_order.save!
      @designer_order.pickedup!
      notice_message = 'Order has been moved to pickedup state'
    elsif params[:state] == 'invoice_uploaded'
      @designer_order.build_designer_invoice(from_date: Date.today.beginning_of_month,to_date: Date.today.end_of_month,invoice_file: params[:file],designer_id: @designer_order.designer_id)
      @designer_order.skip_before_after_filter = true
      @designer_order.designer_uploaded_invoice! if @designer_order.can_designer_uploaded_invoice?
      @designer_order.mark_for_payout! if @designer_order.can_mark_for_payout? && ((@designer_order.ship_to.blank? && @designer_order.order.international?) || (@designer_order.ship_to == 'mirraw'))
      @designer_order.payment_from_buyer! if @designer_order.can_payment_from_buyer?
      notice_message = 'Invoice has been Uploaded and Order has been moved to completed state !'
    elsif params[:state] == 'completed'
      mirraw_shippable_order = (@designer_order.ship_to.blank? && @designer_order.order.international?) || (@designer_order.ship_to == 'mirraw')
      if @designer_order.can_mark_for_payout? && mirraw_shippable_order
        @designer_order.mark_for_payout!
      end
      @designer_order.payment_from_buyer! if @designer_order.can_payment_from_buyer?
      notice_message = 'Order has been moved to completed state'
    end
    redirect_to (request.env["HTTP_REFERER"]||designer_designer_orders_path(@designer_order.designer)), notice: notice_message
  end

  def change_rack
    response = {error: true, error_text: 'No Rack Code Provided'}
    if params[:rack_list_code].present? && params[:id].present?      
      designer_order = DesignerOrder.find_by_id(params[:id])
      response = designer_order.allocate_rack(params[:rack_list_code], current_account, new_rack: designer_order.rack_list)
    end
    render json: response
  end

  def create_adjustment
    adj_service_obj = DesignerOrders::CreateAdjustmentService.new(params, current_account)
    order_number, msg = adj_service_obj.create
    respond_to do |format|
      format.html{ redirect_to order_order_detail_path(order_number), notice: msg }
    end
  end

  def upload_qc_issue
    if request.get?
      qc_decrypt = decrypt_qc_data(params["token"])
      if qc_decrypt.present? && (qc_fail_data = qc_decrypt.split('.')).length == 4
        @account_id = Account.where(id: qc_fail_data[0]).first.id
        datetime = DateTime.parse(qc_fail_data[3]).advance(hours: 2)
        @order = Order.where(id: qc_fail_data[2]).first
        @designer_issue_id = DesignerIssue.where(order_id: qc_fail_data[2], design_id: qc_fail_data[1]).limit(1).pluck(:id).first
        unless datetime > DateTime.now && @order.present? && @designer_issue_id.present? && @account_id.present?
          @error = 'Require QR code scan'
        end
      else
        @error = 'Token mismatch'
      end
      render layout: false
    end
    if request.post?
      designer_issue = DesignerIssue.where(id: params["designer_issue_id"]).first
      uploaded_images = [params[:uploaded_qc_file2].present?,params[:uploaded_qc_file3].present?]
      if (params[:uploaded_qc_file1].size + params[:uploaded_qc_file2].try(:size).to_i + params[:uploaded_qc_file3].try(:size).to_i) < 6000000
        designer_issue.update_attributes(qc_issue_image1: params[:uploaded_qc_file1], qc_issue_image2: params[:uploaded_qc_file2], qc_issue_image3: params[:uploaded_qc_file3],notes: params[:notes][0])
        DesignerMailer.sidekiq_delay_until(30.minutes.from_now)
                      .send_qc_images_to_designer(
                        designer_issue.id,
                        params["order_id"],
                        params["account_id"],
                        uploaded_images
                      )
        designer_issue.line_item.add_into_scan('QC Reason', params["account_id"], params[:notes][0])
        message = 'Mail sent successfully'
      else
        message = 'Please Upload Images less than 2MB each'
      end
      redirect_to :back, notice: message
    end
  rescue => error
    @error = 'Please Re scan the QR code'
    render layout: false
  end

  def rack_code_sticker
    designer_order = DesignerOrder.preload(:order,:rack_list,line_items: :design).where(id: params[:id]).first
    if designer_order.present? && ((rack=designer_order.rack_list) || params[:fake_rack]).present?
      order = designer_order.order
      if (rack_code = params[:rack_code]).present?
        rack = RackList.where(code: rack_code).first
      end
      rack_hash = designer_order.get_rack_hash_by_design(rack.try(:code), params[:design_id])
      respond_to do |format|
        format.html
        format.pdf do
          render layout: false,
                 pdf: designer_order.id.to_s,
                 template: 'shipments/rack_code_stickers.html',
                 orientation: 'Landscape',
                 locals: {:@rack_detail_hash => rack_hash}
        end
      end
    else
      redirect_to '/404', :notice => "No designer order found"
    end
  end

  def gst_status_update
    file = "gst_release_#{Time.now.strftime('%d_%m_%Y_%H_%M_%S')}.csv"
    notice=""
    csv_data = CSV.new(open(params[:gst_status_csv].path),headers: :true, header_converters: :symbol)
    if !csv_data.first
      notice = "Invalid File"
    elsif csv_data.rewind && csv_data.first.headers.exclude?(:order_id) 
      notice = "Order_id column not found"
    else
      AwsOperations.create_aws_file(file, params[:gst_status_csv], false)
      DesignerOrder.sidekiq_delay.update_gst_status(file, current_account.email)
      notice = "GST release report is being process, you will get the status on #{current_account.email}"
    end
    redirect_to payout_management_path, notice: notice
  end

  def generate_combined_awb_unpacks
    designer_order = DesignerOrder.where('created_at > ?', INSCAN_DO_MONTHS.months.ago).where('tracking_num = ? OR recent_tracking_number = ?', params[:awb_number], params[:awb_number]).first ||  DesignerOrder.where('created_at > ?', INSCAN_DO_MONTHS.months.ago).where("tracking_num LIKE :search OR recent_tracking_number LIKE :search",search: "%#{params[:awb_number]}%").first

    if designer_order && !designer_order.other_details["combined_unpack_label"].blank?
      render json: {aws_url: designer_order.other_details["combined_unpack_label"]}
    else
      render json: {error: 'Error while processing.'}, status: :unprocessable_entity
    end
  end


  def get_encrypted_token
    current_account_id = params[:current_account_id]
    design_id = params[:design_id]
    order_id = params[:order_id]

    if (current_account_id.present? && design_id.present? && order_id.present?)
      encrypted_token = encrypt_string(current_account_id, design_id, order_id)
      render json: {encrypted_token: encrypted_token}, status:200
    else
      render json: {encrypted_token: nil}, status: 204
    end
  end

  private

  def get_state(state, gharpay, blocked)
    if state.present?
      state = state
    elsif gharpay.present? || blocked.present?
      state = 'new'
    else
      state = 'pending'
    end
  end

  def decrypt_qc_data(encrypted)
    decipher = OpenSSL::Cipher::AES.new(128, :CBC)
    decipher.decrypt
    decipher.key = Mirraw::Application.config.qc_image_key
    plain = decipher.update(Base64.urlsafe_decode64(encrypted)) + decipher.final
  end
end
