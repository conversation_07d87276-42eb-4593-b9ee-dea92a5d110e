class WmsCallbackController < ApplicationController
  include PackageManagementHelper
  before_action :authenticate_token, only: [:get_unscaled_price, :get_rack_finder_details, :create_adjustment]
  def after_qc    
    line_item_id = params[:line_item_id]
    status = params[:status]
    rate = params[:rate]
    line_items = LineItem.where(id: line_item_id)
    account_id = params[:account_id]
    prev_rack_code = params[:prev_rack_code]
    new_rack_code = params[:new_rack_code]    
    if line_items.present?
      designer_order = line_items.first.designer_order
      line_items.first.add_into_scan(line_items.first.qc_status? ? 'QC Passed' : 'QC Failed', account_id)
      qc_done_by_account = Account.find_by(id: account_id)
      after_qc_pass(line_items, status, rate, qc_done_by_account)
      if (dns = designer_order.delivery_nps_info).present? and dns.actual_delivery_date.blank?
        dns.update_attributes(actual_delivery_date: Time.current)
      end
      if prev_rack_code.present? and new_rack_code.present?
        designer_order.add_notes_without_callback("Rack changed #{prev_rack_code} to #{new_rack_code}",'rack_change')
      end
      bucket_relation = line_items.first.bucket_relations.where(entity_status:'assigned').last
      warehouse_bucket = bucket_relation.warehouse_bucket
      fake_rack = ["StitchingBucket", "PackagingBucket"].include?(warehouse_bucket.bucket_type).to_s
      line_items.first.assign_to_warehouse_bucket(warehouse_bucket.code, warehouse_bucket.bucket_type, fake_rack, "", designer_order.order, account_id, true)
    end    
    head :ok
  end

  def generate_manifest_for_tailoring
    TailoringInfo.sidekiq_delay.generate_tailor_manifest_for_django(params)
    head :ok
  end

  def get_unscaled_price
    line_item_id = params[:line_item_id]
    return render json: {error: "Please enter a line item id"}, status: :bad_request if line_item_id.blank?

    line_item = LineItem.where(id: line_item_id).first
    response, status_code = if line_item.present?
                              [{unscaled_price: line_item.unscaled_price}, :ok]
                            else
                              [{error: 'No Details Found'}, :not_found]
                            end

    render json: response, status: status_code
  end

  def get_rack_finder_details
    line_item_id = params[:line_item_id]
    return render json: {error: "Please enter a line item id"}, status: :bad_request if line_item_id.blank?

    line_item_details = {}
    line_item_details = LineItem.joins(designer_order: [:order, :rack_list]).select('line_items.id, designer_orders.id as designer_order_id, orders.id as order_id, orders.number as order_number, orders.state as order_state, rack_lists.code').where(id: line_item_id).first

    response, status_code = if line_item_details.present?
                              [line_item_details, :ok]
                            else
                              [{error: "No Details Found"}, :not_found]
                            end
    render json: response, status: status_code
  end
  
  def create_adjustment
    render json: {}, status: :unauthorized if params[:current_account_id].blank?
    current_account = Account.find(params[:current_account_id])
    adj_service_obj = DesignerOrders::CreateAdjustmentService.new(params, current_account)
    order_number, msg = adj_service_obj.create
    render json: {order_number: order_number, message: msg}, status: :ok
  end

  def authenticate_token
    token = request.headers['TOKEN']
    if token.present? && ActiveSupport::SecurityUtils.secure_compare(token, ENV['WMS_API_TOKEN'])
      true
    else
      return render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

end
