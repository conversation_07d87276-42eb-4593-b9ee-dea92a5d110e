class StoreController < ApplicationController
  DEFAULT_SORT = "designs.sell_count DESC"
  after_filter :set_no_cache, only: [:catalog2, :tags1, :search1]
  before_filter :catalog2_redirect_to_required, :catalog2_set_required_variables, :catalog2_sanitize_params, only: [:catalog2, :tags1, :search1]
  before_filter :validate_search_query, only: [:search1]
  after_filter :prepend_pid_designs, only: :catalog2
  include StoreHelper

  caches_action :catalog2, cache_path: :catalog2_cache_path, if: :catalog2_can_cache?, layout: proc {request.xhr?}, expires_in: 6.hours
  caches_action :tags1, cache_path: :catalog2_cache_path, if: :catalog2_can_cache?, layout: proc {request.xhr?}, expires_in: 6.hours
  def full_image

    app_source = APP_SOURCE[0].downcase

    @blocks = Frontpage.graded.live.country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['COUNTRY'] || session[:country][:country_code]).app_source(app_source)
    @static_collage_blocks = Frontpage.where(app_source: 'DESKTOP_COLLAGE').graded.country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['COUNTRY'] || session[:country][:country_code])
    @banner_slider = BannerSlider.mirraw.graded.live.country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['COUNTRY'] || session[:country][:country_code]).app_source(app_source)
    @bestseller_designs = Bestseller.bestseller_designs(12, false).preload(:images, :designer)
    #featured_design_ids = Feature.graded.live.select('design_id')

    #@designs = Design.where(:id => featured_design_ids)
    @designs = promise{ Design.featured_products_memcached.sample(16) }
    @history = promise{ Design.where(:id => session[:viewing_history]).includes(:images, :designer) }
    @integration_status = "new"
    @seo = SeoList.where(label: 'home_page').first
    @pagetype = "home"
    gtm_data_layer.push({pageType: 'home'})
  end

  def landing
    app_source = APP_SOURCE[0].downcase

    @landing = Landing.mirraw.where(label: params[:landing],category_landing: false).first
    @boards = nil
    if @landing.present?
      if @landing.boards.present?
        @boards = @landing.boards.graded.country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['COUNTRY'] || session[:country][:country_code]).app_source(app_source)
      end
      @blocks = @landing.blocks.mirraw.graded.country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']  || request.headers['COUNTRY'] || session[:country][:country_code]).app_source(app_source)
      @integration_status = "new"
    else
      redirect_to root_url
    end
  end

  def validate_search_query
    log4j_pattern = /\$\{.*?\}/
    invalid_characters_pattern = /[^a-zA-Z0-9\- ]/
  
    if params[:q].present? && (params[:q] =~ log4j_pattern || params[:q] =~ invalid_characters_pattern)
      Order.sidekiq_delay.notify_exceptions(
        "Suspicious search query detected on Desktop!",
        "Someone is trying to inject a malicious query: #{params[:q]}"
      )
      render 'errors/error_404', status: :not_found
    end
  end


  def catalog
    params.each do |key, value|
      params[key] = params[key].gsub(/\/\?.*/, '')
    end
    @price_low = params[:low] unless params[:low].blank?
    @price_high = params[:high] unless params[:high].blank?
    @color = params[:color].try(:downcase) unless params[:color].blank?
    @kind = params[:kind].try(:downcase) unless params[:kind].blank?
    @sortVal = params[:sort] unless params[:sort].blank?
    @sortName = params[:sortname] unless params[:sortname].blank?
    @tag = params[:tag] unless params[:tag].blank?

    @sort = DEFAULT_SORT
    if params[:sort].present?
      if params[:sort] == "top_rated"
        @sort = "designs.graded_rating DESC"
      elsif params[:sort] == "bstslr"
        @sort = "designs.sell_count DESC"
      elsif params[:sort] == "h2l"
        @sort = "designs.price DESC"
      elsif params[:sort] == "default"
        @sort = ['inr', 'rs'].include?(@symbol.downcase) ? "designs.grade DESC" : "designs.international_grade DESC"
        @rand = true
      elsif params[:sort] == "new"
        @sort = "designs.created_at DESC"
      elsif params[:sort] == "discount"
        @sort = "designs.discount_percent DESC"
      elsif params[:sort] == "l2h"
        @sort = "designs.price ASC"
      end
    end

    if @kind.present?
      @seo = SeoList.where(:label => @kind).first
      if @seo && @seo.category.present?
        @category = @seo.category
        @kind = @seo.category.name
      else
        category = Category.find_by_namei(@kind)
        @seo = SeoList.where(:category_id => category.id).first if category.present?
        @category = category
        if @seo.present?
          @category = nil if @seo.present?
          @seo = nil
        end
      end
    end

    if @category.present?
      #@designs = Design.published.retail.includes([:designer, :images, :categories]).greater_than(params[:low]).less_than(params[:high]).color(params[:color]).tag(params[:tag]).with_brand(params[:brand]).in_category(@kind).where('designers.state_machine <> ?', 'banned').order(@sort).paginate(:page => params[:page], :per_page => 40)
      page_num = params[:page].present? ? params[:page] : 1
      sort_params = params[:sort].present? ? params[:sort] : 'default'
      @old_catalog_index = @kind + '/' + page_num.to_s + '/' + sort_params + '/' + @symbol +'/'+@country_code
      @old_catalog_index = @old_catalog_index + '/' + params[:low] if params[:low].present?
      @old_catalog_index = @old_catalog_index + '/' + params[:high] if params[:high].present?
      RequestStore.cache_preload "offer_message_#{Design.country_code}", "additional_discount_#{Design.country_code}", "bmgnx_hash_#{Design.country_code}", "free_stitching_#{Design.country_code}"
      @index = Rails.cache.fetch(@old_catalog_index)
      unless @index.present?
        RequestStore.cache_preload "global_discount_on_amount_#{Design.country_code}", "global_price_percent_#{Design.country_code}", "sale_discount_country_#{Design.country_code}" , :discount_category, "category_discount_country_#{Design.country_code}", "category_discount_promotion_record_#{Design.country_code}", "category_wise_global_price_percent_#{Design.country_code}", :promotions_categories_ids
        @designs = Design.published.includes([:dynamic_price_for_current_country,:designer,:images,:variants,:categories]).greater_than(params[:low]).less_than(params[:high]).in_category(@kind).order(@sort).paginate(:page => params[:page], :per_page => 40)
      end
      if @tag.present?
        @seo = SeoList.where(:label => @tag).first
      elsif @kind.present?
        @seo = SeoList.where(:label => @kind).first unless @seo.present?
      end
    end

    respond_to do |format|
      format.html
    end
  end

  def solr_search(options,symbol)
    # Search price is only to properly get price facets [query facets]
    # Prevents search by price and thus helps in getting proper price facets
    options[:grouped_cluster] ||= 0
    boost_category_ids,boost_prop_val_ids = [],[]
    if session[:viewing_history].present? && options[:search_term].present?
      design_ids = session[:viewing_history].last(10)
      recent_designs = Design.where(id: design_ids)
      boost_category_ids = recent_designs.top_count_with_ties(:categories)
      boost_prop_val_ids = recent_designs.top_count_with_ties(:property_values)
      @personalized_search = boost_category_ids.present? || boost_prop_val_ids.present?
    end

    preload_arr = [:designer, :images, :categories, :variants]
    preload_arr.push(:collections) if current_account.try(:admin?)
    preload_arr.push(:active_promotion_pipe_lines) if Promotions.design_free_stitching_active?
    preload_arr.push(:dynamic_price_for_current_country) if DYNAMIC_PRICE_ENABLED && ['inr', 'rs'].exclude?(symbol.downcase)
    sale_factor = get_sale_factor
    pv_objects = []
    search = Design.search(:include => [*preload_arr, in_stock_siblings: preload_arr]) do
      with(:state, options[:state])
      preference_query = options[:preference]
      if options[:kind_category_id].present?
        with(:category_parents, options[:kind_category_id])
      elsif options[:tag].present?
        keywords options[:tag], :fields => :tag_list
      elsif options[:search_term].present?
        search_term_length = options[:search_term].split(' ').count(&:present?)
        search_keyword_boosting = SearchKeywordBoosting.dup
        if boost_category_ids.present? || boost_prop_val_ids.present?
          fields = [:category_name,:title,:product_id,:keywords,:imp_keywords]
          search_keyword_boosting.except!(:designer_name, :specification)
        else
          fields = [:category_name,:title,:product_id,:specification,:keywords,:imp_keywords]
        end
        fulltext options[:search_term],fields: fields do
          if options[:sort].present? && [:grade,:international_grade].exclude?(options[:sort][0])
            boost(function{scale(options[:sort][1] == :asc ? product(options[:sort][0],-1) : options[:sort][0],1,search_keyword_boosting.delete(:ordering_boost_scale) || 2)})
          elsif options[:sort].present? && [:discount_price, :scaled_discount_price].include?(options[:sort][0])
            boost(function{scale(options[:sort][1] == :asc ? product(options[:sort][0],-1) : options[:sort][0],1,search_keyword_boosting.delete(:discount_price_boost_scale) || 8)})
          else
            boost(function{scale(:sell_count,search_keyword_boosting.delete(:min_sell_count_scale) || 1,search_keyword_boosting.delete(:max_sell_count_scale) || 1.4)})
          end
          boost_prop_val_ids.each{|pv_id,_| boost(1.1){ with(:property_value_ids, pv_id) } }
          boost_category_ids.each{|cat_id,_| boost(1.1){ with(:category_ids, cat_id) } }
          if search_term_length >= 8
            minimum_match 6
          elsif search_term_length > 4
            minimum_match(search_term_length / 2 + 2)
          end
          boost_fields search_keyword_boosting
          if preference_query
            preference_query.execute(self)
          end
        end
        order_by('score','desc')
      elsif options[:collection].present?
        # Use exact phrase matching to avoid partial matches like "sarees" matching "sarees-bride"
        fulltext "\"#{options[:collection]}\"", fields: :collection_list do
          if preference_query
            preference_query.execute(self)
          end
        end
        order_by('score','desc') if preference_query
      elsif options[:catalogue].present?
        # Use exact phrase matching to avoid partial matches
        fulltext "\"#{options[:catalogue]}\"", fields: :catalogue_list do
          if preference_query
            preference_query.execute(self)
          end
        end
        order_by('score','desc') if preference_query
      elsif options[:designer_search].present?
        options[:grouped_cluster] = -1
        with(:designer_id, options[:designer_search])
      elsif options[:buy_get_free].present?
        if options[:buy_get_free] == 4
          with(:buy_get_free, 4)
        else
          with(:buy_get_free, [options[:buy_get_free], 3])
        end
      end
      text_search_query = options[:search_term].present? || options[:collection].present?
      if preference_query.present? && !text_search_query
        fulltext '*:*' do
          preference_query.execute(self)
        end
        order_by(:score, :desc)
      end
      with(:cod_shipper_ids, options[:cod_shipper_ids]) if options[:cod_shipper_ids].present?
      without(:designer_id, options[:banned_designer]) if options[:banned_designer].present? and options[:designer_search].blank?
      without(:in_catalog_one, 0)
      if ['inr', 'rs'].exclude?(symbol.downcase)
        without(:in_catalog_one).equal_to(1)
      else
        without(:in_catalog_one).equal_to(2)
      end
      without(:in_catalog_one).equal_to(3) unless options[:show_banned].present?

      facet :discount_percent, :range => 0..100, :range_interval => 10, :exclude => with(:discount_percent, options[:min_discount]..options[:max_discount]), :include => 'all'

      if options[:min_carat].present? && options[:max_carat].present?
        facet :carat, range: 0..100, range_interval: 5, include: 'all', exclude: with(:carat, options[:min_carat]..options[:max_carat])
      else
        facet :carat, range: 0..100, range_interval: 5, include: 'all'
      end
      if ((odr_90 = options[:max_odr].to_f/100) >= 0.0)
        facet :odr_90_day, include: with(:odr_90_day, 0..odr_90)
        options[:grouped_cluster] = 1
      else
        facet :odr_90_day
      end

      if options[:promoted]
        with(:featured_product, true)
      end

      if options[:designer_search].blank?
        if options[:designer_ids].present? and options[:designer_ids].count > 0
          facet :designer_id, :exclude => with(:designer_id, options[:designer_ids])
        else
          facet :designer_id
        end
      end

      if options[:created_at].present?
        with(:created_at).greater_than(options[:created_at].to_i.days.ago)
      end

      if options[:category_ids].present? and options[:category_ids].count > 0
        facet :category_ids, :exclude => with(:category_ids, options[:category_ids])
      else
        facet :category_ids
      end

      if options[:option_type_value_ids].present? and options[:option_type_value_ids].count > 0
        facet :option_type_value_ids, :exclude => with(:option_type_value_ids, options[:option_type_value_ids])
      else
        facet :option_type_value_ids
      end

      # avoide promise(gem) error
      options[:colour_property_value_ids] = options[:colour_property_value_ids].to_a
      options[:property_value_ids] = options[:property_value_ids].to_a
      property_values_with = []
      @pv_objects_frm_opts = if options[:property_value_ids].present?
        PropertyValue.select('id,property_id').where(id: options[:property_value_ids]).to_a
      else
        []
      end
      pv_objects = @pv_objects_frm_opts if @pv_objects_frm_opts.size == 1
      if options[:colour_property_value_ids].present?
        property_values_with << with(:property_value_ids, options[:colour_property_value_ids])
      end
      @pv_objects_frm_opts.reject{|pv| options[:colour_property_value_ids].include?(pv.id)}.group_by(&:property_id).each do |property_id,prop_values|
        property_values_with <<  with(:property_value_ids,prop_values.collect(&:id))
      end
      property_values_with.compact!
      if property_values_with.size > 0
        facet :property_value_ids, exclude: property_values_with, name: :property_value_ids_union
      end
      facet :property_value_ids

      with(price_field_solr, options[:min_price].to_f..options[:max_price].to_f)
      #TODO Optimize Code. hacked for now as range was not being set properly
      # facet :discount_price, :exclude => with(:discount_price, options[:min_price].to_i * sale_factor..options[:max_price].to_i * sale_factor) do
      #   row('1 - 500') do
      #     with(:discount_price, 1..500 * sale_factor/100)
      #   end
      #   row('501 - 1000') do
      #     with(:discount_price, 501*sale_factor/100..1000*sale_factor/100)
      #   end
      #   row('1001 - 2000') do
      #     with(:discount_price, 1001*sale_factor/100..2000*sale_factor/100)
      #   end
      #   row('2001 - 4000') do
      #     with(:discount_price, 2001*sale_factor/100..4000*sale_factor/100)
      #   end
      #   row('4001 - 6000') do
      #     with(:discount_price, 4001*sale_factor/100..6000*sale_factor/100)
      #   end
      #   row('Show All') do
      #     with(:discount_price, 0..MAX_PRICE_PER_PRODUCT)
      #   end
      # end

      if options[:min_rating].to_i > 0
        facet :average_rating, exclude: with(:average_rating, options[:min_rating].to_i..5)
      else
        facet :average_rating
      end

      if Design::CLUSTER_CATEGORY_WISE_IDS == 0 || (options[:kind_category_id].present? && Design::CLUSTER_CATEGORY_WISE_IDS.include?(options[:kind_category_id]))
        if options[:grouped_cluster] == 0 || [6, 7].include?(options[:kind_category_id].to_i) || options[:search_term].present?
          with(:cluster_winner, 1)
        elsif options[:grouped_cluster] == 1
          group :cluster_id_str do
            simple
            order_by(:design_cluster_score, :desc)
          end
        end
      end

      paginate :page => options[:page], :per_page => options[:items_per_page]
      if options[:sort].present?
        if ENABLE_PIPELINE_SORT
          case options[:sort][0]
          when :discount_price
            order_by(:discount_price_bucket,options[:sort][1])
            order_by(:sell_count_bucket,:desc)
            order_by(:grade_bucket,:desc)
          when :created_at
            order_by(:created_at_bucket,options[:sort][1])
            order_by(:sell_count_bucket,:desc)
            order_by(:clicks_bucket,:desc)
          end
        end
        if options[:grade_name].present?
          order_by(options[:grade_name].to_sym, options[:order_seq])
        end
        order_by(options[:sort][0], options[:sort][1])
      end
    end
    @pv_obj_frm_opt = pv_objects.try(:first)
    search
  end

  def catalog2_search_seo_params(params)
    options = Hash.new
    @integration_status = "new"
    # The three different type of variables for the same data [kind,tag,search_term,designer_search] are used
    # instance variable for haml template
    # normal variable for sunspot solr since sunspot solr search block cannot access instance variable
    # gon.variable for javascript required mainly for url construction and correction function
    if params[:kind].present?
      kind = params[:kind].downcase
      RequestStore.cache_preload "kind_to_category_and_seo_#{kind}_#{params[:facets]}", "find_by_namei_#{params[:kind]}","getids_#{params[:kind]}",'unbxd_container_active_Store Page > Bottom Horizontal Container','load_unbxd_container_Store Page > Bottom Horizontal Container'
      @breadcrumb = Breadcrumb.new(:category, category: @category, facet_name: params[:facets])
      if @category.try(:searchable?)
        options[:search_term] = @category.p_name
      else
        options[:kind_category_id] = @category.try(:id)
      end
      @kind = kind
      #@pagetype = "category"
      # gon.kind = params[:facets].present? ? request.path[0..request.path.rindex('/')] : request.path
    elsif params[:q].present? && params[:utf8].nil?
      options[:tag] = params[:q]
      @tag = options[:tag]
      # gon.tag = options[:tag]
      #@pagetype = "searchresults"
    elsif params[:q].present?
      options[:search_term] = params[:q]
      @search_term = options[:search_term]
      # gon.search_term = options[:search_term]
      #@pagetype = "searchresults"
    elsif params[:collection].present?
      options[:collection] = params[:collection]
    elsif params[:catalogue].present?
      options[:catalogue] = params[:catalogue]
    elsif params[:id].present?
      #@pagetype = "category"
      if @designer.present?
        options[:designer_search] = @designer.id
        @designer_search = params[:id]
        # gon.designer_search = params[:id]
        # gon.skip_designer_show = params[:skip_designer_show] if params[:skip_designer_show]
        @vacation_mode_on = @designer.vacation_mode_on?
      end
    end
    # Currently handling old color URL's here to maintain backward compatibility
    # with old /colour-:color URL's where faceted URL's are not being used
    # Get the ids of property_value names that are appended in the URL
    show_details = FACETED_URL_KINDS[params[:kind]].present?
    # gon.facet_category_name = FACETED_URL_KINDS[params[:kind]]['name'] if show_details
    imp_pvids_data = params[:facets].present? ? promise{PropertyValue.get_property_values_for_facets(params[:kind], params[:facets], details: show_details)} : []
    if show_details
      @facet_properties = imp_pvids_data
      imp_pvids = promise{imp_pvids_data.collect { |prop| prop[:id]}}
    else
      imp_pvids = imp_pvids_data
    end
    params[:option_type_value_ids] = StringModify.string_ascii_clean_without_space(params[:option_type_value_ids])
    params[:property_value_ids] = StringModify.string_ascii_clean_without_space(params[:property_value_ids])
    property_value_ids = promise {imp_pvids + params[:property_value_ids].to_s.split(',').map(&:to_i)}
    options[:banned_designer] = Designer.banned.pluck(:id) unless true #params['availability'] == 'all'
    options[:show_banned] = true if params['availability'] == 'all'
    options[:category_ids] = params[:category_ids].split(',').map(&:to_i) if params[:category_ids].present?
    options[:designer_ids] = params[:designer_ids].split(',').map(&:to_i) if params[:designer_ids].present?
    options[:option_type_value_ids] = params[:option_type_value_ids].split(',').map(&:to_i) if params[:option_type_value_ids].present?
    options[:colour_property_value_ids] = show_details ? [] : imp_pvids
    options[:property_value_ids] = property_value_ids
    options[:facets] = params[:facets] if params[:facets].present?
    options[:cod_shipper_ids] = Courier.cod_shipper_ids(params[:pincode].split(',')) << -1 if params[:pincode].present?
    if params[:page].to_i > 0 then options[:page] = params[:page].to_i else options[:page] = 1 end
    options[:next_page] = options[:page] + 1

    sale_factor = get_sale_factor
    options[:max_price] = params[:max_price] ? params[:max_price].to_f * sale_factor : MAX_PRICE_PER_PRODUCT
    options[:min_price] = params[:min_price] ? params[:min_price].to_f * sale_factor : 0

    options[:max_discount] = params[:max_discount] ? adjust_promo_discount(params[:max_discount].to_i) : 100
    options[:min_discount] = params[:min_discount] ? adjust_promo_discount(params[:min_discount].to_i) : 0

    if params[:min_carat] && params[:max_carat]
      options[:max_carat] = params[:max_carat].to_f
      options[:min_carat] = params[:min_carat].to_f
    end

    if params[:kind] == 'b1g1' && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && ['inr', 'rs'].exclude?(@symbol.try(:downcase))
      if bmgnx_hash[:buy_get_free].to_i == 1
        options[:max_price] = bmgnx_hash[:filter].to_i
      else
        options[:buy_get_free] = 1
      end
    elsif params[:kind] == 'direct_dollar' && ['inr', 'rs'].exclude?(@symbol.try(:downcase))
      options[:buy_get_free] = 4
    elsif params[:kind] == 'b1g1' && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && ['inr', 'rs'].include?(@symbol.try(:downcase))
      options[:buy_get_free] = 2
    end

    options[:min_rating] = params[:min_rating] if params[:min_rating].present?

    if params[:created_at].present?
      options[:created_at] = params[:created_at]
    end

    if params[:max_price] || params[:min_rating]  || params[:min_price] || params[:max_discount] || params[:min_discount] ||
       params[:designer_ids] || params[:option_type_value_ids] || params[:max_carat] || params[:min_carat]
      options[:grouped_cluster] = 1
    end
    options[:sort] = @sort
    if params['availability'].present? and (params['availability'] == 'out_of_stock')
      options[:state] = ['in_stock','sold_out','blocked','seller_out_of_stock']
    elsif params['availability'].present? and (params['availability'] == 'all')
      options[:state] = ['in_stock','sold_out','blocked','seller_out_of_stock', 'delete_by_mirraw', 'delete_by_designer', 'blocked', 'banned', 'review', 'reject']
    else
      options[:state] = ['in_stock']
    end

    designs_per_page = 48

    options[:items_per_page] = designs_per_page

    if params[:preference].present?
      options[:preference] = SolrPreferenceQuery.new(params[:preference], kind: @kind)
    end
    # Creating next page url without get page param
    # next_url_param_hash = request.path_parameters.merge(request.query_parameters)

    # # Avoid utm and google campaign parameters from being cached within next_page_url
    # # Keep all keys that you want to have in next_url - params
    # keep_params_url = [:action, :availability, :category_ids, :controller, :created_at, :designer_ids, :id, :kind, :max_discount, :max_price, :min_discount, :min_price, :option_type_value_ids, :page, :property_value_ids, :q, :sort, :tag, :utf8]

    # next_url_param_hash.each do |key ,value|
    #   if keep_params_url.include?(key.to_sym)
    #     # Do nothing
    #   else
    #     next_url_param_hash.delete(key)
    #   end
    # end

    # # next_url_param_hash ':page symbol' key doesn't exist -- needs to be changed
    # next_url_param_hash.delete(:page) if params[:page].present?
    # next_url_param_hash['page'] = options[:next_page].to_s
    # next_url_param_hash['paginate'] = true
    # if params['get_page'] == 'all' then next_url_param_hash.delete('get_page') end
    # @next_page_url = url_for(next_url_param_hash)

    # @next_url_param_hash = next_url_param_hash
    # @query_params = request.query_parameters

    # gon.conversion_rate = @conversion_rate
    # options[:default] = false
    # options[:search_status] = true

    #designer_odr_filter
    options[:max_odr] = params[:max_odr] || -1
    options[:promoted] = params.keys.include?('promoted')
    # gon.max_odr = options[:max_odr] if options[:max_odr].to_f > 0

    # Checking condition to test whether the query is default
    # if !(current_account.present? and current_account.admin?) && options[:designer_ids].blank? && options[:category_ids].blank? &&
    #    options[:search_term].blank? && options[:state] == ['in_stock'] && options[:created_at].blank? &&
    #    options[:next_page] <= 31 && options[:items_per_page] == designs_per_page && params[:property_value_ids].blank? &&
    #    options[:option_type_value_ids].blank?
    #     options[:default] = true
    #   if options[:kind_category_id].present?
    #     options[:index] = 'solr/kind_category_id/'+options[:kind_category_id].to_s
    #   elsif options[:tag].present?
    #     options[:index] = 'solr/tag/'+options[:tag]
    #   elsif options[:designer_search].present?
    #     options[:index] = 'solr/designer/'+options[:designer_search].to_s
    #   end
    #   if options[:buy_get_free].present?
    #     options[:index] = 'solr/' unless options[:index].present?
    #     options[:index] += "/buy_get_free/#{options[:buy_get_free]}"
    #   end
    #   if options[:index].present? && @symbol.present? &&  @country_code.present?
    #     options[:index] += "/price/" + options[:min_price].to_s + "/" + options[:max_price].to_s + "/discount/" + options[:min_discount].to_s + "/" + options[:max_discount].to_s
    #     options[:index] += '/page/' + options[:page].to_s + '/sort/' + options[:sort][2] + '/' + @symbol + '/' + @country_code
    #     options[:index] += "/sort-blank" if params[:sort].blank? && FACETED_URL_KINDS[params[:kind]]
    #   end

    #   if params[:fexp].present?
    #     options[:index] += '/fexp'
    #   end

    #   if options[:index].present? && options[:min_carat].present? && options[:max_carat].present?
    #     options[:index] += "/carat/#{options[:min_carat]}/#{options[:max_carat]}"
    #   end
    #   if options[:index].present? && options[:min_rating].present?
    #     options[:index] += "/rating/#{options[:min_rating]}"
    #   end
    #   # if options[:index].present?
    #   #   options[:index] += "/utm_c/" + params[:utm_campaign].gsub(/-/,'') if params[:utm_campaign].present?
    #   # end
    #   if options[:index].present? && options[:show_banned].present?
    #     options[:index] += "/show_banned/"
    #   end
    #   if options[:facets].present?
    #     options[:index] += "/facets/#{options[:facets]}"
    #   end
    #   if options[:cod_shipper_ids].present?
    #     options[:index] = 'solr/' unless options[:index].present?
    #     options[:index] += "/cod_shipper_ids/#{options[:cod_shipper_ids]}"
    #   end

    #   if options[:max_odr].present?
    #     options[:index] += "/odr/#{options[:max_odr]}"
    #   end

    #   if options[:index].present?
    #     @index = Rails.cache.fetch(options[:index] + "/#{@country_code}")
    #     options[:search_status] = false if @index.present?
    #   end
    # end

    ## Need to cache it
    # gon.property = Property.select("LOWER(regexp_replace(properties.name,'[\W\s.?]','_', 'g')) as name").collect(&:name)
    # gon.option_types = OptionType.select("LOWER(regexp_replace(option_types.name,'[\W\s.?]','_', 'g')) as name").collect(&:name)
    if options[:kind_category_id].present?
      options[:grade_name] = GradingTag.get_grading_tags_for_grade_name(options[:kind_category_id], 'Category', @country_code)
    elsif options[:collection].present?
      collection = ActsAsTaggableOn::Tag.find_by('LOWER(name) = ?', options[:collection].downcase)
      options[:grade_name] = GradingTag.get_grading_tags_for_grade_name(collection.id, 'Collection', @country_code) if collection
    elsif options[:catalogue].present?
      catalogue = ActsAsTaggableOn::Tag.find_by('LOWER(name) = ?', options[:catalogue].downcase)
      options[:grade_name] = GradingTag.get_grading_tags_for_grade_name(catalogue.id, 'Catalogue', @country_code) if catalogue
    end

    options[:order_seq] = 'desc'
    options
  end

  def catalog2_category_designer_facets_organize(search,options)
    # Pushing selected and unselected designers and categories into seperate arrays
    # Creating new hashes consisting of id, proper_styled_name* and facet count
    categories_facets_count = Hash.new
    search.facet(:category_ids).rows.each do |cat|
      categories_facets_count[cat.value] = cat.count
    end
    if categories_facets_count.present?
      categories = Category.where(:id => categories_facets_count.keys)
      @categories_list = [[],[]]
      @super_categories_list = []
      categories.each do |cat|
        cat_item = {:id => cat.id, :count => categories_facets_count[cat.id], :name => cat.name.titleize, original_name: cat.name}
        if !params[:facets].present? && cat.super_child? && cat.parent_id == @category.try(:id).to_i
          cat_item.merge!(label: cat.super_child_label, photo: cat.photo(:thumbnail))
        end
        if options[:category_ids].present? and options[:category_ids].index(cat.id)
          @categories_list[1] << cat_item unless cat_item[:label].present?
          @super_categories_list << cat_item.merge!(checked: true) if cat_item[:label].present?
        else
          @categories_list[0] << cat_item unless cat_item[:label].present?
          @super_categories_list << cat_item if cat_item[:label].present?
        end
      end
    end

    if options[:designer_search].blank?
      designers_facets_count = Hash.new
      search.facet(:designer_id).rows.each do |designer|
        designers_facets_count[designer.value] = designer.count
      end

      if designers_facets_count.present?
        designers = Designer.select('id, name').where(:id => designers_facets_count.keys).where(:state_machine => ['review', 'approved'])
        @designers_list = [[],[]]
        designers.each do |designer|
          des_list = {:id => designer.id, :count => designers_facets_count[designer.id],:name => designer.name.to_s.titleize}
          if options[:designer_ids].present? and options[:designer_ids].index(designer.id)
            @designers_list[1] << des_list
          else
            @designers_list[0] << des_list
          end
        end
      end
    end
    @pv_list = PropertyValue.create_pv_facets_for_catalog(search, options, @pv_objects_frm_opts)
    optv_ids = []
    optv_facets_count = {}
    search.facet(:option_type_value_ids).rows.each do |optv|
      optv_facets_count[optv.value] = optv.count
      optv_ids << optv.value
    end

    @optv_list = {}
    if optv_ids.present? && @category.present?
      s_optv = "option_type_values.*, option_types.p_name as option_type_p_name, LOWER(regexp_replace(option_types.name,'[\W\s.?]','_', 'g')) as option_type_name, option_types.id as option_type_id"
      category_option = OptionType.where("category_id = #{@category.id}").to_sql
      OptionTypeValue.select(s_optv).joins("JOIN (#{category_option}) option_types ON option_type_values.option_type_id = option_types.id").where(:id => optv_ids).order(:position).group_by{|optv| optv[:option_type_id]}.each do |id, option_type_values|
        opt_name = option_type_values.first[:option_type_name]
        @optv_list[opt_name] = [[],[]]
        option_type_values.each do |optv|
          option_type_value_item = {:id => optv.id, :count => optv_facets_count[optv.id], :name => optv.p_name, :option_type_p_name => optv[:option_type_p_name]}
          if options[:option_type_value_ids].present? && options[:option_type_value_ids].index(optv.id)
            @optv_list[opt_name][1] << option_type_value_item
          else
            @optv_list[opt_name][0] << option_type_value_item
          end
        end
      end
    end
  end

  def catalog2_redirect_to_required
    # preventing logged in designer from viewing his designer page in facet view
    # also preventing admin from viewing any designer page in facet view
    if params[:id].present?
      begin
        @designer = Designer.find(params[:id])
      rescue
        redirect_to root_url
        return
      end
    end
    if !params[:skip_designer_show] && @designer.present? && current_account.present?
      if current_account.remote_vendor? && @designer.owner_id != current_account.accountable_id
        return redirect_to root_url
      elsif (@designer.try(:account).try(:id) == current_account.try(:id) || current_account.admin?)
        designers_show(@designer)
      end
    elsif params[:sell_at].present?
      best_seller
    end
  end

  def prepend_pid_designs
    if !request.xhr? && @pids.present?
      designs_cache_key = !current_account.try(:admin?) && SHARED_PARTIAL_CACHING && proc do |item|
        key = "#{@country_code}_#{@symbol}_#{item.cache_key}"
        @long_div.present? ? "#{key}_long_div" : key
      end
      in_catalog_one_value = ['inr', 'rs'].include?(@symbol.downcase) ? 1 : 2
      winners = DesignCluster.where(design_id: @pids).uniq.pluck(:winner_design_id,:design_id)
      @pids = (@pids.map(&:to_i)  - winners.collect(&:last) + winners.collect(&:first)).uniq
      adverb_designs = Design.where(id: @pids, state: 'in_stock').where('in_catalog_one is null or in_catalog_one = ?', in_catalog_one_value).preload([:dynamic_price_for_current_country,:designer,:images,:variants,:categories])
      adverb_designs_to_add= adverb_designs.present? ? render_to_string(:partial => 'shared/design', :collection => adverb_designs, :as => :design, cache: designs_cache_key) : ''
      if adverb_designs_to_add.present?
        old_response = response.body
        response.body = old_response.sub!(/<.*?ul.*?class.*?listings.*?>/, "\\0\n#{adverb_designs_to_add}\n") || old_response
      end
    end
  end

  def catalog2
    # collect search params, seo_params, create next page url and also set gon vars
    @ga_list = "CategoryPage"
    @options = catalog2_search_seo_params(params)
    @index = 0
    if @designer.present?
      @ga_designer_id = @designer.id
      @coupons = @designer.coupons.live.advertise
      @designer_collections = [] #designer.designer_collections.by_position.limit(4).to_a
    end
    # # Get search results from solr
    # if DISABLE_SOLR
    #   if @designer.present?
    #     @designs = @designer.designs.published.preload(:designer, :images, :categories, :variants, :dynamic_price_for_current_country).paginate(page: params[:page])
    #   elsif params[:kind].present?
    #     category_ids = Category.getids(params[:kind])
    #     @designs = Design.published.preload(:designer, :images, :categories, :variants, :dynamic_price_for_current_country).joins(:categories_designs).where(categories_designs: {category_id: category_ids}).paginate(page: params[:page])
    #   else
    #     @designs=[]
    #   end
    # if (@options[:kind_category_id].present? or @options[:tag].present? or @options[:search_term].present? or @options[:designer_search].present? or @options[:buy_get_free].present?)
    @faqs =
      if params[:facets].present?
        page_not_found and return if @options[:property_value_ids].empty?
        # NOTE: The `dup` is necessary because otherwise ActiveRecord behaves
        # weirdly. It forms the query like `= NULL`, which never forms in
        # ActiveRecord (at least not that I saw anywhere). Instead the query
        # formed should be an `IN` query. It only happens in this particular
        # scenario because everything is working properly in the Rails console.
        #
        FAQ.joins(:property_values).where({
          'property_values.id': @options[:property_value_ids].dup
        })
      else
        FAQ.joins(:categories).where({
          'categories.id': @options[:kind_category_id]
        })
      end.order(updated_at: :desc).limit(5).map do |faq|
        faq.attributes.slice('id', 'question', 'answer')
      end

    RequestStore.cache_preload "discount_promotion_#{Design.country_code}", "sale_discount_percent_#{Design.country_code}", "global_discount_on_amount_#{Design.country_code}", "global_price_percent_#{Design.country_code}", "sale_discount_country_#{Design.country_code}", :discount_category, :promotions_categories_ids, "category_discount_country_#{Design.country_code}", "category_wise_global_price_percent_#{Design.country_code}"
    @search = solr_search(@options,@symbol)
    #show banner on category pages
    @banner_category = promise {CategoryBanner.category_banner_for(@country_code, @category)}
    @new_arrivals = promise {CategoryNewArrival.category_new_arrival_for(@category, @country_code)} if @options[:kind_category_id].present?
    @popular_links = @pv_obj_frm_opt.present? ? @pv_obj_frm_opt.popular_links : (@category.present? ? @category.popular_links : [])
    # Get Records for search from db
    @designs = promise{ @search.results }
    # @designs = @designs.sort_by(&:return_count).reverse! if params[:return].present? and params[:return] == 'y'
    # To fetch db records of category and designers based on facets returned from search and set flag for list js relating to designer and category
    catalog2_category_designer_facets_organize(@search,@options)
    #end
    # if DISABLE_SOLR
    #   render  'catalog'
    #   return
    # end
    @breadcrumb = Breadcrumb.new(:designer, designer: @designer) if @designer.present?
    respond_to do |format|
      format.html {render :template => '/store/catalog2'}
      format.js {render :partial => '/store/catalog2'}
    end
  end


  def search1
    @ga_list = "Search"
    @fb_search = "Search_Null"
    @fb_search = params[:q] if params[:q].present?
    catalog2()
  end

  def tags1
    @ga_list = "Tags"
    if params[:q].present? && ['jhumkas','toe rings','skirts','hijab'].include?(params[:q].downcase)
      redirect_to store_search_path(kind: params[:q].downcase.gsub(' ','-'))
    elsif params[:q].present? && ['crop top and lehenga'].include?(params[:q].downcase)
      redirect_to store_search_path(kind: "crop-top-lehengas")
    elsif params[:q].present? && ['ethnic wear'].include?(params[:q].downcase)
      redirect_to store_search_path(kind: "women-ethnic-wear")
    elsif params[:q].present? && ['oxidised jewellery','oxidised jewelry','oxidized jewellery','ethnic oxidized jewelry'].include?(params[:q].downcase)
      redirect_to store_search_path(kind: 'oxidised-jewellery')
    elsif params[:q].present? && ['bridal', 'temple', 'statement', 'thewa', 'handmade', 'filigree', 'pearl', 'high end', 'ethnic', 'kundan', 'indian traditional', 'antique', 'american diamond', 'maharashtrian traditional'].map { |s| "#{s} jewellery" }.include?(params[:q].downcase)
      redirect_to store_search_path(kind: params[:q].parameterize)
    else
      catalog2()
    end
  end

  def set_no_cache
    # Prevent browsers from caching ajax requests, browser give cache response on back button.
    if request.xhr?
      response.headers["Cache-Control"] = "no-cache, no-store, max-age=0, must-revalidate"
      response.headers["Pragma"] = "no-cache"
      response.headers["Expires"] = "Fri, 01 Jan 1990 00:00:00 GMT"
    end
  end

  def product_widgets
    if request.xhr?
      @designs = Design.featured_products_memcached.sample(16)
    else
      redirect_to root_url, notice: 'You have entered wrong path'
    end
  end

  def collection
    @integration_status = "new"
    @dynamic_pricing = true
    if params[:collection].present?
      @page_num = params[:page].present? ? correct_page_param(params[:page]).to_s : "1"
      if current_account.present? && current_account.admin?
        # Do Nothing
        @designs =
          if ['inr', 'rs'].include?(@symbol.downcase)
            Design.published.desc_graded.not_banned_in_catalog(:domestic).includes([:designer,:images,:variants,:categories]).tagged_with(params[:collection]).paginate :page => @page_num
          else
            Design.published.int_desc_graded.not_banned_in_catalog(:international).includes([:dynamic_price_for_current_country,:designer,:images,:variants,:categories]).tagged_with(params[:collection]).paginate :page => @page_num
          end
      else
        @old_catalog_index = "collection" + '/' + params[:collection] + '/' + @page_num + '/' + @symbol + '/' + @country_code
        RequestStore.cache_preload "offer_message_#{Design.country_code}", "additional_discount_#{Design.country_code}", "bmgnx_hash_#{Design.country_code}", "free_stitching_#{Design.country_code}"
        @index = Rails.cache.fetch(@old_catalog_index)
        unless @index.present?
          RequestStore.cache_preload "global_discount_on_amount_#{Design.country_code}", "global_price_percent_#{Design.country_code}", "sale_discount_country_#{Design.country_code}" , :discount_category, "category_discount_country_#{Design.country_code}", "category_discount_promotion_record_#{Design.country_code}", "category_wise_global_price_percent_#{Design.country_code}", :promotions_categories_ids
          @designs =
            if ['inr', 'rs'].include?(@symbol.downcase)
              Design.published.desc_graded.not_banned_in_catalog(:domestic).includes([:designer,:images,:variants,:categories]).tagged_with(params[:collection]).paginate :page => @page_num
            else
              Design.published.int_desc_graded.not_banned_in_catalog(:international).includes([:dynamic_price_for_current_country,:designer,:images,:variants,:categories]).tagged_with(params[:collection]).paginate :page => @page_num
            end
        end
      end
      @active_promotions = PromotionPipeLine.active_promotions
      @seo = SeoList.where(label: params[:collection]+'_collection').first
      @collection = Collection.where(:title => params[:collection]).first
      @tag = params[:collection]
    end
    render 'catalog'
  end

  def online
    @integration_status = "new"
    if params[:online].present?
      @page_num = params[:page].present? ? correct_page_param(params[:page]).to_s : "1"
      if current_account.present? && current_account.admin?
        # Do Nothing
        @designs =
          if ['inr', 'rs'].include?(@symbol.downcase)
            Design.published.desc_graded.not_banned_in_catalog(:domestic).includes([:designer,:images,:variants,:categories, :active_promotion_pipe_lines]).tagged_with(params[:online]).paginate :page => @page_num
          else
            Design.published.int_desc_graded.not_banned_in_catalog(:international).includes([:dynamic_price_for_current_country,:designer,:images,:variants,:categories, :active_promotion_pipe_lines]).tagged_with(params[:online]).paginate :page => @page_num
          end
      else
        @old_catalog_index = "online" + '/' + params[:online] + '/' + @page_num + '/' + @symbol + '/' + @country_code
        RequestStore.cache_preload "offer_message_#{Design.country_code}", "additional_discount_#{Design.country_code}", "bmgnx_hash_#{Design.country_code}", "free_stitching_#{Design.country_code}"
        @index = Rails.cache.fetch(@old_catalog_index)
        unless @index.present?
          RequestStore.cache_preload "global_discount_on_amount_#{Design.country_code}", "global_price_percent_#{Design.country_code}", "sale_discount_country_#{Design.country_code}" , :discount_category, "category_discount_country_#{Design.country_code}", "category_discount_promotion_record_#{Design.country_code}", "category_wise_global_price_percent_#{Design.country_code}", :promotions_categories_ids
          @designs =
            if ['inr', 'rs'].include?(@symbol.downcase)
              Design.published.desc_graded.not_banned_in_catalog(:domestic).includes([:designer,:images,:variants,:categories]).tagged_with(params[:online]).paginate :page => @page_num
            else
              Design.published.int_desc_graded.not_banned_in_catalog(:international).includes([:dynamic_price_for_current_country,:designer,:images,:variants,:categories]).tagged_with(params[:online]).paginate :page => @page_num
            end
        end
      end
      @active_promotions = PromotionPipeLine.active_promotions
      # @seo = SeoList.where(:label => params[:collection]).first
      @collection = Collection.where(:title => params[:online]).first
      @tag = params[:online]
      gtm_data_layer.push({pageType: 'online', collectionName: params[:online].downcase})
      gon.ga_product_list = "online / #{params[:online]}"
    end
    render 'catalog'
  end

  def new_collection
    # Sarees, Earrings, Necklace Sets, Salwar Kameez
    @new_sarees = Design.published.retail.includes([:designer, :images, :categories]).in_category("sarees").order('designs.created_at DESC').limit(8)
    @new_necklace = Design.published.retail.includes([:designer, :images, :categories]).in_category("necklace-sets").order('designs.created_at DESC').limit(8)
    @new_earrings = Design.published.retail.includes([:designer, :images, :categories]).in_category("earrings").order('designs.created_at DESC').limit(8)
    @new_salwar = Design.published.retail.includes([:designer, :images, :categories]).in_category("salwar-kameez").order('designs.created_at DESC').limit(8)
  end

  def search_old
     @search_param = params[:q]
     banned_designer = Designer.banned.collect(&:id)
     @search = Design.search do
       fulltext @search_param
       with(:state, 'in_stock')
       without(:designer_id, banned_designer) if banned_designer.present?
       paginate :page => params[:page], :per_page => 32
       order_by(:created_at, :desc)
     end
     @designs = @search.results
     render 'catalog'
   end

   def search
     @search_param = params[:q]
     @designs = Design.published.retail.includes([:designer, :images, :categories]).where('designs.title ILIKE (?)', "%" + @search_param + "%").active_designer_designs.paginate(:page => params[:page], :per_page => 40)
     render 'catalog'
   end

   def tag
       redirect_to "/women/jewellery/earrings/jhumkas" if params[:q] == 'jhumkas' || params[:q] == "Jhumkas"
       @sort = DEFAULT_SORT
       @tag = params[:q]
       @seo = SeoList.find(:first, :conditions => [ "lower(label) = ?", @tag.downcase ]) if @tag
       @designs = Design.published.retail.tagged_with(@tag).order(@sort).paginate :page => params[:page]
       render 'catalog'
    end

  def best_seller
    @integration_status = "new"
    if params[:kind].present?
      seo_attributes
      catalog = @country_code == 'IN' ? :domestic : :international
      d_order_states = ['pickedup', 'pending', 'dispatched', 'completed']
      li_created_between = Time.zone.now.beginning_of_day().advance(days: - params['sell_at'].to_i)..Time.zone.now.end_of_day()
      category_ids = params[:child] == 'true' ? @category.id : Category.getids(@category.name)
      @designs = Design.preload(:categories,:images,:designer,:variants).not_banned_in_catalog(catalog).joins(:categories_designs, line_items: :designer_order).select("designs.*, sum(line_items.quantity) as sell_count").where(categories_designs: {category_id: category_ids} ,line_items: {designer_order: {state: d_order_states}, created_at: li_created_between}).group('designs.id').order('sum(line_items.quantity) DESC').paginate(page: params[:page], per_page: 32,total_entries: 800)

      @tag = params[:kind]
    end
    render 'catalog'
  end

  # GET /best-sellers
  def best_sellers
    @integration_status = "new"
    @designs = Bestseller.bestseller_designs(nil, randomize: false).paginate(page: params[:page])

    render 'catalog'
  end

  private

  def seo_attributes
    kind = params[:kind].downcase
    @seo = SeoList.where(label: kind).includes(:category).first
    if @seo.present? && (@category = @seo.category).present?
      # Do nothing
    elsif (@category = Category.find_by_namei(kind)).present?
      @seo = SeoList.where(category_id: @category.id).first
    end
    @kind = kind
  end

  def correct_page_param(page)
    page = page.to_i
    page = 1 if page == 0
    page
  end

  def start_experiment(type, params)
    if (result = @tester.set(type, params))
      session[:experiment] = @tester.set_session
    else
      @tester.remove_current_experiment(type)
    end
    result
  end

  def page_not_found
    respond_to do |format|
      format.html {render 'errors/error_404', status: 404}
      format.js {render nothing: true, status: 404}
     end
    #raise ActionController::RoutingError.new('Not Found')
  end
end
