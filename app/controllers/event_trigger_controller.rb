class EventTriggerController < ApplicationController
  before_filter :authenticate_account!
  skip_before_filter :verify_authenticity_token

  def trigger_event_for_order
    @order = Order.all_rel.find_by_id(params[:order_id])
    @events = @order.events
    notice = ''
    prev_state = @order.state
    begin
      case params[:state].try(:to_sym)
      when :sane
        if params[:form_transaction_id].present? 
          transaction_details = params[:form_transaction_id].split('::')
          attribute = transaction_details[0].to_sym
          unless @order[attribute].present?
            @order[attribute] = transaction_details[1]
            note_content = "Transaction Id : #{params[:form_transaction_id]}"
          end
          @order.duplicated_from_id = params[:source_order_id] if params[:source_order_id].to_i > 0
        end
        if ['accounts_admin', 'accounts'].include?(current_account.role.try(:name)) || (@order.cod? && ACCESSIBLE_EMAIL_ID['cod_sane'].to_a.include?(current_account.email))
          @order.good_data! if @order.new? || @order.confirmed? || @order.fraud? || @order.followup?
          @order.buyer_confirmation! if @order.pending?
          @order.reactivate! if @order.cancel?
        end
      when :complete
        @order.archive_data!
      when :pending
        @order.non_cod_area! if @order.new?
        @order.move_to_pending! if @order.cancel?
      when :cancel
        @order.order_cancel_reason = [params[:reason].to_s, current_account] if @order.can_cancel?
        @order.cancel!
        @order.restore_combo_variant_quantity
      when :confirmed
        @order.confirmed_order!
      when :reject
        @order.buyer_dissatisfied!
      when :fraud
        @order.fraud_alert!
      when :cancel_complete
        @order.cancel_followed_up
      when :dispatched
        if (params[:tracking_number].present? && params[:courier_company].present?) || (@order.tracking_number.present? && @order.courier_company.present?)
          @order.tracking_number = params[:tracking_number] if params[:tracking_number].present?
          @order.courier_company = params[:courier_company] if params[:courier_company].present?
          # if Shipment.sidekiq_delay
          #            .generate_manual_shipment(
          #              @order.id,
          #              @order.tracking_number,
          #              @order.courier_company,
          #              current_account
          #            )
          if SidekiqDelayGenericJob.perform_async("Shipment", 
                                                nil, 
                                                "generate_manual_shipment",
                                                @order.id,
                                                @order.tracking_number,
                                                @order.courier_company,
                                                {current_account.class.to_s => current_account.id}
                                              )
            @order.package_shipped!
            @order.save
          end
        end
      when :partial_dispatch
        @order.partial_package_shipped!
      when :pickedup
        @order.package_pickedup!
      when :ready_for_dispatch
        @order.items_ready_dispatch!
      when :followup
        @order.move_to_followup!
      end
    if current_account.present? && @order.state != prev_state
      if params[:state] == 'sane'
        new_notes = "[#{current_account.email.split('@')[0]}] #{note_content}"
        @order.update_column(:notes,"#{@order[:notes]} #{new_notes}")
        @order.add_notes_without_callback("#{params[:state]}", 'state_change', current_account)
        @order.notes = new_notes
      else
        @order.add_notes_without_callback("#{params[:state]}", 'state_change', current_account)
      end
    end
    if ['accounts_admin', 'accounts'].include?(current_account.role.try(:name)) && (['ready_for_dispatch','dispatched','pickedup'].include?(prev_state)) && (params[:state] == 'sane')
      @order.update_column(:state, 'sane')
      @order.add_notes_without_callback("#{params[:state]}", 'state_change', current_account)
    end
    rescue StateMachines::InvalidTransition => e
      notice = e.message
    end

    respond_to do |format|
      format.js
      format.html { redirect_to :back, :notice => notice }
    end

  end

  def trigger_event_for_designer_order
    if params[:state] != 'canceled'
      designer_order = DesignerOrder.find_by_id(params[:designer_order_id])
    else
      designer_order = DesignerOrder.includes(:line_items => [:design =>[:variants, :slugs]]).find_by_id(params[:designer_order_id])
    end

    notice = 'State for was changed'

    @order = Order.all_rel.find_by_id(designer_order.order_id)
    @events = @order.events
    if current_account.present? && designer_order.state != params[:state] && params[:state] != 'canceled'
      designer_order.add_notes_without_callback("#{params[:state]}",'state_change',current_account)
    end
    begin
      case params[:state].to_sym
      when :pending
        designer_order.delayed_by_designer! if designer_order.state == "scheduled"
        designer_order.order_looks_sane! if designer_order.state == "new" && !@order.new?
        designer_order.reactivate! if designer_order.state == "canceled" && !@order.new?
      when :scheduled
        designer_order.confirmation_from_designer! unless @order.new?
      when :pickup_done
        designer_order.pickup_by_shipping_guys!
      when :dispatched
        if (designer_order.gst_tax.to_i <= 0 || designer_order.tcs_tax.to_i <= 0 || designer_order.tds_tax.to_i <= 0 ) && designer_order.ship_to != 'mirraw'
          designer_order.gst_tax, designer_order.tcs_tax, designer_order.tds_tax = designer_order.get_tcs_tds_and_gst_value
        end
        designer_order.got_awb! if (got_awb_states = ["pickup_done","pickedup","critical"].include?(designer_order.state))
        if designer_order.state == "pending"
          designer_order.dispatched_by_designer!
          designer_order.update_invoice_number if designer_order.invoice_number.blank?
        end
        designer_order.replacement_dispatched! if designer_order.state == 'replacement_pending'
        designer_order.again_dispatch_for_rto_return! if ['buyer_returned','rto'].include?(designer_order.state)
      when :completed
        if designer_order.state == "dispatched"
          designer_order.mark_for_payout! if designer_order.can_mark_for_payout? && (@order.international? || designer_order.ship_to == 'mirraw')
          designer_order.payment_from_buyer! if designer_order.can_payment_from_buyer?
        else
          designer_order.missed_dispatch_event! if designer_order.state == "scheduled"
          designer_order.critical_to_completed! if designer_order.state == "critical"
          designer_order.pickupdone_to_completed! if designer_order.state == "pickup_done"
          designer_order.replacement_completed! if designer_order.state == 'replacement_pending'
        end
      when :vendor_canceled
        designer_order.vendor_cancel!
        designer_order.restore_combo_variant_quantity
      when :buyer_returned
        designer_order.buyer_dissatisfied! if ['new','pending'].exclude?(@order.state)
      when :canceled
        designer_order.cancel_reason = params[:reason]
        designer_order.cancel!
        designer_order.add_notes_without_callback("#{params[:state]}:#{params[:designer_order_id]}",'state_change',current_account)
        designer_order.restore_combo_variant_quantity
      when :rto
        designer_order.didnt_reach_buyer! unless designer_order.pickedup?
      when :critical
        designer_order.fuckup!
        DesignerMailer.sidekiq_delay
                      .designer_order_critical_mail_to_vendor(
                        designer_order.designer_id, @order.number
                      )
        if @order.country.downcase == 'india' && (@order.order_notification.blank? || (@order.order_notification.present? && @order.order_notification[':critical'].blank?))
          @order.order_notification.store(:critical,1)
          OrderMailer
            .sidekiq_delay
            .send_issue_qcfail_mail_to_user(@order.id,'faced some issue')
          @order.send_confirmation_sms('faced some issue')
          @order.save!
        end
      when :pickedup
        designer_order.pickedup!
      when :replacement_pending
        designer_order.replacement_required! if designer_order.can_replacement_required?
      end if !current_account.try(:role?, :outsourced_support)
      notice = 'Not Authorized' if current_account.try(:role?, :outsourced_support)
    rescue StateMachines::InvalidTransition => e
      notice = e.message
    end

    respond_to do |format|
      format.js
      format.html {redirect_to(order_order_detail_path(designer_order.order), :notice => notice)}
    end
  end

  def trigger_event_for_return_designer_order
    rdo = ReturnDesignerOrder.find_by_id(params[:return_designer_order_id])
    notice = "State updated"
    @user = current_account.user if current_account && current_account.user?
    redirect_to :back, notice: 'State cannot be updated anymore.' and return if rdo.return.payment_complete?
    case params[:state].to_sym
    when :buyer_dispatched
      if params[:tracking_company].present? && params[:tracking_number].present?
        if rdo.buyer_dispatched?
          ReturnDesignerOrder.where(id: rdo.id).update_all(tracking_company: params[:tracking_company],tracking_number: params[:tracking_number],updated_at:Time.zone.now)
          return1 = rdo.return
          return1.update_column(:state, 'refund_details_pending')
          return1.update_return_tracking_info() if return1.check_if_all_rdo_have_tracking
        elsif rdo.can_product_sent?
          rdo.add_tracking_details(params[:tracking_company],params[:tracking_number])
          rdo.product_sent!
        end
      else
        notice = 'Please check state and tracking details'
      end
    when :vendor_received
      if rdo.can_product_received?
        rdo.product_received!
        rdo.return.save!
      else
        notice = 'Please check state'
      end
    when :completed
      if rdo.can_return_completed?
        rdo.return_completed!
      else
        notice = 'Please check state'
      end
    when :canceled
      if rdo.can_products_kept?
        rdo.products_kept!
        rdo.return.order.add_notes_without_callback("Refund Canceled", 'return' ,current_account)
        rdo.return.save!
      else
        notice = 'Please check state'
      end
    when :buyer_canceled
      if rdo.can_product_canceled?
        rdo.add_return_type params[:return_type]
        rdo.product_canceled!
        rdo.return.save!
      else
        notice = 'Please check state'
      end
    when :goodwill_adjusted
      if rdo.can_product_goodwill?
        rdo.add_return_type params[:return_type]
        rdo.product_goodwill!
        rdo.return.save!
      else
        notice = 'Please check state'
      end
    end
    if @user.present?
      # redirect_to user_order_return_items_path(order_number: rdo.return.order.number,return_id: rdo.return.id), :notice => "Tracking Details Updated Successfully. Your return request need to be approved by our support team."
      redirect_to :back, notice: "Tracking Details Updated Successfully. Your return request need to be approved by our support team."
    else
      notice == 'State updated' && rdo.return.check_if_all_rdo_have_tracking ? (redirect_to edit_return_path(rdo.return.id), notice: notice) : (redirect_to :back, notice: notice)
    end
  end

  def trigger_event_for_return_order
    return1 = Return.find_by_id(params[:return_id])
    if params[:commit] == 'Convert To Refund'
      notice = "Coupon was not found"
      coupon = Coupon.find_by_id return1.coupon_id
      if coupon.present? && return1.type_of_refund == 'Coupon'
        if coupon.use_count < coupon.limit
          coupon.end_date = coupon.start_date + 1.minute
          coupon.use_count = coupon.limit
          coupon.converted_to_refund = true
          return1.update_column(:type_of_refund,'Refund')
          if coupon.save
            notice = "Coupon #{coupon.code} is Expired and converted to Refund."
            return1.order.add_notes_without_callback(notice,'return',current_account)
          else
            notice = 'Coupon could not be updated but Return state was updated.'
          end
          return1.update_column(:state,'pending_payment') if return1.payment_complete?
        else
          notice = "Coupon is already used"
          if (used_order = Order.find_by_id(coupon.coupon_used_on_id)).present?
            notice += " on #{used_order.number}"
          end
        end
      elsif return1.type_of_refund == 'Wallet' && return1.payment_complete? && (user_wallet = return1.order.user.try(:wallet)).present?
        wallet_amount = (return1.discount/return1.order.currency_rate).round(2)
        if params[:wallet_amount].to_f <= wallet_amount && (completed_wt = return1.wallet_transactions.where(state: 'return_complete', fund_type: 'Credit').last).present? && completed_wt.wallet_id == user_wallet.id && user_wallet.debit(0, params[:wallet_amount].to_f)
          new_txn = WalletTransaction.create(order_id: return1.order_id,user_id: return1.user.id, return_id: return1.id, wallet_id: user_wallet.id, referral_amount: 0, return_amount: params[:wallet_amount].to_f, state: 'return_revoked', fund_type: 'Debit', notes: 'Amount transfered in bank account.')
          completed_wt.update_column(:state, 'return_success')
          values = {type_of_refund: 'Refund', state: 'pending_payment'}
          if params[:wallet_amount].to_f != wallet_amount
            return_amt = (params[:wallet_amount].to_f * return1.order.currency_rate).round(2)
            values.merge!({discount: return_amt, total: return_amt})
          end
          return1.update_columns(values)
          notice = "Wallet is debited and converted to Refund"
          return1.order.add_notes_without_callback(notice,'return',current_account)
        else
          notice = "Wallet Transaction of type Credit not found or user is invalid"
        end
      end
    else
      notice = "State updated"
      case params[:state].to_sym
      when :pending_payment
        if return1.can_all_products_received? && (!return1.refund_rejected? || (return1.refund_rejected? && current_account.present? && RETURN_ACCESS['refund_rejection'].include?(current_account.email)))
          return1.all_products_received!
        else
          notice = 'You are not authorised to change state.'
        end
      when :payment_complete
        if return1.can_payment_made?
          return1.payment_made!
        else
          notice = 'Please check state'
        end
      when :canceled
        if return1.can_return_canceled?
          return1.return_designer_orders.each do |rdo|
            rdo.products_kept! unless rdo.state == 'canceled'
          end
          return1.cancelled_by = current_account.id
          return1.return_canceled!
        else
          notice = 'Please check state'
        end
      end
    end
    redirect_to :back, :notice => notice
  end

  def trigger_event_for_design
    design = Design.find_by_id(params[:design_id])
    if design.present?
      case params[:state].to_sym
      when :delete
        if current_account.admin?
          design.delete_item_mirraw
        elsif design.designer.account.id == current_account.id
          design.delete_item_designer
        end
      when :seller_out_of_stock
        design.unavailable
      when :in_stock
        design.available
      when :reject
        design.reject_design
        design.notes = '' if design.notes.blank?
        design.notes = '. ' + params[:notes] + '.' if params[:notes].present?
      when :approved
        design.approved
      end
      if design.save!
        if params[:state] != 'reject'
          render :json => {:status => 'ok', :state => design.human_state_name}
        elsif params[:state] == 'reject'
          render :json => {:status => 'ok', :state => design.human_state_name, :notes => design.notes}
        end
      else
        render :json => {:status => 'error', :errors => design.errors}
      end
    else
      render :json => {:status => 'error', :errors => 'design not found'}
    end
  end

  def trigger_event_for_designer
    error = nil
    state = nil
    designer = Designer.includes(:slugs).where(id: params[:designer_id]).first
    if designer.present?
      case params[:state].to_sym
      when :approved
        designer.satisfied
      when :review
        designer.update_column(:approved_on,DateTime.now) if current_account.present? && ['accounts','accounts_admin'].include?(current_account.role.try(:name))
        designer.dissatisfied
      when :banned
        designer.banned_1
      when :on_hold
        designer.to_on_hold
      end
      if designer.save!
        state = designer.human_state_machine_name
      else
        error = 'Error while saving designer'
      end
    else
      error = 'Designer Not Found'
    end
    if error.present? then render :json => {:error => error} else render :json => {:state => state} end
  end

  def trigger_event_for_designer_issue
    designer_issue = DesignerIssue.find_by_id(params[:designer_issue_id])
    if designer_issue.present?
      case params[:state].to_sym
      when :resolved_by_coupon
        designer_issue.issued_coupon
      when :resolved_by_refund
        designer_issue.issued_refund
      when :resolved_by_replacement
        designer_issue.replacement_arranged
      end
      # designer_issue.save!
    end
    redirect_to :back
  end

end
