class PackageManagementController < ApplicationController
  before_filter :authenticate_account!, except: [:mark_qc_done]
  before_filter :hide_menu
  authorize_resource if: Proc.new { |a| a.is_from_wms?}
  require 'will_paginate/array' 
  layout 'admin', :only => [:dispatched_from_vendors, :index_in_scan, :report, :rtv_outscan, :rtv_outscan_report, :handover_to_stitching, 'out_scan', 'dispatched_today', 'items_received', 'pending_quality_check', 'items_not_marked_received', 'not_unpacked_orders', :tailoring_inscan, :stitching_searching_list, :post_tailoring_inscan_scans, :stitching_done_panel, :quality_repairable_products, :orders_followup, :post_packaging_scan, :process_pending_panels, :update_picking_config, :rto_inscan_panel, :rto_panel]
  include PackageManagementHelper
  before_filter :update_bulk_scan_input_params, only: [:tailoring_inscan, :post_tailoring_inscan_scans, :handover_to_stitching], if: proc { request.post? }

  def home
    get_packages                 = params[:get_packages].present? ? params[:get_packages] : 'nt'
    @designer_orders, @designers = list(false, nil, get_packages)
    render action: 'index'
  end

  def is_from_wms?
    if request.get?
      if params[:account_id].present?
        acc = Account.where(id: params[:account_id]).last
        return false if acc.present? && acc.send(:admin_account?)
      end
    end
    return true
  end

  def index_in_scan
    @integration_status = 'new'
    if request.get?
      @start_date,@end_date = get_date('month')
      @open_po_numbers = PurchaseOrder.where(status: 'open').pluck(:number).uniq
      w_bag_search, w_tracking_num_search, join_assoc, w_bag_status = '', '', '', ''
      w_bag_search = 'lower(number) = ?', params[:bag_number].strip.downcase if params[:bag_number].present?
      w_bag_status = 'lower(status) = ?', params[:bag_status] if params[:bag_status].present?
      tracking_num_searched = params[:tracking_num].try(:strip).try(:downcase)
      if tracking_num_searched.present?
        w_tracking_num_search = 'lower(designer_orders.tracking_num) = ? or lower(recent_tracking_number) = ?', tracking_num_searched, tracking_num_searched
        join_assoc = :designer_orders
      end
      @all_bags = InwardBag.preload(designer_orders: [:order, :line_items]).joins(join_assoc).where(w_tracking_num_search).where(w_bag_status).where('inward_bags.created_at between ? and ?', @start_date.beginning_of_day, @end_date.end_of_day).where(w_bag_search).order('created_at').uniq.paginate(page: params[:page], per_page: 20)
    end
    begin
      if params[:track_num].present?
        if params[:order_scan].present?
          barcode_split = params[:order_scan].include?('+') ? params[:order_scan].split('+') : params[:order_scan].split('-')
          des_ord_id = barcode_split[1].to_i 
        end
        response = "Tracking Number not found or Tracking Number already scanned."
        unless params[:order_scan].present?
          tracking_num = params[:track_num].strip.downcase
          designer_order = DesignerOrder.where('LOWER(tracking_num) = ? OR LOWER(recent_tracking_number) = ?',tracking_num,tracking_num).where("package_received_on is NULL OR recent_tracking_number is NOT NULL").pluck(:id)      
        else 
          designer_order = DesignerOrder.where('id = ?',des_ord_id).where("package_received_on is NULL OR recent_tracking_number is NOT NULL").pluck(:id)
          response = 'Replacement Product Scanned.' unless designer_order.present?
        end
        if designer_order.present?
          DesignerOrder.where(id: designer_order).update_all(:package_received_on => DateTime.current,:package_received_by => current_account.name,inscan_tracking_num:params[:track_num])
          response = 'Received on '+ DateTime.current.strftime('%a, %e %b') + ' by ' + current_account.try(:name)
          # PackageManagement.sidekiq_delay(queue: 'critical')
          #                  .assign_rack_and_code(designer_order,current_account)
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("PackageManagement", 
            nil, "assign_rack_and_code",
            designer_order,
            {current_account.class.to_s => current_account.id},
            false
          )
        end
        # @designer_orders, @designers = list(false, nil, 're')
        redirect_to :back, notice: response
        return
      elsif params[:csv_file].present? 
        if CSV.read(params[:csv_file].path).length <= 1000
          filename  = 'packages/inscan/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
          file = AwsOperations.create_aws_file(filename,params[:csv_file])
          uploader_name = current_account.name
          courier_company_name = params[:courier_company_name]
          # PackageManagementController.sidekiq_delay(queue: 'critical')
          #                            .mark_package_received_csv(
          #                              filename,courier_company_name,
          #                              current_account,
          #                              false
          #                            )
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("PackageManagementController", 
                                                                        nil, "mark_package_received_csv",
                                                                        filename,courier_company_name,
                                                                        {current_account.class.to_s => current_account.id},
                                                                        false
                                                                      )
          redirect_to (request.env["HTTP_REFERER"]||package_management_index_in_scan_path), :notice => 'Packages update is scheduled'
          return
        else
          redirect_to (request.env["HTTP_REFERER"]||package_management_index_in_scan_path), :notice => 'Please upload csv file with max. 1000 tracking numbers only.'
          return
        end
      elsif (csv_barcode = params[:csv_file_from_barcode]).present?
        if CSV.read(csv_barcode.path).length <= 1000
          filename  = 'packages/inscan/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
          file = AwsOperations.create_aws_file(filename,csv_barcode)
          # PackageManagementController.sidekiq_delay(queue: 'critical')
          #                            .mark_package_received_csv(
          #                              filename,
          #                              nil,
          #                              current_account,
          #                              true
          #                            )
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("PackageManagementController", 
                                                                        nil, "mark_package_received_csv",
                                                                        filename,nil,
                                                                        {current_account.class.to_s => current_account.id},
                                                                        true
                                                                      )                                     
          redirect_to :back, :notice => 'Packages update is scheduled'
          return
        else
          redirect_to :back, :notice => 'Please upload csv file with max. 1000 tracking numbers only.'
          return
        end
      end
    rescue NoMethodError => error
      error = csv_barcode.blank? || params[:csv_file].blank? ? 'CSV file not uploaded' : error.message
      redirect_to (request.env["HTTP_REFERER"]||package_management_index_in_scan_path), :notice => error
      return
    rescue => error
      redirect_to (request.env["HTTP_REFERER"]||package_management_index_in_scan_path), :notice => error.message
      return
    end
    render :action => 'index'
  end

  def update_bag_details
    if request.post?
      notice = 'Bag Could not be found.'
      if params[:bag_id].present? 
        if params[:old_bag_status] != params[:bag_status]
          InwardBag.where(id: params[:bag_id]).update_all(status: params[:bag_status])
          notice = 'Successfully Updated Bag Status.'
        else
          notice = ''          
        end
        if params[:new_tracking_number].present?
          des_ord = DesignerOrder.preload(:inward_bag).where('(lower(tracking_num) = ? and package_status in (?) and inward_bag_id is null) or (lower(recent_tracking_number) = ?)', params[:new_tracking_number].downcase, ['packed', 'inscanned'], params[:new_tracking_number].downcase)
          if des_ord.present?
            des_ord.update_all(inward_bag_id: params[:bag_id], package_received_on: DateTime.current, package_received_by: current_account.name, package_status: 'inscanned')
            LineItem.bulk_add_into_scan('DesignerOrder', des_ord.map(&:id), ['Package Inscan', "Assigned To Bag Number #{params[:old_bag_number]}"], current_account.id)
            InwardBag.where(id: params[:bag_id]).update_all(status: 'open')                        
            notice = 'Successfully Updated with New Package.'          
          else
            notice = 'Tracking Number provided could not be found or may be already unpacked or is in some other bag.'
          end
        end
      end
      redirect_to (request.env["HTTP_REFERER"] || package_management_index_in_scan_path), notice: notice
    end
  end

  def update_bag_or_package
    if request.post?
      if params[:action_name] == 'delete'
        response = {error: true, message: 'Could Not Delete. Please try again.'}
        if params[:entity] == 'bag'
          if (bag = InwardBag.find_by_id(params[:entity_id])) && (bag_number = bag.try(:number)) && bag.try(:destroy)  
            des_orders = DesignerOrder.where(inward_bag_id: params[:entity_id])
            des_orders.update_all(inward_bag_id: nil, package_received_on: nil)
            LineItem.bulk_add_into_scan('DesignerOrder', des_orders.map(&:id), ["Deleted Bag #{bag_number}"], current_account.id)
            response = {message: 'Successfully Deleted.'} 
          end
        elsif params[:entity] == 'package'
          DesignerOrder.where(id: params[:entity_id]).update_all(inward_bag_id: nil, package_received_on: nil)
          LineItem.bulk_add_into_scan('DesignerOrder', params[:entity_id], "Removed from Bag", current_account.id)
          response = {message: 'Package Successfully Deleted.'} 
        end
      else
        response = {error: true, message: 'Could Not Update. Please try again.'}
        if params[:package_id].present? && params[:new_status].present?
          if DesignerOrder.where(id: params[:package_id]).update_all(package_status: params[:new_status])
            if (bag = InwardBag.preload(:designer_orders).where(id: params[:bag_id]).first)
              need_to_open = bag.designer_orders.map(&:package_status).any?{|status| ['packed', 'inscanned'].include?(status)}
              new_status = need_to_open ? 'open' : 'close'
              if new_status != bag.status
                bag.update_column(:status, new_status)
              end
              response = {message: 'Successfully Deleted.', new_status: new_status}
              if (p_order = bag.purchase_order).present?
                update_purchase_order_status(p_order)
              end
            end            
          else
            response = {error: true, message: 'Package Could Not be Found.'}
          end
        end
      end
      render json: response
    end
  end


  def unpack_line_item
    line_item = LineItem.find_by_id(params[:unpacking_item][:item_id].to_i)
    tracking_number = params[:unpacking_item][:tracking_number]
    line_item.unpacking_status = true
    if (rtv_reason = params[:rtv_reason]).present?
      order_number = line_item.designer_order.order.number
      design = line_item.design
      line_item.rtv_reason = rtv_reason
      # DesignerMailer.sidekiq_delay
      #               .product_received_issue(line_item, 'QC Failed', rtv_reason)
      SidekiqDelayGenericJob.perform_async("DesignerMailer", 
                      nil, "product_received_issue",
                      {line_item.class.to_s => line_item.id},
                      'QC Failed',
                      rtv_reason
                    )
    end
    if line_item.qc_done.nil?
      line_item.qc_done = "Y"
      line_item.qc_done_on = Time.now
      change_quality_level(line_item.design,3)
    end
    respond_to do |format|
      if line_item.save
        format.html { redirect_to tracking_details_path(tracking_number: tracking_number, bag_number: params[:unpacking_item][:bag_number]), notice: 'Unpacking of item successfully created.' }
      end
    end
  end

  def assign_rtv_rack
    response = {error: true, error_text: ''}
    if params[:rack_list_code].present? && params[:item_id].present?
      rack_list = RackList.find_by_code(params[:rack_list_code])
      line_item = LineItem.find_by_id(params[:item_id])
      item_rack_code = line_item.rack_list_code
      if line_item.present? && rack_list.present? && item_rack_code != rack_list.code
        total_quantity = line_item.quantity
        line_item.update_column(:rack_list_code,rack_list.code)
        rack_list.change_quantity(total_quantity,'add')
        response[:error] = false
        previous_rack = item_rack_code.present? ? item_rack_code : ''
        line_item.designer_order.add_notes_without_callback("Line items Rack code changed from #{previous_rack} to #{rack_list.code}",'rack_change',current_account) 

      end
    end
    render :json => response
  end

  def mark_package_received(designer_order)
    response = {:error => 'designer order not found'}
    if (dso_id = designer_order.id).present?
      designer_order = DesignerOrder.where(id: dso_id).update_all(:package_received_on => DateTime.current,:package_received_by => current_account.name)
      response = {:received_status => 'Received on '+ DateTime.current.strftime('%a, %e %b') + ' by ' + current_account.name} if designer_order.present? and designer_order > 0
    end
  end

  def self.mark_package_received_csv(filename,courier_company,uploader_account,barcode_scan)
    uploader_name = uploader_account.name
    bucket = (Rails.env.production? || Rails.env.admin? ) ? 'mirraw' : 'mirraw-test'
    full_path = 'https://s3-ap-southeast-1.amazonaws.com/' + bucket + '/' + filename
    tracking_numbers_all ={}
    if !barcode_scan
      CSV.new(open(full_path), {:headers => true, :header_converters => :symbol}).each do |line|
        begin
          tracking_numbers_all[line[:tracking_no].downcase] = DateTime.parse(line[:date]) if (line[:tracking_no].present? && line[:date].present?)
        rescue ArgumentError
          tracking_numbers_all[line[:tracking_no].downcase] = DateTime.current
        end
      end
      tracking_numbers_all.each_slice(100) do |tracking_numbers|
        tracking_numbers = tracking_numbers.to_h 
        tracking_numbers_group_by_date = tracking_numbers.group_by{|key,value| value}
        tracking_numbers_group_by_date.each do |key,value|
          flatten_array_of_tracking_numbers = value.flatten
          flatten_array_of_tracking_numbers.delete(key)
          flatten_array_of_tracking_numbers.map!(&:downcase)
          flatten_array_of_tracking_numbers.map!(&:strip)
          received_time = DateTime.parse("#{key.strftime('%y/%m/%d')} #{Time.now.strftime('%H:%M:%S')}")
          if courier_company.present?
            courier_company.downcase!
            all_designer_order = DesignerOrder.where(state: ['dispatched','completed']).where('created_at >= ?',1.month.ago).where("package_received_on is NULL OR recent_tracking_number is NOT NULL").where('LOWER(tracking_num) LIKE (?) OR LOWER(recent_tracking_number) LIKE (?)',"%#{courier_company}%","%#{courier_company}%")
            all_designer_order_hash = {}
            all_designer_order.select('id,lower(tracking_num) as tracking_num,lower(recent_tracking_number) as recent_tracking_number,package_received_on').each do |dso|
              all_designer_order_hash[dso.id] = [dso.tracking_num,dso.recent_tracking_number,dso.package_received_on]
            end  
            designer_order_hash = all_designer_order_hash.dup
            designer_order_hash.each do |k,v|
              flatten_array_of_tracking_numbers.each do |number| 
                include_flag =false
                if number.length > 3 
                  include_flag = (v[0].include?(number) && v[2].nil?) || (v[1].present? && v[1].include?(number))
                else
                  flatten_array_of_tracking_numbers.delete(number)
                end
                if include_flag
                  flatten_array_of_tracking_numbers.delete(number)
                  designer_order_hash.delete(k)
                end
              end
            end            
            designer_order_found  = all_designer_order_hash.keys - designer_order_hash.keys
            DesignerOrder.where(id: designer_order_found).update_all(package_received_on: received_time,package_received_by: uploader_name)
            PackageManagement.assign_rack_and_code(designer_order_found,uploader_account)
          else 
            designer_orders = DesignerOrder.where('created_at >= ?',1.month.ago).where('LOWER(designer_orders.tracking_num) IN (?) OR LOWER(designer_orders.recent_tracking_number) IN (?)',flatten_array_of_tracking_numbers,flatten_array_of_tracking_numbers).pluck(:id)
            DesignerOrder.where(id: designer_orders).where("package_received_on is NULL OR recent_tracking_number is NOT NULL").update_all(:package_received_on => received_time,:package_received_by => uploader_name)
            PackageManagement.assign_rack_and_code(designer_orders,uploader_account)
          end
        end
      end
    else
      all_des_orders = {}
      inscan_tracking = {}
      CSV.new(open(full_path), {:headers => true, :header_converters => :symbol}).each do |line|
        barcode_split = (line[:order_scan].include?('+') ? line[:order_scan].split('+') : line[:order_scan].split('-')) if line[:order_scan].present?
        des_ord_id = barcode_split[1].to_i
        des_ord_id = barcode_split[0] if barcode_split.length == 1
        all_des_orders[des_ord_id]  = (line[:date].present? ? Time.zone.parse(line[:date]) : DateTime.current)
        inscan_tracking[des_ord_id] = line[:tracking_no]
      end
      all_des_orders.each_slice(100) do |des_ord|
        des_ord = des_ord.to_h
        designer_orders_group_by_date = des_ord.group_by{|key,value| value}
        designer_orders_group_by_date.each do |key,value|
          flatten_array_of_designer_orders = value.flatten
          flatten_array_of_designer_orders.delete(key)
          received_time = Time.zone.parse("#{key.strftime('%Y/%m/%d')} #{Time.now.strftime('%H:%M:%S')}")
          designer_orders = DesignerOrder.where('id IN (?)',flatten_array_of_designer_orders).where("package_received_on is NULL OR recent_tracking_number is NOT NULL")
          import_des_ords = []
          designer_orders.each do |des|
            des.package_received_on = received_time
            des.package_received_by = uploader_name
            des.inscan_tracking_num = inscan_tracking[des.id]
            import_des_ords << des
          end
          DesignerOrder.import import_des_ords, validate: false, on_duplicate_key_update: { conflict_target: [:id], columns: [:package_received_on,:package_received_by,:inscan_tracking_num]}
          PackageManagement.assign_rack_and_code(flatten_array_of_designer_orders,uploader_account)
        end
      end
      not_scanned_designer_orders = DesignerOrder.where('id IN (?) AND package_received_on is NULL AND package_received_by is NULL',all_des_orders.keys).all
      ShipmentMailer.sidekiq_delay
                    .mail_in_scan_report_status(
                      uploader_account,
                      not_scanned_designer_orders
                    ) if not_scanned_designer_orders.present?
    end
  end

  def items_received
    @hide_designer_list    = false
    @start_date, @end_date = get_date
    w_received_between     = 'received_on BETWEEN ? and ?', @start_date, @end_date.end_of_day
    @line_items            = LineItem.includes([:designer_order => :order], :design => [:designer, :images]).where(w_received_between).where(:received => 'Y')
    s_received_by_count    = 'received_by, count(line_items.id)'
    @line_item_received    = LineItem.select(s_received_by_count).joins(:design).where(w_received_between).where(:received => 'Y').group(:received_by)
    if params[:designer_id].present? and params[:designer_id] != 'All'
      @line_items          = @line_items.where('designs.designer_id = ?', params[:designer_id])
      @line_item_received  = @line_item_received.where('designs.designer_id = ?', params[:designer_id])
      @designer_id         = params[:designer_id]
    end
    @line_items            = @line_items.paginate(:page => params[:page])
    @designers             = @line_items.collect {|item| [item.design.designer.name, item.design.designer.id]}.prepend(['All','All']).uniq
  end

  def detail
    get_packages                 = params[:get_packages].present? ? params[:get_packages] : 'all'
    @designer_orders, @designers = list(true, nil, get_packages)
  end

  def report
    @start_date, @end_date       = get_date
    @designer_orders, @designers = list(true, period = {:start_date => @start_date.beginning_of_day, :end_date => @end_date.end_of_day})
    if params[:commit] == "DOWNLOAD REPORT"
      begin
        dir = "tmp/reports/"
        FileUtils.mkdir_p(dir) unless File.directory?(dir)
        filepath = dir + "packages_received.csv"
        header = ['Order Number','Designer','Tracking Number','Tracking Partner','Transit Time','Package Status','Designer Order Items Count','Not Received Items']
        CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
          csv << header
          @designer_orders.each do |des_od|
            des_line_items = des_od.line_items
            package_status = 'Not Received'
            if des_od.package_received_on.present? and des_od.package_received_by.present?
              package_status = 'Received on ' + des_od.package_received_on.strftime('%a, %e %b') + ' by ' + des_od.package_received_by
            end
            line_items_count = des_line_items.size >0 ? des_line_items.size : nil
            not_received_count = (not_rec_count = des_line_items.collect(&:received_on).count(nil)) > 0 ? not_rec_count : nil
            csv << [des_od[:order_number],des_od[:designer_name],des_od.tracking_num,des_od.tracking_partner,des_od[:transit_time],package_status,line_items_count,not_received_count]
          end
          send_file filepath and return
        end
      rescue
        flash[:notice] = "Error Downloading File."
      end
      redirect_to :back
    end
  end

  def mark_warehouse_received
    response = {received_status: 'Not received',error: true}
    if params[:barcode].present? && (barcode_split = params[:barcode].split('-')).length.between?(4,5)
      current_time = DateTime.now
      #62D-MIRWH153462811-116315-D-1
      #70A-MIRWH571012144-3301-22821+L-1
      rack_code,warehouse_order,design_id,size_kind,index_of_wli = barcode_split
      design_query = size_kind.exclude?('+') ? {design_id: design_id.to_i} : {design_id: design_id.to_i,variant_id: size_kind.split('+')[0].to_i}
      item = LineItem.where(id: params[:line_item_id].to_i,rack_list_code: rack_code).where(design_query).update_all(received: 'Y', received_by: current_account.name, received_on: current_time)
      if item > 0
        line_item = LineItem.find_by_id(params[:line_item_id].to_i)
        designer_order_id = line_item.designer_order_id
        designer_order = DesignerOrder.find_by_id(designer_order_id)
        response = {received_status: "Received by #{current_account.name} "\
          "(#{current_time.strftime('%a, %e %b')}) ",received_code: "#{designer_order.rack_code}",received_rack: "#{designer_order.rack_list.try(:code)}",designer_order_id: designer_order_id.to_s}
        check_package(designer_order)
        line_item.add_into_scan('Received In Warehouse', current_account.id)
        LineItem.update_rack_status(condition: {id: line_item.id}, rack_log: false)
      end
    end
    render json: response
  end

  def mark_item_received
    current_time = DateTime.now
    response = {:error => 'Error adding item received details'}
    if (item_id = params[:item]).present?
      item = LineItem.where(:id => item_id).update_all(:received => 'Y', :received_by => current_account.name, :received_on => current_time)
      if item.present? and item > 0
        designer_order = DesignerOrder.joins(:line_items).where('line_items.id = ?', item_id).first
        if (dns = designer_order.delivery_nps_info).present? && dns.actual_delivery_date.blank?
          dns.update_attributes(actual_delivery_date: Time.current)
        end
        # if params[:rack_list_code].present?
        #   rack_list = RackList.find_by_code(params[:rack_list_code])
        #   designer_order.rack_list_id = rack_list.id if rack_list.present?
        # end
        # set_item_code(designer_order)
        response = {received_status: "Received by #{current_account.name} "\
          "(#{current_time.strftime('%a, %e %b')}) ",received_code: "#{designer_order.rack_code}",received_rack: "#{designer_order.rack_list.try(:code)}",designer_order_id: designer_order.id.to_s}
        check_package(designer_order)
        line_item = LineItem.find_by_id(item_id)
        line_item.add_into_scan('Received In Warehouse', current_account.id) if line_item
      end
    end
    render :json => response
  end

  ## Quality Check
  def mark_qc_done
    if params[:account_id] #for wms
      acc = Account.where(id: params[:account_id]).last
      current_account_wms = (acc.present? && acc.send(:admin_account?)) ? acc : nil
      if !current_account_wms
        render :json => {:error => 'Error Not an Admin user'} and return
      else
        from_wms = true
      end
    end
    current_time = DateTime.now
    response = {:error => 'Error adding item qc details'}
    # from_wms = false
    scan_types = []
    if params[:item].present?
      status = params[:status] ## returns true/false
      line_items = LineItem.where(id: params[:item]).preload(:design,:order)
      item = from_wms ? line_items.last : line_items.last.mark_qc(status, current_account_wms || current_account)

      ## notes add to the order and designer order
      ## and designer Issue is also created.
      order = line_items.first.designer_order.order
      if line_items.first.qc_status == false
        issue_note_msg = ''
        raise_designer_issue(line_items, 'QC Failed') if DesignerIssue.where(design_id: line_items.first.design_id, order_id: order.id).blank?
        if params[:quality_issue_id].present?
          is_repairable = params[:repairable].try(:downcase)
          issue_note_msg = store_quality_issue_data(line_items.first.id, params[:quality_issue_id], params[:other_reason], is_repairable, params[:qc_issue_name])
          scan_types << params[:qc_issue_name] if params[:qc_issue_name] != 'Other'          
          line_items.first.create_issue_for_item(order, current_account_wms || current_account, 'false', '', issue_note_msg, params[:replacement_qty], (is_repairable == 'no' ? 'Yes': 'No'))          
        end
        add_notes(line_items, issue_note_msg)      
      end
      if item.present?
        line_item = item
        designer_order = line_item.designer_order 
        val = (status =='true') ? "Passed" : "Failed"
        response = {:qc_status => 'status:' + val + ' ' + 'QC Done: '+ (current_account_wms || current_account).name + '(' + current_time.strftime('%a, %e %b') + ')', :stitching_required => line_items.first.stitching_required} 
        if val == "Failed" && (order.order_notification.blank? || (order.order_notification.present? && order.order_notification[':qc_failed'].blank?))
          order.order_notification.store(:qc_failed,1)
          OrderMailer.sidekiq_delay
                     .send_issue_qcfail_mail_to_user(
                       order.id,
                       'Failed Quality Check',
                       line_item_id:line_item.id
                     )
          # order.sidekiq_delay
          #      .send_line_items_notification(
          #        'qc_failed',
          #        line_items.first.design.master_image.photo(:large_m)
          #       )
          SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'qc_failed', line_items.first.design.master_image.photo(:large_m))
          order.save!
          order.send_confirmation_sms('Failed Quality Check') if order.country.downcase == 'india'      
        end        
        if line_item.qc_status && params[:bucket_code].present?
          line_item.assign_to_warehouse_bucket(params[:bucket_code], params[:bucket_type], params[:fake_rack], params[:product_type], order, (current_account_wms || current_account).id)
        end
        if params[:wrong_product].present? && params[:wrong_product].downcase == 'y'          
          item_cost = designer_order.transaction_rate.to_f > 0 ? (line_item.vendor_selling_price.present? ? line_item.vendor_selling_price : line_item.unscaled_price) : line_item.snapshot_price
          amount = 0.3*item_cost.to_f
          amount *= line_item.quantity.to_i
          adjustment_note = "Wrong Product Received Cost for Order #{params[:order_no]} - Design ID: #{line_item.design_id} Quantity: #{line_item.quantity}"
          Adjustment.create(amount: -1*amount, designer_id: designer_order.designer.id, order_id: order.id, notes: adjustment_note, status: 'unpaid', designer_order_id: designer_order.id)
          des_order_note = "Negative Adjustment Created for Wrong Product Received - Design ID: #{line_item.design_id} Quantity: #{line_item.quantity}"
          designer_order.add_notes_without_callback(des_order_note,'adjustment', current_account_wms || current_account)
          scan_types << "Negative Adjustment Created - #{line_item.quantity} Qty"          
        end        
        line_item.add_into_scan(scan_types, (current_account_wms || current_account).id)
        designer_order.assign_available_rack unless order.ready_for_dispatch?
      end
      after_qc_pass(line_items, status, params[:rate], current_account_wms || current_account)
    end
    render :json => response
  end

  def mark_qc_issue_list
    response = {error: 'Something wrong with the system'}
    issue = params[:issue]
    item_id = params[:item]
    quality_check = QualityCheck.where(qc_type_name: issue).first
    if quality_check.present? && params[:reason].present? && params[:reason] == 'true'
      qc_fail_reason = "Quality check done for : #{issue}"
      QualityCheckValue.where(line_item_id: item_id, quality_check_id:quality_check.id, qc_reason: qc_fail_reason,qc_type_flag: true).create
      response = {msg: 'Quality check done: ' + issue }
    else
      if item_id.present? && quality_check.present?
        qc_fail_reason = params[:reason]
        if ( qc_value = QualityCheckValue.where(line_item_id: item_id, quality_check_id:quality_check.id)).present?
          qc_value.first.update_column(:qc_reason, params[:reason])
          response = {msg: 'QC failed issue updated'}
        else
          # if quality check fails then qc_type_flag will be false
          QualityCheckValue.where(line_item_id: item_id, quality_check_id:quality_check.id, qc_reason: qc_fail_reason,qc_type_flag: false).create
          order = LineItem.where(id: item_id).first.designer_order.order
          order.add_tags_skip_callback('issue')
          order.add_notes_without_callback("issue in quality check (#{issue})",'other',current_account)  
          response = {msg: 'QC failed new issue created'}
        end
      end
    end
    render json:  response
  end

  def mark_stitching_sent
    current_time = DateTime.now
    response = {:error => 'Error adding stitching details'}
    if (item_id = params[:item]).present?
      item = LineItem.where(:id => item_id).update_all(:stitching_sent => 'Y', :stitching_sent_by => current_account.id, :stitching_sent_on => current_time)
      if item.present? and item > 0
        LineItem.bulk_add_into_scan('LineItem', item_id, 'Stitching Sent', current_account.id)
        response = {:stitching_sent_status => 'Stitching Sent: '+ current_account.name + '(' + current_time.strftime('%a, %e %b') + ')'}
      end
    end
    render :json => response
  end

  def mark_stitching_done
    current_time = DateTime.now
    response = {error: 'Error adding stitching details. Either Stitching is not marked required or fabric measurement is not yet done.'}
    if (item_id = params[:item]).present? && (line_items = LineItem.preload(:tailoring_info, :design, line_item_addons: :addon_type_value).where(id: item_id).where("((fabric_measured_on is not null and stitching_done_on is null) OR (available_in_warehouse = true)) and stitching_required = ?", 'Y')).present? && (last_item = line_items.last) && last_item.stitching_done.blank? && (last_item.fabric_measured_on.present? || (last_item.available_in_warehouse && (already_stitched = last_item.already_stitched?)))
      tailoring_info = last_item.tailoring_info
      design = last_item.design      
      addon_type_value_names = last_item.line_item_addons.map(&:addon_type_value).map(&:name)
      is_only_pcot_product = design.designable_type == 'Saree' && (addon_type_value_names - ['Petticoat Stitching', 'No Fall and Pico', 'Unstitched Blouse']).blank?
      if is_only_pcot_product || !tailoring_info.any?{|t| t.material_received_status.nil?} || already_stitched
        LineItem.sidekiq_delay(queue:'low').mark_fabric_measurement_done_and_confirmed(item_id,current_account.id,current_account.name) if already_stitched
        new_item_details = line_items.first.store_item_details(detail_name: 'fake_rack', action: :delete, update: false)
        AppEvent::ItemEvent.new(item_id.to_i, "Stitching Done").trigger_clevertap_event_deliver_later
        item = line_items.update_all(stitching_done: 'Y', stitching_done_by: current_account.id, stitching_done_on: current_time, item_details: new_item_details)
        warehouse_bucket = WarehouseBucket.find_by_code "stitching-1"         ##  Assigning bucket realtion to stitching-1 bucket by deault on live 
        line_items.first.bucket_relations.last.update_attributes(entity_status:'Tailor Assigned', bucket_kind:'StitchingBucket', warehouse_bucket_id: (warehouse_bucket.try(:first).try(:id) or 1))
        designer_order = line_items[0].designer_order
        if (rack_list = designer_order.rack_list).blank? && params[:rack_code].present?
          all_line_items = designer_order.line_items.to_a
          total_quantity = all_line_items.sum(&:quantity)
          designer_order.add_notes_without_callback("Rack changed from #{rack_list.code} to #{params[:rack_code]}", 'rack_change', current_account)          
          designer_order.update_column(:rack_list_id, RackList.where(code: params[:rack_code]).first.id)
          designer_order.change_rack_quantity(total_quantity,'add')
          LineItem.bulk_add_into_scan('DesignerOrder', designer_order.id, 'Rack changed', current_account.id)
          if rack_list.description == 'stitching_done' && total_quantity > 1
            rack_list.change_quantity(total_quantity)
          else
            if all_line_items.map(&:fabric_measured_on).compact.length > 1
              fabric_measured_count = all_line_items.select{|i| i.fabric_measured_on.present?}.sum(&:quantity)
              total_quantity -= fabric_measured_count
            else
              total_quantity -= line_items.first.quantity
            end
            rack_list.change_quantity(total_quantity) if total_quantity > 0
          end
        end
        if item.present? and item > 0
          order = designer_order.order
          last_item.stitching_measurements.each(&:measurement_approved) if already_stitched
          LineItem.bulk_add_into_scan('LineItem', item_id, 'Stitching Done', current_account.id)
          AppEvent::ItemEvent.new(item_id.to_i, "Stitching Done").trigger_clevertap_event_deliver_later
          LineItem.update_rack_status(condition: {id: item_id.to_i})
          # order.sidekiq_delay(queue: 'high').mark_order_as_stitching
          SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
          # Order.sidekiq_delay(queue: 'low')
          #      .check_status_line_items(order, 'stitching_done')
          SidekiqDelayGenericJob.set({queue: 'low'}).perform_async("Order", nil , "check_status_line_items", {order.class.to_s => order.id}, 'stitching_done')
          response = {stitching_done_status: ('Stitching Done: '+ current_account.name + '(' + current_time.strftime('%a, %e %b') + ')'), sticker_link: rack_code_sticker_url(designer_order.id, format: :pdf, design_id: last_item.design_id)}
          # order.sidekiq_delay(priority: -1).check_order_valid_for_rfd(true)
          SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(order.class.to_s, order.id, "check_order_valid_for_rfd", true)
        end
      else
        response = {error: 'All tailoring materials are not yet mark received.'}
      end
    end
    render :json => response
  end

  def mark_fabric_measurement_done
    render json: LineItem.mark_fabric_measurement_done(params[:item],current_account.id,current_account.name)
  end

  def mark_stitching_required
    current_time = DateTime.now
    response = {:error => 'Error adding stitching details'}
    if (item_id = params[:item]).present?
      line_item = LineItem.find_by_id(item_id)
      response = line_item.mark_stitching_required(current_account) if line_item.present?    
    end
    render :json => response
  end

  def remove_stitching_required
    response = {error: 'Error removing line item'}
    if params[:item].present? && (item = LineItem.preload(designer_order: :order).where(id: params[:item]).first)
      item.update_column(:stitching_required, nil)
      designer_order = item.designer_order
      order = designer_order.order
      designer_order.add_notes_without_callback("Removed Stitching from Design: #{item.design_id}",'stitching', current_account)
      # order.sidekiq_delay.send_stitching_info_mail_to_user
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_stitching_info_mail_to_user")
      item.add_into_scan('Removed Stitching Required', current_account.id)
      # order.sidekiq_delay(queue: 'critical').mark_order_as_stitching
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(order.class.to_s, order.id, "mark_order_as_stitching")
      # order.sidekiq_delay(queue: 'low').update_cost_estimation if order.international?
      SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(order.class.to_s, order.id, "update_cost_estimation") if order.international?
      response = {stitching_required_status: 'Removed Stitching Required'}
    end
    render json: response
  end

  def items_not_marked_received
    w_order_not_state = ['dispatched','pickedup', 'cancel', 'complete', 'reject', 'cancel_complete']
    @shipper_names = Shipper.pluck(:name)

    designer_orders_details = LineItem.joins(:designer_order => :order).
      where(:designer_orders => {:state => 'completed'}).
      where('line_items.received IS NULL OR line_items.received <> ?', 'Y').
      where('orders.state NOT IN (?)', w_order_not_state).
      where('orders.country <> ?', 'India').
      where('orders.created_at > ?', 3.months.ago).
      select('COUNT(line_items.id) as pending_items_count, designer_orders.id').
      group('designer_orders.id')

    if params[:shipper_name].present?
      designer_order_details = designer_orders_details.where(:designer_orders => {:tracking_partner => params[:shipper_name]})
    end
    designer_order_details = designer_orders_details.order('COUNT(line_items.id) ASC')

    ids = designer_orders_details.collect(&:id)
    assoc = :shipment, [:line_items => [[:design => [:variants, :images]], :line_item_addons]], :order, :designer

    @designer_orders = DesignerOrder.includes(assoc).where(:id => ids).paginate(:page => params[:page], :per_page => params[:items_per_page] || 5)
  end

  def mark_measurement_confirmed
    render json: LineItem.mark_fabric_measurement_confirmed(params[:item],current_account.id,current_account.name)
  end

  def pending_quality_check
    sla_count = params[:sla].present? ? params[:sla].to_i : 1
    w_clause = "orders.country <> 'India' AND designer_orders.package_received_on IS NOT NULL AND "
    w_clause += 'line_items.received_on IS NOT NULL AND line_items.qc_done IS NULL '
    w_sla_clause = "(current_date - date(line_items.received_on)) > ?"

    line_items = LineItem.joins(designer_order: :order).where(w_clause)
    line_item_ids = line_items.where(w_sla_clause, sla_count).pluck('line_items.id')

    @line_items = LineItem.includes(designer_order: :order).where(id: line_item_ids).
                  order('line_items.id DESC').paginate(page: params[:page], per_page: params[:item_per_page])

    @line_items_count = {}
    (1..3).each do |index|
      @line_items_count[index] = line_items.where(w_sla_clause, index).count
    end
  end

  def not_unpacked_orders
    @integration_status = 'new'
    sla_count = params[:sla].present? ? params[:sla].to_i : 1
    w_clause = "orders.country <> 'India' AND  "
    w_clause += "line_items.received_on IS NULL AND "
    w_clause += "designer_orders.package_received_on IS NOT NULL AND designer_orders.state NOT IN('canceled','vendor_canceled')"
    w_sla_clause = "(current_date - date(designer_orders.package_received_on)) > ?"

    line_items = LineItem.joins(designer_order: :order).where(w_clause)
    line_item_ids = line_items.where(w_sla_clause, sla_count).pluck('line_items.id')

    # For CSV
    if params[:csv_data].present?
      PackageManagementController.new.sidekiq_delay
                                     .report_csv(
                                       PER_PAGE,
                                       line_item_ids,
                                       params[:action],
                                       current_account.email
                                     )
      flash[:notice] = "Download report link has been sent to #{current_account.email} successfully."
      redirect_to packages_not_unpacked_orders_path
    end
    @line_items = LineItem.includes(designer_order: :order).where(id: line_item_ids).
                  order('line_items.id DESC').paginate(page: params[:page], per_page: params[:item_per_page])

    @line_items_count = {}
    (1..3).each do |index|
      @line_items_count[index] = line_items.where(w_sla_clause, index).count
    end

  end

  def report_csv(per_page, line_item_ids, filename, email)
    @line_items = LineItem.includes(designer_order: :order).where(id: line_item_ids).order('line_items.id DESC').paginate(page: 1, per_page: per_page)
    html_string = render_to_string(:partial => 'package_management/not_unpacked_orders/list.html.haml',
      locals:{line_items: @line_items}, layout: false)
    report_file_aws(html_string, filename, email)
  end

  def dispatched_from_vendors
    @integration_status = 'new'
    sla_day =  params[:sla_day].present? ? params[:sla_day].to_i : 4
    w_clause = " orders.country <> 'India' AND line_items.received_on IS NULL AND "
    w_clause += "designer_orders.pickup IS NOT NULL AND "
    w_clause += "designer_orders.tracking_num IS NOT NULL AND designer_orders.package_received_on IS NULL AND "
    w_clause += "(current_date - date(designer_orders.pickup)) > #{sla_day}"
    @urgent_orders = LineItem.unscoped.select("orders.number as order_number, designer_orders.pickup as designer_order_pickup, designer_orders.tracking_num, designers.name as dname, designer_orders.tracking_partner, shipments.shipment_state as shipment_state").joins(
            'line_items LEFT OUTER JOIN
            designer_orders ON designer_orders.id = line_items.designer_order_id LEFT OUTER JOIN
            designers ON designers.id = designer_orders.designer_id LEFT OUTER JOIN
            orders ON orders.id = designer_orders.order_id LEFT OUTER JOIN
            shipments ON shipments.designer_order_id = designer_orders.id LEFT OUTER JOIN
            taggings on  orders.id  = taggings.taggable_id LEFT OUTER JOIN
            tags on tags.id = taggings.tag_id')
            .where(w_clause).where('taggings.taggable_type = ? and tags.name = ?', 'Order', 'urgent').where(designer_orders: {state: ['dispatched','pickedup']}).group('orders.id, designer_orders.id, shipments.id, tags.id, taggings.id, designers.id').to_a
    @line_items = LineItem.unscoped.select("orders.number as order_number, designer_orders.pickup as designer_order_pickup, designer_orders.tracking_num, designers.name as dname, designer_orders.tracking_partner, shipments.shipment_state as shipment_state").joins(
            'line_items LEFT OUTER JOIN
            designer_orders ON designer_orders.id = line_items.designer_order_id LEFT OUTER JOIN
            designers ON designers.id = designer_orders.designer_id LEFT OUTER JOIN
            orders ON orders.id = designer_orders.order_id LEFT OUTER JOIN
            shipments ON shipments.designer_order_id = designer_orders.id')
            .where(w_clause).where(designer_orders: {state: ['dispatched','pickedup']}).group('orders.id, designer_orders.id, shipments.id, designers.id').order('orders.created_at DESC')
    @line_items = @line_items.paginate(:page =>params[:page])
  end

  def dispatched_today
    @integration_status = 'new'
    order_ids = Order.joins(shipments: :shipper).where("orders.country <> 'India'").where(state: ['partial_dispatch','dispatched'], pickup: Date.today.beginning_of_day..Date.today.end_of_day).pluck('orders.id')
    all_orders = Order.includes(shipments: :shipper).where(id: order_ids)
    order_stitching_ids = Order.tagged_with('stitching').pluck('orders.id') # manually aading stitching tag
    stitching_addon_orders_ids = all_orders.stitching.pluck('orders.id')
    all_stitching_orders_ids = order_stitching_ids + stitching_addon_orders_ids
    all_stitching_orders = Order.where(id: all_stitching_orders_ids)

    @orders_count = all_orders.count
    @all_stitching_orders_count =  all_stitching_orders.count
    @orders = params[:order_type] == 'stitching' ? all_stitching_orders : all_orders
    @orders = @orders.paginate(page: params[:page])
  end

   def out_scan
    @integration_status = 'new'
    @shippers = Shipper.all.collect{|sh| [sh.name, sh.id]} if request.get?
    if params[:csv_file].present?
      begin
        filename  = 'packages/outscan/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
        file = AwsOperations.create_aws_file(filename,params[:csv_file])
        PackageManagement.sidekiq_delay
                         .csv_mark_package_out_scanned(filename,current_account)
        redirect_to :back, :notice => 'Packages update is scheduled'
      rescue NoMethodError => error
        error = params[:csv_file].blank? ? 'CSV file not uploaded' : error.message
        redirect_to :back, :notice => error
      rescue => error
        redirect_to :back, :notice => error.message
      end
    elsif params[:order_number].present?
      order_number = params[:order_number].strip.upcase
      tracking_number = params[:tracking_number].strip.upcase
      batch_number = params[:batch_number].strip
      order = Order.where(created_at:OUTSCAN_ORDER_MONTHS.to_i.months.ago..DateTime.now).where(number: order_number).first
      response = {}
      if order.present? && order.notes.downcase.exclude?("removed tag")
        Event.where(eventable_id: order.id).each do |event|
          response[:dispatch_error] = event.notes if ["added tag dnd", "added tag dndos", "added tag dndcs"].any?{|notes| event.notes.downcase.include? notes}
        end
      end
      if order.present?
        if batch_number.match(/^ATLANTIC*/) && ((batch_number.include?('UK') && order.country.try(:downcase) != 'united kingdom') || (batch_number.exclude?('UK') && order.country.try(:downcase) == 'united kingdom'))
          response[:country_error] = 'true'
        elsif ['dispatched','partial_dispatch'].include?(order.state)
          response_order = PackageManagement.mark_package_out_scanned(order, tracking_number, DateTime.now(), false, current_account, batch_number)  
          if response_order.present?
            order = response_order[0]
            shipment = response_order[1]
            tracking_error = response_order[3]
            response[:error] = response_order[2]
            response[:tracking_error] =  tracking_error
            response[:order_number] = order.number
            response[:tracking_number] = shipment.try(:number).presence || "NOT PRESENT"
            response[:courier_company] = shipment.try(:shipper).try(:name).presence || "NOT PRESENT"
            response[:notes] = order.notes
            response[:out_of_mirraw_warehouse] = response_order[2].present? ? 'Error' : Time.now.try(:strftime,'%e %b %Y')
            response[:out_scan_notes] = order.out_scan_notes
          end          
        else
          response[:state_error] = 'true'
        end  
      else
        response[:not_found] = "true" 
      end  
      render :json => response
    end
  end

  def out_scan_report
    @integration_status = 'new'
    @outscan_start_date = params[:outscan_start_date].present? ? (DateTime.parse(params[:outscan_start_date]).to_date) : (DateTime.now).to_date
    @outscan_end_date = params[:outscan_end_date].present? ? (DateTime.parse(params[:outscan_end_date]).to_date)  : (DateTime.now).to_date
    @outscan_type = params[:outscan_type]
    case @outscan_type
      when "With Issue"
        outscan_condition = "out_scan_notes is not null and out_scan_notes <> '' and shipments.out_scan_date >='#{@outscan_start_date}' and shipments.out_scan_date <='#{@outscan_end_date}'"
      when "Without Issue"
        outscan_condition = "(out_scan_notes is null or out_scan_notes = '') and shipments.out_scan_date >='#{@outscan_start_date}' and shipments.out_scan_date <='#{@outscan_end_date}'"
      when 'Out Of Warehouse Issue'
        outscan_condition = "shipments.out_scan_date is null and out_scan_notes is not null and out_scan_notes <> '' and shipments.created_at > '#{3.months.ago}'"
      when "All"
        outscan_condition = "shipments.out_scan_date is not null and shipments.out_scan_date >= '#{@outscan_start_date}' and shipments.out_scan_date <= '#{@outscan_end_date}'"
      else
        outscan_condition = "shipments.out_scan_date >= '#{7.days.ago}'"
    end
    outscan_condition += " and orders.state IN ('sane','ready_for_dispatch','dispatched','partial_dispatch')"
    if params[:commit] == "Download Report"
      PackageManagement.sidekiq_delay(queue:'high')
                       .generate_outscan_report(
                         outscan_condition,current_account.email
                       )
      redirect_to (request.env["HTTP_REFERER"] || package_management_out_scan_report_path), notice: "Out Scan Report Mailed to #{current_account.email}" and return
    end
    @shipments = Shipment.select('shipments.id, order_id, orders.number as order_number, shipments.number as tracking_num, shippers.name as shipper_name, shipments.out_scan_date, orders.out_scan_notes, shipments.out_scan_batch').preload(order: :events).joins(:order, :shipper).forward.where(outscan_condition).where('designer_order_id is null').paginate(page: params[:page],per_page: 25)     
  end

  def create_out_scan_batch   
    render :json => [PackageManagement.get_out_scan_batch_number(params[:shipper], params[:batch_type])]
  end

  def download_outscan_batchwise_report    
    response = PackageManagement.sidekiq_delay(queue:'high').generate_outscan_report("shipments.out_scan_batch = '#{params[:batch_code]}'",current_account.email,params[:batch_code])
    redirect_to (request.env["HTTP_REFERER"] || package_management_out_scan_path ), notice: "#{params[:batch_code]} Out Scan Report Mailed to #{current_account.email}"
  end

  def assign_item_code(designer_order)
    set_item_code(designer_order)
  end

  #only when experts give rating @ design page it will be collected separately
  def build_rating
    if (design = Design.where(id: params[:design_id]).first).present?
      r = design.reviews.new(rating: params[:rating], designer_id: design.designer.id, system_user: true, assured: 'Expert')
      r.save
      r.design.update_reviews
      r.design.designer.update_reviews
      change_quality_level(design,params[:rating])
      response = {success: true}
    else
      response = {error: 'design not found'}
    end
    render :json => response
  end

  def tailoring_inscan
    if request.get?
      @integration_status = 'new'
      @tailors = Tailor.get_tailor(false)
    elsif request.post?
      is_new_tailoring_inscan = DISABLE_ADMIN_FUCTIONALITY['new_tailoring_inscan'] == 'true'
      is_warehouse            = params[:inscan_type] == 'Warehouse'
      total_products = params[:total_items]
      input_details, all_measurement_scans, line_item_scans, all_item_wise_data = {}, {}, {}, {}
      barcodes_data = {}
      all_tailor_products = []
      notice = 'Please Scan Atleast 1 Product.'
      (1..total_products.to_i).each do |num|
        input_id = "input_#{num}"
        if params[input_id].present? && (barcode_split = params[input_id].to_s.strip.split('-')).present? && (!is_warehouse || barcode_split.length == 5)
          #barcode_for_warehouse : W04-MIRWH743234468-83889-24-D1(rack-warehouse_order_number-design_id-warehouse_size_item_id-sequence)
          #barcode_for_order : M326718039-2847309-257247-35871136(order_number-design_id-measurement_id-line_item_id)
          if is_warehouse
            measurement_id = barcode_split[3].to_i
            (all_item_wise_data[measurement_id] ||= []) << "#{barcode_split[0]}-#{barcode_split[4]}"
            (barcodes_data[measurement_id] ||= []) << params[input_id]
          else
            measurement_id = barcode_split[2]
            all_tailor_products << measurement_id
            all_measurement_scans[measurement_id] = params[input_id]
            line_item_scans[measurement_id] = barcode_split[3]
            if is_new_tailoring_inscan
              if params[:inscan_type] != 'fnp'
                (all_item_wise_data[barcode_split[3].to_i] ||= []) << params[input_id]
              else
                (all_item_wise_data[barcode_split[1]] ||= []) << barcode_split[4]
                (barcodes_data["#{barcode_split[1]}-#{barcode_split[4]}"] ||= []) << params[input_id]
              end
            end
          end
        end
      end
      if (all_tailor_products.present? || is_warehouse) && params[:tailor_name].present?
        tailor_data = JSON.parse(params[:tailor_name])
        notice = 'Tailoring Inscan is scheduled.'
        if is_new_tailoring_inscan || is_warehouse
          PackageManagement.sidekiq_delay(queue:'high')
                           .generate_tailoring_inscan_bags(
                             tailor_data,
                             all_item_wise_data,
                             current_account,
                             params[:inscan_type],
                             barcodes_data
                           )
        else
          PackageManagement.sidekiq_delay(queue:'high')
                           .mark_csv_tailoring_inscan(
                             tailor_data,
                             all_tailor_products,
                             all_measurement_scans,
                             line_item_scans,
                             current_account
                           )
        end
      elsif params[:tailor_name].blank?
        notice = 'Please Select Tailor Name.'
      end
      redirect_to (request.env["HTTP_REFERER"]|| tailoring_inscan_path), notice: notice
    end  
  end

  def rtv_outscan
    if request.post?
      if params[:shipper_name].present? && params[:tracking_number].present?
        w_shipper_name_check = params[:shipper_name] == 'Other' ? "shipper_name NOT IN ('Rapid Delivery','Delhivery', 'Xpress Bees')" : "Shipper_name = '#{params[:shipper_name]}'"
        if (rtv_shipment = RtvShipment.preload(designer_orders: :payment_order).where(number: params[:tracking_number]).where(w_shipper_name_check).where('out_of_mirraw_warehouse IS NULL').first).present?
          rtv_shipment.out_of_mirraw_warehouse = DateTime.now
          if rtv_shipment.save
            designer_order = rtv_shipment.designer_orders.last
            order = designer_order.payment_order
            designer_order.add_notes_without_callback('RTV Out Of Mirraw Warehouse', 'rtv', current_account)
            rtv_items = rtv_shipment.line_items
            product_ids = rtv_items.collect(&:design_id).join(', ')
            designer = designer_order.designer
            if rtv_items.collect(&:qc_status).compact.any? {|status| !status}
              DesignerMailer.sidekiq_delay(queue:'high')
                            .send_rtv_product_dispatched_notification(
                              rtv_shipment, 
                              designer, 
                              order.number,
                              product_ids
                            )
              message = "Your QC failed product #{product_ids.match(/\,/) ? 'ids' : 'id'}: #{product_ids} from order: #{order.number} has been dispatched to you via #{rtv_shipment.shipper_name}. You can track the same #{rtv_shipment.number} tracking number"
              Message.create(title:'RTV Dispatched', description: message , account_id: designer.account.id, priority: 1)
            end
            LineItem.bulk_add_into_scan('LineItem', rtv_items.collect(&:id), 'RTV Outscan', current_account.id)
          end 
          response = {status: "Rtv Out Scan of #{params[:tracking_number]}-#{params[:shipper_name]} successful."}
        else
          response = {error: true, status: 'Tracking number and shipper name combination not found or already scanned.'}
        end
      else
        response = {error: 'Shipper name or tracking number missing.'}
      end
      render json: response
    end
  end

  def rtv_outscan_report
    @start_date, @end_date = (params[:start_date].present? && params[:end_date].present?) ? get_date() : [Date.today, Date.today]
    w_shipper_name_check = params[:shipper_name].present? ? (params[:shipper_name] == 'Other' ? "shipper_name NOT IN ('Rapid Delivery','Delhivery', 'Xpress Bees')" : "Shipper_name = '#{params[:shipper_name]}'") : ''
    if params[:commit] != 'Mail Report'
      @rtv_outscan_data = RtvShipment.preload(designer_orders: :payment_order).where(w_shipper_name_check).where('out_of_mirraw_warehouse between ? and ?', @start_date.beginning_of_day, @end_date.end_of_day).paginate(page: params[:page], per_page: 30)
    else
      PackageManagement.sidekiq_delay
                       .mail_rtv_outscan_report(
                         @start_date, @end_date,
                         params[:shipper_name], current_account.email
                       )
      redirect_to rtv_outscan_report_path(start_date: {year: @start_date.year, month: @start_date.month, day: @start_date.day}, end_date: {year: @end_date.year, month: @end_date.month, day: @end_date.day}, shipper_name: params[:shipper_name]), notice: "Report Will Be Mailed to #{current_account.email}."
    end
  end

  def handover_to_stitching
    if request.post?
      notice = 'Please Scan Atleast 1 Product.'
      input_details, total_products_hash, receive_pending_products = get_handover_scanned_data(params[:total_items], (params[:receiving] != 'yes' || (params[:scan_type] == 'receive' || (params[:scan_type] == 'assign' && params[:handover_for] == 'FNP'))))
      if input_details.present? || receive_pending_products.present?
        handover_for_param = (params[:scan_type] == 'stylist_receive' ? params[:receive_handover_type] : params[:handover_for])
        PackageManagement.sidekiq_delay(queue:'high').mark_handover_to_stitching(input_details, total_products_hash, current_account, params[:receiving] == 'yes', params[:handover_to], params[:scan_type], handover_for_param, receive_pending_products)
        notice = "Work In Progress. Report of the Same Will Be Mailed To #{current_account.email}"
      end
      redirect_to (request.env["HTTP_REFERER"] || handover_to_stitching_path(receiving: params[:receiving])), notice: notice    
    end
  end

  def inward_bag_generation
    if request.post?
      total_products = params[:total_items]
      tracking_numbers, designer_order_ids = {}, []
      notice = 'Please Scan Atleast 1 Product.'
      (1..total_products.to_i).each do |num| 
        input_id = "input_#{num}"
        input_val = get_input_val(input_id)
        if input_val.present?
          if params[input_id].to_s.include?('|-|')
            designer_order_ids << [input_val, params[input_id]]
          elsif (input_val = (params[input_id].try(:strip))).present? && input_val.length >= DISABLE_ADMIN_FUCTIONALITY['min_tracking_number_length'].to_i
            (tracking_numbers['valid'] ||= []) << input_val
          elsif input_val.present? && input_val.length < DISABLE_ADMIN_FUCTIONALITY['min_tracking_number_length'].to_i
            (tracking_numbers['invalid'] ||= []) << input_val
          end
        end
      end
      if (tracking_numbers.present? || designer_order_ids.present?)
        PackageManagement.sidekiq_delay(queue: 'high').generate_inward_bag(tracking_numbers, current_account, nil, nil, designer_order_ids)
        notice = "Bag Generation Is In Progress.Report of the Same Will Be Mailed To #{current_account.email}"
      end
      redirect_to (request.env["HTTP_REFERER"] || package_management_index_in_scan_path), notice: notice
    end
  end


  def rto_inward_bag_generation
    if request.post?
      total_products = params[:total_items]
      tracking_numbers = {}
      notice = 'Please Scan Atleast 1 Product.'
      (1..total_products.to_i).each do |num|
        input_id = "input_#{num}"
        input_val = get_input_val(input_id)
        input_val = input_val.try(:strip)
        if input_val.present?
          if input_val.length < DISABLE_ADMIN_FUCTIONALITY['min_tracking_number_length'].to_i
            (tracking_numbers['invalid'] ||= []) << input_val
          else
            (tracking_numbers['valid'] ||= []) << input_val
          end
        end
      end
      if tracking_numbers.present?
        PackageManagement.sidekiq_delay(queue: 'critical')
                         .rto_inscan(tracking_numbers, current_account)
        notice = "In Progress for #{tracking_numbers['valid'].count} tracking numbers and will be mailed to #{current_account.email}"
      end
      redirect_to (request.env["HTTP_REFERER"] || rto_inscan_panel_path), notice: notice
    end
  end

  def rto_panel
    all_line_items = LineItem.includes(:designer_order, :order, :warehouse_line_item_joins, :inward_detail, :return_designer_order).where("designer_orders.confirmed_at  >= ?",90.to_i.month.ago).where(available_in_warehouse:true).where("designer_orders.state = 'rto' OR status = 'buyer_return'").references(:designer_order, :return_designer_order).order('designer_orders.confirmed_at DESC')

    if params[:commit] == 'Search'
      where_clause, tracking_number = '', ''
      if params[:line_item_id].present?
        line_item_id = params[:line_item_id]
        where_clause = "line_items.id = #{line_item_id}"
      elsif params[:order_number].present?
        order_number = params[:order_number]
        where_clause = "orders.number = '#{order_number.upcase}'"
      elsif params[:tracking_number].present?
        tracking_number = params[:tracking_number]
        tracking_number.downcase!
        where_clause = "LOWER(designer_orders.tracking_num) = '#{tracking_number}' or LOWER(designer_orders.recent_tracking_number) = '#{tracking_number}' or LOWER(return_designer_orders.tracking_number) = '#{tracking_number}'"
      elsif  params[:design_id].present?
        design_id = params[:design_id]
        where_clause = "line_items.design_id = #{design_id}"
      end
      @line_items = all_line_items.where(where_clause)
      if @line_items.blank? && tracking_number.present?
        where_clause = "LOWER(designer_orders.tracking_num) like '%#{tracking_number}%' or LOWER(designer_orders.recent_tracking_number) like '%#{tracking_number}%' or LOWER(return_designer_orders.tracking_number) like '%#{tracking_number}%'"
        @line_items = all_line_items.where(where_clause)
      end
    else
      @line_items = all_line_items
    end
    @line_items = @line_items.paginate(page: params[:page], per_page: 20)
  end

  def bulk_shipment_bag_generation
    if request.post?
      notice = 'Please Provide all the details.'
      if params[:bulk_awb_number].present? && params[:po_number].present?
         purchase_order = PurchaseOrder.preload(:designer_orders).where(number: params[:po_number]).first
        if purchase_order.present? && (des_ords = purchase_order.designer_orders).present?
          if purchase_order.awb_number.try(:downcase) == params[:bulk_awb_number].downcase || des_ords.map(&:recent_tracking_number).include?(params[:bulk_awb_number])
            PackageManagement.sidekiq_delay(queue: 'high').generate_inward_bag({'valid' => [purchase_order.awb_number]}, current_account, purchase_order.id, params[:invoice_qty].to_i)
            notice = "Bag Generation Is In Progress.Report of the Same Will Be Mailed To #{current_account.email}"
          else
            notice = 'Tracking Number and Purchase Order Number Mismatch.'
          end
        else
          notice = 'Purchase Order or Bulk Vendor Orders Not Found.'
        end
      end
      redirect_to (request.env["HTTP_REFERER"] || package_management_index_in_scan_path), notice: notice
    end
  end

  def stitching_searching_list
    @stylists = Stylist.pluck(:name, :id)
    @start_date, @end_date = get_date('month')
    check_date = ((@start_date.to_date + 3.month) >= @end_date.to_date) && (@start_date <= @end_date)
    w_stylist_search = ''
    w_stylist_search = 'orders.stylist_id = ?', params[:stylist_id].to_i if params[:stylist_id].present?
    searching_type = (params[:searching_type].presence || 'stitching')
    if params[:commit] == 'Mail CSV' && check_date
      PackageManagement.sidekiq_delay(queue: 'critical')
                       .mail_stitching_searching_data(
                         w_stylist_search,
                         @start_date,
                         @end_date,
                         current_account.email,
                         searching_type
                       )
      @notice = 'Mail csv in process'
      params[:commit] = 'GET DATA'
      redirect_to stitching_searching_list_path(params), notice: @notice
    elsif params[:commit] == 'GET DATA'
      @stitching_search_data = PackageManagement.stitching_searching_data(w_stylist_search, @start_date, @end_date, searching_type).paginate(page: params[:page], per_page: 20)  
    end
    @notice = 'Please Select Date Difference of 3 month or less for mail CSV' if !check_date && params[:commit] == 'Mail CSV'    
  end

  def post_tailoring_inscan_scans
    if request.post?
      notice = 'Please Scan Atleast 1 Product.'
      input_details, total_barcode_scanned = {}, {}
      is_rack_barcodes = (params[:handover_need] == 'working' && params[:hand_to] == 'stylist')
      if params[:hand_to] == 'stylist' && params[:csv_file].present? && (csv_data = CSV.read(params[:csv_file].path)).length <= 200
        csv_data.each do |data|
          barcode_split = data[0].to_s.strip.split('-')
          if is_rack_barcodes
            (input_details[barcode_split[1]] ||= []) << barcode_split[4]
            (total_barcode_scanned["#{barcode_split[1]}-#{barcode_split[4]}"] ||= []) << data[0]
          else
            (input_details[barcode_split[0]] ||= []) << barcode_split[1]            
            (total_barcode_scanned["#{barcode_split[0]}-#{barcode_split[1]}"] ||= []) << data[0]
          end
        end
      else
        input_details, total_barcode_scanned, receive_pending_products = get_handover_scanned_data(params[:total_items], is_rack_barcodes)
      end
      if input_details.present?
        PackageManagement.sidekiq_delay(queue:'high')
                         .mark_post_tailoring_scans(
                           input_details,
                           total_barcode_scanned,
                           current_account,
                           params[:hand_to],
                           params[:handover_need],
                           params[:stylist_id]
                         )
        notice = "Work In Progress. Report of the Same Will Be Mailed To #{current_account.email}"
      end
      redirect_to (request.env["HTTP_REFERER"] || post_tailoring_inscan_scans_path), notice: notice    
    else
      @stylists = Stylist.pluck(:name, :id)
    end
  end

  def stitching_done_panel
  end

  def quality_repairable_products
    @repairable_products = LineItem.preload(order: :events, quality_check_values: :quality_check).joins(:quality_check_values).where('qc_status = ? and issue_status = ? and repairable = ? and fabric_measured_on is null and stitching_sent_on is null', false, 'Y', true).uniq.paginate(page: params[:page], per_page: 25)
  end

  def orders_followup
    is_mail_req = (['Mail Data', 'Mail Status'].include?(params[:commit]))
    preload_array = [:tags, :delivery_nps_info, line_items: [:designer_order, :stitching_measurements, :ipending_related_scans, tailoring_info: [:tailoring_inscan_bags]]]
    if request.get?
      @start_date, @end_date = get_date('month')
      followup_where_claues = PackageManagement.get_where_clauses_for_followup_orders(params, @start_date, @end_date)
      if is_mail_req
        PackageManagement.sidekiq_delay
                         .mail_followup_orders(
                           followup_where_claues, params[:team_select],
                           params[:process_select], preload_array,
                           current_account.email
                         )
        redirect_to orders_followup_path, notice: 'Mail Will Be Sent To You Shortly.'
      else
        @orders = PackageManagement.get_followup_orders(followup_where_claues, preload_array).paginate(page: params[:page], per_page: 30)
      end
    elsif request.post? && params[:csv_file].present? && is_mail_req
      filename  = 'packages/orders_followup/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
      file = AwsOperations.create_aws_file(filename, params[:csv_file])      
      PackageManagement.sidekiq_delay.mail_csv_uploaded_orders_status(
        filename, preload_array, current_account.email
      )
      redirect_to orders_followup_path, notice: 'Mail Will Be Sent To You Shortly.'    
    end    
  end

  def update_repair_status
    response = {error: true, message: 'Wrong Details Provided.'}
    if request.post? && params[:repair_status].present? && params[:item_id].present? && (line_item = LineItem.find_by_id(params[:item_id]))
      if params[:repair_status] == 'Yes'
        line_item.mark_qc(true, current_account)
        line_item.create_issue_for_item(line_item.order, current_account, 'true', 'Resolve By Repair', '', nil, '')
        scan_name = 'Product Repaired'
        note_msg = "#{line_item.design_id} : QC Passed After Repair"
      else
        line_item.quality_check_values.where(repairable: true).update_all(repairable: false)
        scan_name = 'Product Marked Not Repairable'
        note_msg = "#{line_item.design_id} : Not Repairable"
      end
      add_notes([line_item], note_msg, false)
      line_item.add_into_scan(scan_name, current_account.id)
      response = {error: false, message: 'Success'}
    end
    render json: response
  end

  def process_pending_panels
    if params[:process_name].present? && (process_query = get_process_pending_query(params[:process_name]))
      @paginated_process_data = process_query.paginate(page: params[:page], per_page: 10)
      @display_data = get_displayable_data(@paginated_process_data)
    end
  end

    
  def post_packaging_scan
    if request.post?
      line_item_ids = params[:line_item_ids].split(',')
      LineItem.where(id: line_item_ids).update_all(packer_id: current_account.id)
      LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Packaging Done', current_account.id)
      flash[:success] = "Packaging Scan Done."
      redirect_to (request.env["HTTP_REFERER"] || post_packaging_scan_path) and return
    end
  end

  def get_order_buckets
    response = {errors: true, error_message: 'Unable to find Order. Please Scan Again', bucket_ids: [], order_id: nil}
    if params[:order_number].present?
      order_number  =  params[:order_number]
      order = Order.preload(not_shipped_invoiced_buckets: :line_items).where(number: order_number).first
      if order.present?
        shipment_buckets = order.not_shipped_invoiced_buckets
        bucket_ids = []
        shipment_buckets.each do |shipment_bucket|
          bucket_ids << shipment_bucket.id if shipment_bucket.line_items.first.packer_id.blank?
        end
        response = {errors: false, error_message:'', bucket_ids: bucket_ids, order_id: order.id}
      end
    end
    render json: response
  end

  def get_line_items
    response = {errors: true, error_message: 'Unable to find Line Items or Order Already Scanned', line_items_details: {} }
    if params[:id_for_line_item].present?
      if params[:which_id] == 'shipment_bucket_id'
        line_items = LineItem.preload(:design, :designer_order).where(shipment_bucket_id: params[:id_for_line_item])
      elsif params[:which_id] == 'order_id'
        line_items = LineItem.preload(:designer_order, :design).joins(:designer_order).where('designer_orders.order_id = ?', params[:id_for_line_item]).where(shipment_bucket_id: nil)
      end
      line_items_details = get_line_items_details_hash(line_items)
      response = {errors: false, error_message: '', line_items_details: line_items_details} if line_items_details.present?
    end
    render json: response
  end

  def mark_rto_sor_line_item_received
    response = {errors: true, message: 'Unable to Mark Receive' }
    if params[:line_item_id].present?
      line_item = LineItem.find_by_id(params[:line_item_id])
      if line_item.present? && (li_inward_detail = line_item.inward_detail).present?
        li_inward_detail.update_columns(mark_received: true, mark_received_by: current_account.id)
        response = {errors: false, message: 'Successfully Marked Received' } 
      else
        response = {errors: true, message: 'Unable to find line item or inward details' }
      end
    end
    render json: response
  end

  def mark_rto_sor_line_item_qc_done
    response = {errors: true, message: 'Unable to Do QC'}
    if params[:line_item_id].present?
      line_item = LineItem.find_by_id(params[:line_item_id])
      if line_item.present? && (li_inward_detail = line_item.inward_detail).present? && li_inward_detail.mark_received
        update_hash = {qc_done: true}
        update_hash[:qc_fail_reason] = params[:qc_fail_reason] if params[:qc_action].to_s == 'fail'
        li_inward_detail.update_columns(update_hash)
        response = {errors: false, message: 'QC Done Successfully',action: params[:qc_action]}
      else
        response = {errors: true, message: 'Unable to find line item or inward details' }
      end
    end
    render json: response
  end

  def assign_rack_rto_sor_line_item
    response = {errors: true, message: 'Unable to assign rack code' }
    if params[:line_item_id].present?
      line_item = LineItem.find_by_id(params[:line_item_id])
      if line_item.present? && (li_inward_detail = line_item.inward_detail).present?
        if li_inward_detail.mark_received
          if li_inward_detail.qc_done && li_inward_detail.qc_fail_reason.blank?
            if (rlwli = line_item.warehouse_line_item_joins.try(:last).try(:rack_lists_warehouse_line_item)).present? && (items = rlwli.try(:item)).present?
              WarehouseLineItem.sidekiq_delay(queue: 'critical')
                               .create_new_rack_list(
                                 line_item,
                                 rlwli,
                                 items,
                                 current_account
                               )
              response = {errors: false, message: 'will be updated in sometime...' }
            else
              response = {errors: true, message: 'RackList or Warehose Line Item does not exist' }
            end
          elsif li_inward_detail.qc_done && li_inward_detail.qc_fail_reason.present?
            if (rack_list = RackList.where(description:"bad_sor_inventory_rack").first).present?
              update_hash = {rack_assigned: true, bad_rack_list_id: rack_list.id, rack_assigned_by: current_account.id, rack_assigned_notes: rack_list.code}
              li_inward_detail.update_columns(update_hash)
              response = {errors: false, message: 'updated' }
            else
              response = {errors: true, message: 'Rack with bad sor inventory tag does not exists' }
            end
          else
            response = {errors: true, message: 'QC is Pending' }
          end
        else
         response = {errors: true, message: 'Line Item not yet marked received' }
        end
      else
        response = {errors: true, message: 'Unable to find line item or inward details' }
      end
    end
    render json: response
  end

  def update_picking_config
    if request.get?
      @picking_config = SystemConstant.get_picking_worker_config
    elsif request.post?
      current_picking_config = SystemConstant.get_picking_worker_config
      current_picking_config.map do |process, workers|
        param_key_name = "#{process}_worker_count".to_sym
        current_picking_config[process] = params[param_key_name].to_i
      end
      SystemConstant.where(name: 'PICKING_WORKERS_CONFIG').update_all(value: current_picking_config.to_json)
      Rails.cache.delete('picking_worker_config')
      PackageManagement.sidekiq_delay.update_searcher_buckets
      redirect_to picking_config_path, notice: 'Picking Config Successfully Updated.'
    end
  end

  private
  def get_line_items_details_hash(line_items)
    line_items.each_with_object({}) do |line_item, memo|
      if line_item.check_items_done_at.present? && line_item.packer_id.blank? && (line_item.status.blank? || ['canceled', 'vendor_canceled'].exclude?(line_item.designer_order.state))
        memo[line_item.id] = {'image_url' =>line_item.thumbnail_image, 'design_id' => line_item.design_id , 'quantity' => line_item.quantity, 'title' => line_item.title}
      end
    end
  end
  
  def get_displayable_data(process_data)
    process_data.map(&:attributes).each{|data| data.delete('id')}
  end

  def get_process_pending_query(process)
    select_clause = get_select_clause_as_per_process(process)
    case process     
    when 'Stitching Done Pending'
      LineItem.select(select_clause).
      joins(:tailoring_info, :order).
      where('stitching_required = ? and status is null and designer_orders.state not in (?) 
        and orders.state in (?) and orders.confirmed_at > ?', 
        'Y', ['canceled', 'vendor_canceled'], ['sane', 'partial_dispatch'], 3.month.ago).
      group('line_items.id, orders.id, designer_orders.id').
      having('array_position(array_agg(material_received_status), NULL) is null')

    when 'RTV Outscan Pending'  
      RtvShipment.select(select_clause).
      joins(designer_orders: :order).
      where('rtv_shipments.shipment_type = ? and rtv_shipments.created_at > ? 
        and rtv_shipments.out_of_mirraw_warehouse is null', 'rtv', 1.month.ago).group(:id)
    end
  end

  def get_select_clause_as_per_process(process)
    case process
    when 'Stitching Done Pending'
      'line_items.design_id, orders.number as order_number, orders.confirmed_at, orders.state as order_state, 
        designer_orders.state as designer_order_state'

    when 'RTV Outscan Pending'
      "string_agg(distinct orders.number, ',') as order_number,
        string_agg(distinct orders.confirmed_at::text, ',') as confirmed_at, 
        string_agg(distinct designer_orders.designer_id::text, ',') as designer_id, 
        rtv_shipments.number as AWB, rtv_shipments.shipper_name, 
        rtv_shipments.created_at as rtv_created_at"
    end
  end
  #hide menu to avoid unecessary query
  def hide_menu
    @hide_menu = true
  end

  def get_input_val(input_id)
    (split_data = params[input_id].to_s.strip.split('|-|'))[1].presence || split_data[0]
  end

  def get_handover_scanned_data(total_products, rack_barcode_input=false)
    input_details, total_products_hash, pending_for_receiving = {}, {}, []
    receive_pending = (params[:scan_type] == 'assign' ? params[:receive_pending].to_s.split(',') : [])
    (1..total_products.to_i).each do |num| 
      input_id = "input_#{num}"
      if params[input_id].present?
        if receive_pending.exclude?(input_id)
          barcode_split = params[input_id].to_s.strip.split('-')
          if rack_barcode_input
            (input_details[barcode_split[1]] ||= []) << barcode_split[4]
            (total_products_hash["#{barcode_split[1]}-#{barcode_split[4]}"] ||= []) << params[input_id]
          else
            (input_details[barcode_split[0]] ||= []) << barcode_split[1]            
            (total_products_hash["#{barcode_split[0]}-#{barcode_split[1]}"] ||= []) << params[input_id]
          end
        else
          pending_for_receiving << params[input_id]
        end
      end
    end
    return input_details, total_products_hash, pending_for_receiving
  end

  ## Once the Ratings are done this method adds notes to order and designer_order
  def add_notes(items, issue_note_msg, qc_note=true)
    ## << Order notes add Start...>>
    text = qc_note ? "QC of item#: #{items.first.design.id} failed" : ''
    text += issue_note_msg if issue_note_msg.present?
    item = items.first
    order = item.designer_order.order
    order.add_notes_without_callback(text, 'other', current_account)
    ## << Order Notes add End .. >>

    ## << DesignerOrder notes add Start...>>
    designer_order = item.designer_order
    designer_order.add_notes_without_callback(text, 'other', current_account)
    ## << DesignerOrder notes add End...>>
  end

  def store_quality_issue_data(item_id, issue_id, issue_reason, repairable, issue_name)
  QualityCheckValue.where(line_item_id: item_id, quality_check_id: issue_id, qc_reason: issue_reason.presence, repairable: (repairable == 'yes')).first_or_create
    other_reason = issue_reason.present? ? "(#{issue_reason})," : ''
    ", #{issue_name} -> #{other_reason} Repairable: #{repairable}"
  end

  def raise_designer_issue(items, issue_type)
    item, design = items.first, items.first.design
    d = design.designer_issues.new(issue_type: issue_type, designer_id: design.designer_id, order_id: item.designer_order.order.id, design_snapshot_price: item.snapshot_price(RETURN_NORMAL), line_item_id: item.id, designer_order_id: item.designer_order.id)
    d.save!
    item.update_column(:claim_flag,true)
  end

  def set_item_code(designer_order)
    if designer_order.rack_code.present?
      return designer_order.rack_code
    else
      item = designer_order.line_items.first
      initial = item.design.get_product_type_initial
      record = RackConfig.first

      if initial == 'J-'
        last_count_jewellery = record.last_count_jewellery
        last_series_jewellery = record.last_series_jewellery
        if last_count_jewellery > 9999
          last_series_jewellery = last_series_jewellery.next
          last_count_jewellery = 0
        end
        new_rack_code = initial + last_series_jewellery + last_count_jewellery.to_s
        record.last_count_jewellery = last_count_jewellery + 1
        record.last_series_jewellery = last_series_jewellery
      else
        last_count_apparel = record.last_count_apparel
        last_series_apparel = record.last_series_apparel
        if last_count_apparel > 9999
          last_series_apparel = last_series_apparel.next
          last_count_apparel = 0
        end
        new_rack_code = initial + last_series_apparel + last_count_apparel.to_s
        record.last_count_apparel = last_count_apparel + 1
        record.last_series_apparel = last_series_apparel
      end
      designer_order.update_column(:rack_code,new_rack_code)
      record.save
    end
  end


  def list(detail = false, period = nil, get_packages = 'all')
    # Building Basic Query
    # Designer Order
    s_details   = 'designer_orders.id, designers.name as designer_name, designers.id as designer_id, orders.number as order_number, designer_orders.package_received_on, designer_orders.package_received_by, designer_orders.tracking_num, designer_orders.tracking_partner, designer_orders.package_status'
    s_days_past = 'age(current_date, DATE(designer_orders.pickup)) AS transit_time'
    w_country   = 'lower(orders.country) <> ?', 'india'
    s_designer_details = 'designers.name as designer_name, designers.id as designer_id'
    if period.present?
      w_date = 'designer_orders.package_received_on BETWEEN ? AND ?', period[:start_date], period[:end_date]
      s_count = "COUNT(designer_orders.id) as packages_received, SUM(CASE WHEN designer_orders.package_status = 'packed' THEN 0 ELSE 1 END) as packages_unpacked"
      designer_orders = DesignerOrder.unscoped.joins(:order, :designer).preload(:line_items).package_states.where(w_date).where(w_country)
      @total_and_unpacked_count = designer_orders.select(s_count).first
      total_count = @total_and_unpacked_count[:packages_received]
      designer_orders = designer_orders.select(s_details).select(s_days_past)
    else
      designer_orders = DesignerOrder.select(s_details).select(s_days_past).joins(:order, :designer).preload(:line_items).package_states.where(w_country)   
      designers = DesignerOrder.unscoped.select(s_designer_details).joins(:order, :designer).package_states.where(w_country)
    end
    
    #Appending search options
    if (s = params[:q]).present?
      s               = s.downcase
      w_search        = 'LOWER(designer_orders.tracking_num) = ? or LOWER(orders.number) = ? or LOWER(designers.name) = ?', s,s,s
      designer_orders = designer_orders.where(w_search)
      designers       = designers.where(w_search)
    end

    # Appending designer id
    if (designer_id   = params[:designer_id]).present? and params[:designer_id] != 'All'
      w_designer      = 'designer_orders.designer_id = ?', designer_id
      designer_orders = designer_orders.where(w_designer)
    end

    # Appending package option
    case get_packages
    when 'all'
      # Do Nothing
    when 'nt'
      designer_orders = designer_orders.package_not_received
      designers       = designers.package_not_received
    when 're'
      designer_orders = designer_orders.package_received
      designers       = designers.package_received
    end

    unless period.present?
      designers = designers.group('designers.id', 'designers.name').order('designers.name').collect{|d| [d[:designer_name], d[:designer_id]]}.prepend(['All','All'])
    end
    # Paginating designer order
    designer_orders = designer_orders.order('transit_time DESC').paginate(:page => params[:page],:per_page => 100, total_entries: total_count)
    
    return designer_orders, designers
  end

  def check_package(designer_order)
    unless designer_order.line_items.where('(line_items.received <> ? or line_items.received IS NULL or qc_done_on is null) and line_items.status is null and (line_items.available_in_warehouse is null or line_items.available_in_warehouse = ?) ','Y', false).exists?
      designer_order.can_got_awb? ? (designer_order.package_status = 'unpacked'; designer_order.got_awb!) : designer_order.update_column(:package_status, 'unpacked')
      if (bag_id = designer_order.inward_bag_id).present?
        update_bag_status(bag_id)
      end
      if (p_order = designer_order.purchase_order).present?
        update_purchase_order_status(p_order) 
      end
    end
  end

  def update_bag_status(bag_id)
    #DesignerOrder -> No index on inward_bag_id (Seq Scan)
    if (DesignerOrder.where(inward_bag_id: bag_id).pluck(:package_status) & ['packed', 'inscanned']).blank?
      InwardBag.where(id: bag_id).update_all(status: 'close')
    end
  end

  def update_purchase_order_status(p_order)
    des_orders = p_order.designer_orders
    if !des_orders.any?{|d_o| ['unpacked', 'qc passed replacement'].exclude?(d_o.package_status.try(:downcase))} && !des_orders.select{|d_o| d_o.package_status == 'unpacked'}.map(&:line_items).flatten.any?{|item| !item.qc_status} 
      p_order.update_column(:status, 'close')
    end
  end

  def update_bulk_scan_input_params      
    LineItem.update_scanned_input_param(params)
  end
end
  
