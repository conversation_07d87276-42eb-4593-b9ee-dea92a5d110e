class ApiController < ApplicationController
  def list
    list  = Hash.new
    begin
      model = params[:model].downcase
      case model
      when 'category'
        list = Category.designer_category_list(params[:designer])
      when 'child_categories'
        list = []
        JSON.parse(params[:category_name]).each do |c_name|
          list.push(*Category.getids(c_name))
        end
        list = [list].flatten
      when 'property_value'
        list = Property.get_property_values_list
      when 'transfer_model'
        list = Designer.get_transfer_model_vendors
      when 'option_type_value'
        list = OptionTypeValue.get_option_type_values_list
      when 'option_type'
        OptionType.get_option_types_list
      when 'property'
        list = Property.get_properties_list
      when 'master_addon_designer'
        list = MasterAddon.get_designer_master_addons_list(params[:designer])
      when 'master_addon_option_designer'
        list = MasterAddonOptionType.get_designer_maot_list(params[:designer])
      end
    rescue => error
      list = {:error => error}
    end
    render :json => list
  end

  def cod
    begin
      cart_id = params[:cart_id]
      pincode = params[:pincode]
      designer_ids = Cart.select('designers.id').joins(:line_items => [:design => :designer]).where(:id => params[:cart_id])
      designers = Designer.where(:id => designer_ids)
      response  = {:cod => 'true'}
      designers.each do |designer|
        if designer.cod
          shipper_ids = designer.allowed_shipper_ids('cod')
          unless Courier.shippable?(shipper_ids, designer.pincode, pincode, true)
            response[:cod] = 'false'
            break
          end
        else
          response[:cod] = 'false'
          break
        end
      end
    rescue => error
      response = {:error => error.message, :cod => 'false'}
    end
    render :json => response
  end

  def international_min_cart_value
    cart_id = params[:cart_id]
    cart = Cart.where(id: cart_id).preload(line_items: [:variant,[design: :designer],[line_item_addons: [addon_type_value: :addon_option_types]]]).first
    total_price = cart.try(:items_total_price) - cart.additional_discounts
    if total_price.to_i < INTERNATIONAL_MIN_CART_VAL.to_i
      min_val = @symbol+' '+(CurrencyConvert.convert_to(@symbol,INTERNATIONAL_MIN_CART_VAL.to_i,@country_code).round(2)).to_s 
      response = {:error => true, :min_val => min_val}
    else
      response = {:error => false}
    end
    render :json => response
  end

  def convert_to_cod
    response = Hash.new
    begin
      order_id = params[:order_id]
      order = Order.find_by_id(order_id)
      if order.pay_type != COD
        total_cod_charges = Order.post_domestic_cod_charge(order.pincode, order.designer_orders.where('designer_orders.state <> ?', 'canceled'))
        if (total_cod_charges && response[:cod] = order.convert_to_cod(current_account,total_cod_charges))
          response[:order_number] = order.number
        end
      else
        response[:error] = 'Order pay type is already COD'
      end
    rescue => error
      response = {:error => error.message, :cod => false}
    end
    render :json => response
  end

  def fetch_pdd_from_cp
    response = {error: true, error_message: 'Error while fetching PDD', eta: 0}
    begin
      pickup_pincodes = params[:p_pincodes]
      drop_pincode = params[:d_pincodes]
      click_post = ClickPostAutomation.new()
      promise_days = click_post.get_eta_from_clickpost(pickup_pincodes, [drop_pincode]).to_i
      response = {error: false, error_message: '', eta: promise_days} if promise_days > 0
    rescue => error
      response = {error: true, error_message: error.message, eta: 0}
      Order.sidekiq_delay.notify_exceptions('fetch_pdd_from_cp error', 'error while pdd fetch', { error: error.inspect })
    end
  end

  def fetch_pdd_from_lane
    response = {error: true, eta: 0}
    begin
      delivery_city = params[:to_location]
      vendor_pickup_locations = params[:from_location]
      lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(delivery_city.downcase, vendor_pickup_locations)
      if for_all_vendor_pickup_locations && lane_max_eta > 0
        response = {error: false, eta: lane_max_eta}
      end
      render json: response
    rescue => error
      render json: response
    end
  end

  def cod_disable_lockdown_pincodes(pincode)
    pincode_to_int = pincode.to_i
    return !LOCKDOWN_COD_DISABLE_PINCODES.include?(pincode_to_int)
  end

  def cod_cbd
    begin
      cart_id = params[:cart_id]
      pincode = params[:pincode]
      cart = Cart.where(id: cart_id).first
      domestic_cod_charge = Order.domestic_cod_charge(pincode, cart)
      total_price_currency = cart.total_price_currency
      courier_detail = Courier.check_cod(cart_id, pincode,true)
      designer_shippable_check = courier_detail[0]
      unless designer_shippable_check
        error_code_cod = 3 if domestic_cod_charge.present?
        designs_id_unshippable = cart.designs.where(designer_id: courier_detail[1]).pluck :id
      end
      cod_available = (cart.mirraw_payable_addons? == false && designer_shippable_check) && domestic_cod_charge && cod_disable_lockdown_pincodes(pincode) ? 1 : 0
      is_cod_available = (cod_available == 1)
      cbd_value = Courier.cbd_available?(pincode) ? 1 : 0
      cod_charge_req = is_cod_available ? domestic_cod_charge : 0
      cod_charge = CurrencyConvert.convert_to(@symbol, domestic_cod_charge, @country_code).round(2)
      shipping_charge = Country.shipping_cost_for(nil,'India',total_price_currency, cart.line_items)
      total_cod_charge = cod_available != 0 ? "#{@symbol} #{cod_charge}" : 0
      if COD_REQ_ORDER_ENABLE == 'true'
        designer_orders_total = Order.designer_orders_count(cart)
        CodRequest.where(cart_id: cart_id, pincode: pincode.to_s, order_total: total_price_currency).first_or_create(ip: request.remote_ip, app_source: 'Desktop', total_cod: cod_charge_req, is_cod_available: is_cod_available, line_items_count: cart.line_items.count, designer_order_count: designer_orders_total)
      end
      response = {cod_c: total_cod_charge, cod_charge: cod_charge, cod: cod_available, cbd: cbd_value, cart_value: CurrencyConvert.convert_to(@symbol,cart.total_price_currency(1, :referral),@country_code).round(2), min_cart_value: CurrencyConvert.convert_to(@symbol,SystemConstant.get("MIN_CART_VAL_FOR_COD").to_i,@country_code).round(2), symbol: @symbol , error_cod: error_code_cod, designer_unshippable: designs_id_unshippable, shipping_charge: shipping_charge}
    rescue => error
      response = {error: error.message, cod: 0, cbd: 0, cart_value: 0, min_cart_value: 0, symbol: ''}
      Order.sidekiq_delay.notify_exceptions('cod_cbd error', 'error while cod check', { error: error.inspect })
    end
    render json: response
  end

  def pincode_info
    begin
      pincode = params[:pincode]
      pin = Pincode.find_by_pin_code(pincode)
      city_fetch = pin.city
      dist_fetch = pin.district
      state_fetch = pin.state
      response = { fCity: city_fetch, fDistrict: dist_fetch, fState: state_fetch }
    rescue => error
      response = {error: error.message, fCity: "", fDistrict: "", fState: ""}
    end
     render json: response
  end

  def get_design_weight
    data = {error: 'Details Wrong.'}
    begin
      if params[:searchable_id].present? && params[:searchable_id].size <= 10
        variant_select_options = []
        if params[:first_part].to_s == 'Unpack'
          item = LineItem.preload(:design, variant: :option_type_values).where(id: params[:searchable_id]).last
          d = item.design
          variant_select_options = get_variant_details([item]) if item.variant_id.present?
        else
          d = Design.where(id: params[:searchable_id].to_i).first
          if params[:order_number].present? && (variant_items = LineItem.joins(:order).preload(variant: :option_type_values).where('orders.number = ? and design_id = ? and variant_id is not null', params[:order_number], params[:searchable_id].to_i)).present?
            variant_select_options = get_variant_details(variant_items)
          end
        end
        data = {weight: d.try(:actual_weight), variant_options: variant_select_options.presence}      
      end
      render json: data
    rescue => e
      ExceptionNotifier.notify_exception(
      e,
      data:{ input: "#{params[:first_part]}-#{params[:searchable_id]}", user: "#{current_account.name}"}
      )
      render json: data, status: :unprocessable_entity
    end
  end  

  def get_design_image
    render json: get_design_image_details(params[:design_id], params[:first_part])
  end

  def get_design_image_list
    image_list = []
    design = Design.find(params[:design_id])
    design_images = design.images
    design_images.each do |image|
      image_response = get_design_image_response(image)
      image_list << image_response
    end
    render json: image_list, status: :ok
  end

  def get_petticoat_details
    response = {error: 'Details Not Found.'}
    if params[:order_number].present? && params[:design_id].present? && params[:design_id].size <= 10
      pluck_string = 'line_item_addons.notes, line_items.quantity, addon_type_values.name, line_items.status, designer_orders.state, orders.state'
      join_array, product_status = [:order, line_item_addons: :addon_type_value], ''
      if params[:rack_check].present?  
        join_array = [:order]
        pluck_string = 'line_items.id,line_items.quantity, line_items.status, designer_orders.state, orders.state'  
      end
      addon_data = LineItem.joins(join_array).where('line_items.design_id = ? and lower(orders.number) = ?', params[:design_id].to_i, params[:order_number].downcase).pluck(pluck_string)
      product_status = (addon_data[0].to_a[2..-1].to_a.any?{|i| i.to_s.match(/^cancel*/)} ? 'Canceled' : (addon_data[0].to_a[4] == 'dispatched' ? 'Dispatched' : ''))
      addon_notes = addon_data.map(&:third).zip(addon_data.map(&:first)).map{|note| note.join(' => ')}
      response = {details: addon_notes, quantity: addon_data[0].try(:[],1), product_status: product_status}
    end
    render json: response  
  end

  def get_item_and_rack_details
    response = {error: 'Wrong Details Provided'}    
    preload_array = [designer_order: :rack_list]
    correct_data = false
    if params[:item_id].present?
      join_array, w_clause = [], ['id = ?', params[:item_id].to_s.first(10).to_i]    
      correct_data = true
    elsif params[:order_num].present? && params[:design_id].present?
      join_array = [:order]
      w_clause = ['design_id = ? and orders.number = ?', params[:design_id].first(10).to_i, params[:order_num]]
      correct_data = true
    end
    if correct_data && (item = LineItem.joins(join_array).preload(preload_array).where(w_clause).where('variant_id is null').last).present?
      stitching_done_rack = (rack_list = item.designer_order.rack_list).present? ? rack_list.code : nil
      response = {item_id: item.id, stitching_done_rack: stitching_done_rack, quantity: item.quantity, design_id: item.design_id, order_number: (params[:item_id].present? ? item.order.try(:number) : params[:order_num]) } 
      if params[:need_image] == 'true' && (image_res = get_design_image_details(item.design_id)) && image_res[:error].blank?        
        response.merge!(image_res)
      end
    end
    render json: response
  end

  def check_stitching_done_status
    response = {error: 'wrong details'}
    order = get_order_record_for_stitching_done
    if order.present?      
      des_orders = order.designer_orders
      valid_line_items = des_orders.select{|d_o| ['canceled', 'vendor_canceled'].exclude?(d_o.state)}.map(&:line_items).flatten.compact.select{|i| i.status.blank?}
      stitching_required_count = valid_line_items.count{|i| i.stitching_required == 'Y'}
      stitching_done_count = valid_line_items.count{|i| i.stitching_done_on.present?}
      scanned_item = valid_line_items.find do |i| 
        ((params[:order_num] == 'M' && i.id == params[:searchable_id].to_i) ||
        (i.design_id == params[:searchable_id].to_i)) && 
        i.stitching_required == 'Y' && i.stitching_done_on.blank?
      end
      if scanned_item.present? && (stitching_required_count - stitching_done_count) == 1
        response = {last_stitching_done_product: 'true'}
      end
    end
    render json: response
  end

  def get_order_record_for_stitching_done
    if params[:order_num] == 'M'
      Order.joins(:line_items).preload(:line_items).where(line_items: {id: params[:searchable_id]}).last
    else
      Order.preload(:line_items).where(number: params[:order_num]).last
    end
  end

  def check_for_tailoring_receiving_status
    response = {error: 'Some Details are missing.'}
    if params[:order_number].present? && params[:searchable_id].present?
      w_material_clause = (params[:handover_for] != 'FNP' ? ['tailoring_material not in (?)', ['Saree', 'Combo Saree']] : [])
      join_array, w_base_clause = get_base_query_needs_for_tailoring_receiving
      tailoring_infos = TailoringInfo.joins(join_array).where(w_base_clause).where(w_material_clause).to_a
      is_combo_product = tailoring_infos.any?{|t_info| t_info.tailoring_material.downcase.include?('combo')}
      is_all_received = (tailoring_infos.map(&:material_received_status).count(nil) == 0)
      material_with_quantity = []
      tailoring_infos.each {|t_info| material_with_quantity << "#{t_info.tailoring_material} - #{t_info.line_item_quantity}"}
      response = {combo_product: is_combo_product, all_received: is_all_received, material_with_quantity: material_with_quantity}
    end
    render json: response
  end


  def check_inscan_package_status
    response = {found: false}
    begin
      if params[:scanned_num].present?
        order = Order.select('orders.id as id, orders.other_details, recent_tracking_number').preload(:designer_orders).joins(:designer_orders).where('orders.confirmed_at >= ? and (tracking_num = ? or recent_tracking_number = ?)', INSCAN_DO_MONTHS.to_i.months.ago, params[:scanned_num], params[:scanned_num]).last
        if order.present?
          dso = DesignerOrder.where(tracking_num: params[:scanned_num])
          status_data = dso[0].line_items.map do |item|
            design_id = item.design.id
            premium = item.design.premium
            stitching_required = item.stitching_required
            status = premium ? "Luxe" : stitching_required
          end
          designer_dynamic_qc_rate = dso[0].designer.dynamic_qc_rate
          status_data << "QC" if (designer_dynamic_qc_rate && designer_dynamic_qc_rate > 0)

          is_replacement = (order.recent_tracking_number == params[:scanned_num])
          is_non_stitch_single_order = (order.other_details['stitching_order'] != 'true' && order.designer_orders.map(&:designer_id).uniq.count == 1)
          response = {found: true, replacement: is_replacement, non_stitch_single: is_non_stitch_single_order, design_status: status_data}
        end
      end
    rescue => error
      response = {found: false}
    end
    render json: response
  end

  def pdd_design
    response = {error: true, error_text: 'Something went wronge', eta: 0}
    if params[:id].present? && params[:pincode].present? && (design = Design.find params[:id]).present?
      pickup = design.sor_available? ? 'bhiwandi' : design.designer.pickup_location.try(:downcase)
      drop_city = Pincode.where(pin_code: params[:pincode]).pluck(:city).first
      city_pdd = DeliveryNpsInfo.get_city_based_pdd(drop_city, [pickup]) if drop_city.present? && pickup.present?
      if city_pdd.to_i > 0
        city_pdd += design.eta.to_i unless design.sor_available?
        city_pdd += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless design.sor_available?
        city_pdd += 1 if Time.current.advance(days: city_pdd).sunday?
      end
      cod_available = design.designer.can_cod?(params[:pincode])
      if COD_REQUEST_ENABLE == 'true'
        CodRequest.where(ip: request.remote_ip, app_source: 'Desktop',
           pincode: params[:pincode], design_id: params[:id], is_cod_available: cod_available).first_or_create
      end
      response = {error: false, error_text: '',cod_available: cod_available, eta: (Time.now + city_pdd.days).strftime('%d %b, %Y')} if city_pdd.to_i > 0
    end
    render json: response
  end 

  def cod_design
    response = {cod_available: false, error: false, cod_designs: []}
    if params[:id].blank? || params[:pincode].blank?
      response = {cod_available: false, error: true, error_text: 'params blank'}
    else
      pincode = params[:pincode]
      ids = params[:id].kind_of?(Array) ? params[:id] : params[:id].split(",")
      begin
        designs = Design.where(id: ids)
        if designs.present? && pincode != '0'
          designs.each do |design|
            if design.designer.can_cod?(pincode) == false
              response[:cod_available] = false
            else
              response[:cod_designs] << design.id
            end
          end
          response[:cod_available] = response[:cod_designs].count > 0 ? true : false
        else
          response[:cod_available] =  false
          response[:error] = true
          response[:error] = 'design not found | pincode invalid'
        end
        unless designs.count > 1
          if COD_REQUEST_ENABLE == 'true'
            CodRequest.where(ip: request.remote_ip, app_source: 'Desktop',
               pincode: pincode, design_id: params[:id], is_cod_available: response[:cod_available]).first_or_create
          end
        end
      rescue => error
       response = {error: 'Error Occured.'}
      end
    end
    render json: response
  end

  def check_free_shipping
    begin
      country = params[:country]
      cart = Cart.where(id: params[:cart_id]).preload(line_items: [design: :categories]).first
      shipping = cart.get_international_shipping_cost(@conversion_rate, country)
      grandtotal, taxes, shipping, wallet_discount = @cart.get_all_the_cart_details(@country_code, country , nil ,@conversion_rate, nil, session[:gift_wrapped])
      shipping_with_currency = cart.get_international_shipping_cost_in_currency_with_symbol(@conversion_rate, @symbol, country)
      available = cart.get_free_shipping_rate(@conversion_rate, country) > 0 && cart.free_shipping_available?(@conversion_rate, country) && !(cart.cart_has_bmgn_products? && PromotionPipeLine.bmgnx_hash[:free_shipping] != 'true') && !cart.shipping_categories_available?
      apply_for_country = cart.get_free_shipping_rate(@conversion_rate, country) > 0
      shipping_text = cart.free_shipping_charges_text(country, @conversion_rate, @symbol)
      express_shipping_charge = (Country.get_express_delivery_charge(country)/@conversion_rate).round(2)
      express_shipping_with_currency = country == 'India' ? "#{@symbol} #{express_shipping_charge}" : "#{@symbol} #{shipping + express_shipping_charge}"
      response = { express_shipping_charge: express_shipping_charge, express_shipping_with_currency: express_shipping_with_currency, available: available, shipping: shipping, 
        shipping_with_currency: shipping_with_currency, apply_for_country:  apply_for_country, shipping_text: shipping_text, grandtotal: grandtotal, taxes: taxes, wallet_discount: wallet_discount}
    rescue => error
      response = {free_shipping: false, error_text: error.message, error: true}
    end
     render json: response
  end

  def get_cod_charge
    cart = Cart.find_by_id(params[:cart_id])
    cod_charge = Order.domestic_cod_charge(params[:pincode], cart) || 0
    cod_charge = (cod_charge/@conversion_rate).round(2)
    response = {cod_charge: cod_charge}
    render json: response
  end

  def process_app_rank_file
    filepath = params[:filepath]
    AppRankingKeyword.sidekiq_delay
                     .parse_app_rank_report(
                       filepath, params[:batch_code], params[:email],
                       params[:prev_ranking_batch],
                       params[:alert_task] == 'true'
                     )
    render json: {status: 'ok'}
  end

  def insert_new_clusters
    render json: {msg: 'No Data'} and return if params[:new_designs].blank?
    if (token = request.headers['Authorization']).present? && SystemConstant.get('WEB_DEVICE_ID') == Designer.onlinesalses_cipher(Base64.urlsafe_decode64(token), :decrypt, ENV['RANDOM_PRIVATE_KEY'])
      DesignCluster.sidekiq_delay
                   .assign_cluster(
                     params[:new_designs],
                     params[:failed_designs],
                     params[:winner_hash]
                   )
      render json: {msg: 'Cluster process initiated'}
    else
      render json: {msg: 'Not Authorized'}
    end
  end
  private

  def get_design_image_details(searchable_id, first_part=nil)
    response = {error: 'Design not found'}
    if first_part == 'Unpack'
      if (item = LineItem.find_by_id(searchable_id)) && (image = item.design.try(:master_img))
        response = get_design_image_response(image)
      end
    else
      if (image = Image.where(design_id: searchable_id.to_s.first(10), kind: 'master').first).present?
        response = get_design_image_response(image)
      end
    end
    response
  end

  def get_design_image_response(image_obj)
    { 
      product_id: image_obj.design_id, 
      zoom_url: image_obj.try(:photo).try(:url, :zoom), 
      large_url: image_obj.try(:photo).try(:url, :large),
      original_url: image_obj.try(:photo).try(:url, :original)
    }
  end

  def get_base_query_needs_for_tailoring_receiving
    if params[:order_number] == 'M'
      [[], ['item_id = ?', params[:searchable_id].first(10)]]
    else
      [[:order, :line_item], ['orders.number = ? and line_items.design_id = ?', params[:order_number].upcase, params[:searchable_id].first(10)]]
    end
  end

  def get_variant_details(items)
    items.map(&:variant).compact.map{|v| [v.option_type_values[0].try(:p_name), v.id]}
  end

end
