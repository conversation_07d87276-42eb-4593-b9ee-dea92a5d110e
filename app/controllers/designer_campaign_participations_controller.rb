class DesignerCampaignParticipationsController < ApplicationController
    def create
        csv_file = params[:csv_file]
        seller_campaign_id = params[:seller_campaign_id]
        designer_id = params[:designer_id]
        if csv_file.present? && csv_file.content_type == 'text/csv'
            filename = "design-csv-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
            directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
            fog_object = directories.files.create(
              key: filename,
              body: csv_file.read,
              public: false
            )
            @csv_file_path = fog_object.url(Time.now + 7200) # URL expires in 2 hour
            UploadCampaignDiscountJob.perform_async(@csv_file_path, current_account.email, seller_campaign_id, designer_id)
        end
        @seller_campaign = SellerCampaign.find params[:seller_campaign_id]
        @designer = Designer.find params[:designer_id]
        @designer_campaign_participation = DesignerCampaignParticipation.create(seller_campaign: @seller_campaign, designer: @designer)
        respond_to do |format|
            format.html { redirect_to all_campaign_path(@designer)}
            format.json { render json: { message: "Our team will get back to you soon for futher details. Thank you for participating." }}
        end
    end

    def cancel
        @designer_campaign_participations = DesignerCampaignParticipation.find params[:id]
        @designer = Designer.find @designer_campaign_participations.designer_id
        if @designer_campaign_participations.present?
            @designer_campaign_participations.seller_campaign.design_campaign_discounts.destroy_all
            @designer_campaign_participations.destroy
        else
            message = 'Cannot find Campaign'
        end
        redirect_to all_campaign_path(@designer), notice: message
    end
end
  