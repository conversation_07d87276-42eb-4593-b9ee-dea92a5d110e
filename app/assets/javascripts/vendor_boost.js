var MR = MR || {};

MR = (function (window, document, Mirraw) {
  Mirraw.boostModal = {
    init: function () {
      this.bindEvents();
    },

    bindEvents: function () {
      $(document).on("click", ".boost-button", this.showModal);
      $(document).on("click", ".close", this.hideModal);
      $(document).on("click", "#confirmBoost", this.confirmBoost);
    },

    showModal: function () {
      var designId = $(this).data("design-id");
      var designerId = $(this).data("designer-id");
      
      $("#boostModal").data("design-id", designId);
      $("#boostModal").data("designer-id", designerId);
      
      $("#boostModal").show();
    },

    hideModal: function () {
      $("#boostModal").hide();
    },

    confirmBoost: function () {
      var designId = $("#boostModal").data("design-id");
      var designerId = $("#boostModal").data("designer-id");
      MR.boostModal.showLoader()
      
      $.ajax({
        url: "/designers/" + designerId + "/boost_product",
        type: "POST",
        data: { design_id: designId },
        dataType: "json",
        success: function (response) {
          MR.boostModal.hideModal();
          if (response.success) {
            alert(response.message);
            location.reload();
          } else {
            MR.boostModal.hideModal();
            MR.boostModal.hideLoader();
            alert("Error: " + response.errors.join(", "));
          }
        },
        error: function (xhr, status, error) {
          MR.boostModal.hideModal();
          MR.boostModal.hideLoader();
          var response = xhr.responseJSON;
        
          if (response && response.errors) {
            if (Object.prototype.toString.call(response.errors) === '[object Array]') {
              alert("Error: " + response.errors.join(", "));
            } else if (typeof response.errors === "object") {
              // Handle object form { field1: ["msg1"], field2: ["msg2"] }
              var messages = [];
              for (var key in response.errors) {
                if (Object.prototype.toString.call(response.errors[key]) === '[object Array]') {
                  Array.prototype.push.apply(messages, response.errors[key]);
                } else {
                  messages.push(response.errors[key]);
                }
              }
              alert("Error: " + messages.join(", "));
            } else {
              alert("Error: " + response.errors);
            }
          } else {
            alert("An unexpected error occurred: " + error);
          }
        }        
      });
    },

    showLoader: function () {
      $("#pageLoader").fadeIn();
    },

    hideLoader: function () {
      $("#pageLoader").hide();
    }
  };

  $(document).ready(function () {
    MR.boostModal.init();
  });

  return Mirraw;
})(this, this.document, MR);
