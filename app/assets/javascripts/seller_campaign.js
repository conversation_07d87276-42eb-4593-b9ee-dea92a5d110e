var MR = MR || {};
MR = (function(window, document, Mirraw){
  MR.sellerCampaign = {
    datePicker: function() {
      var selectedStartDate = $(".datepicker.start_date").val() || null;
      var selectedEndDate = $(".datepicker.end_date").val() || null;

      $(".datepicker.start_date").datepicker({
        dateFormat: 'yy-mm-dd',
        defaultDate: selectedStartDate,
        onSelect: function(selectedDate) {
          var selected = new Date(selectedDate);
          $(".datepicker.end_date").datepicker("option", "minDate", selected);
        }
      });

      $(".datepicker.end_date").datepicker({
        dateFormat: 'yy-mm-dd',
        defaultDate: selectedEndDate,
        minDate: selectedStartDate ? new Date(selectedStartDate) : null
      });
    },
  campaignBanner: function() {
    $("#closeBtn").click(function() {
      $("#campaigns-carousel-banner").hide();
    });
  },
  
  campaignPopup: function() {
    setTimeout(function() {
      $("#campaigns-popup-modal").modal("show");
    }, 1000);

    $('#close-campaigns-modal').click(function() {
      $("#campaigns-popup-modal").modal("hide");
    });
    
    $(document).on("ajax:success", "form", function(event, data) {
      alert(data.message);
      $(this).find(".participateButton").val("Participated").prop("disabled", true); 
    });
  }
};
  return Mirraw;
})(this, this.document, MR);