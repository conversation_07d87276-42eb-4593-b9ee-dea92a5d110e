var MR = MR || {};
var MR = (function (window, document, Mirraw) {
  var variantsPresent = 0;
  Mirraw.designs = {
    init: function () {
      $('#size-chart').hide().find('input[type="file"]').prop('required', false);
      this.searchField=$('#search-input')
      this.categoryDropdown=$("#design_category_ids")
      this.selectedChips = $("#selected-categories");
      this.variantBlockClass = '.variant_items';
      this.designQuantityFieldId = '#design_quantity';
      this.validateVariants();
      this.searchEvent();
    },
    
    displayVariantsAddons: function() {
      var self=this;
      $('.addon').hide();
      this.hideVariants();
      $('#design_category_ids input[type="checkbox"]:checked').each(function() {
        var category_parent_name = $(this).next('label').text().split('< ');
        $('.' + category_parent_name[1]).show();
        self.showVariants(this);
      });
      this.designQuantityDisplayCheck();
    },

    showVariants: function(category_element){
    var category_id = '#category_'+$(category_element).val()
    if ($(category_id).length >= 1 && variantsPresent == 0){
      $(category_id).show()
      this.enableForms(category_id)
      variantsPresent = 1
    }
    },

    hideVariants: function(){
      $(this.variantBlockClass).hide();
      this.disableForms(this.variantBlockClass);
      variantsPresent = 0;
    },

    enableForms: function(location){
      $(location).find('input[disabled="disabled"]').prop('disabled', false);
    },

    disableForms: function(location){
      $(location).find('input').prop('disabled', true);
    },

    designQuantityDisplayCheck: function(){
      if ($(this.variantBlockClass).length >= 1 && $(this.variantBlockClass).is(':visible')) {
        $(this.designQuantityFieldId).prop('disabled', true);
        $(this.designQuantityFieldId).parent().hide();
      } else {
        $(this.designQuantityFieldId).prop('disabled', false);
        $(this.designQuantityFieldId).parent().show();
      }
    },

    validateVariants: function() {
      var self=this;
      self.hideVariants('.variants');
      self.displayVariantsAddons();
      $('#design_category_ids input[type="checkbox"]').change(function() {
        self.displayVariantsAddons();
      })
      
      $('#design_category_ids input[type="checkbox"]:checked').each(function() {
        var categoryParentName = $(this).next('label').text().split('< ');
        $('.' + categoryParentName[1]).show();
        self.showVariants(this);
        self.designQuantityDisplayCheck();
      });
      
      $('#design_category_ids, .stitched').change(function(e) {
        var stitching = $('.stitched option:selected').val();
        if($(e.target).is(':checked')){
          var category=$(e.target).val();
        }
        else{
          var category = $('#design_category_ids input[type="checkbox"]:checked').val();
        }
        var isStitched = stitching === undefined || +stitching === gon.stitched_property_id;
        var isCategoryDefined = category !== undefined;
        if (isStitched && isCategoryDefined) {
          $.ajax({
            url: '/designers/' + gon.designer_id + '/designs/get_option_type',
            type: 'GET',
            dataType: 'script',
            data: { category_id: category }
          });
        } else {
          $('#variants-panel').html('');
          $('#size-chart').hide().find('input[type="file"]').prop('required', false);
        }
      });
    },
    
    searchEvent: function () {      
      var self = this;
      this.searchField.on("keydown", function (e) {
        if (e.key === "Enter") {
          e.preventDefault();
        }
      }); 
      this.renderSelectedChips();
      this.searchField.on("input", function (e) {
        var query=$(e.target).val().toLowerCase().trim();
        self.categoryDropdown.find(".category-item").each(function () {
          var toggleCategoryLabel=false;
          var categoryLabels = $(this).find("label").text().toLowerCase().split(" < ");
          for (var i = 0; i < categoryLabels.length; i++) {
            if (categoryLabels[i].indexOf(query) === 0) {
              toggleCategoryLabel = true;
              break;
            }
          }
          $(this).toggle(toggleCategoryLabel);
        });
      });

      this.categoryDropdown.on('change', 'input[type="checkbox"]',function () {
        var max_uploadable_categories = gon.max_uploadable_categories
        if(self.categoryDropdown.find('input:checked').length> max_uploadable_categories){
          alert("You can select a maximum of " + max_uploadable_categories + " categories!")
          $(this).prop('checked', false);
          return false;
        }
        else{
          self.renderSelectedChips($(this));
        };
        }
      )

      this.selectedChips.on("click", ".remove", function (e) {
        self.removeChip($(e.target).closest("li").data("id"));
      });
    },

    renderSelectedChips: function () {
      var self=this;
      this.selectedChips.empty();  
      self.categoryDropdown.find('input:checked').each(function(index,option) {
        self.selectedChips.append('<li data-id="' + $(option).val() + '">' +$('label[for="' + $(option).attr('id') + '"]').text() + ' <button class="remove">&times;</button></li>');
      });
    },

    removeChip: function (categoryId) {
      $("#category_"+categoryId).prop('checked', false);
      $("#category_"+categoryId).trigger("change");
    } 
};   

  return Mirraw;
})(this, this.document, MR);