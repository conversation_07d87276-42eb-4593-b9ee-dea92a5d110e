var MR = MR || {};

MR = (function(window, document, Mirraw) {
  MR.common = {
    ajaxify: function(url, type, data, success_function, error_function) {
      $.ajax({
        type: type,
        url: url,
        data: data,
        success: success_function,
        error: error_function
      });
    }
  };

  MR.filters = {
    init: function() {
      $(document).on('change', '#type_of_filter', function () {
        var selected = $(this).val();
        MR.filters.handleFilterChange(selected);
      });
      
      $('form').on('submit', function() {
        $('#warehouse_input:hidden input').val('');
        $('#design_input:hidden input').val('');
      });
    },

    handleFilterChange: function(selected) {
      switch (selected) {
        case 'Warehouse Order Number':
          MR.filters.showInput('#warehouse_input');
          MR.filters.hideAndClearInput('#design_input');
          break;
        case 'Design Id':
          MR.filters.showInput('#design_input');
          MR.filters.hideAndClearInput('#warehouse_input');
          break;
        default:
          MR.filters.hideAndClearInput('#warehouse_input');
          MR.filters.hideAndClearInput('#design_input');
      }
    },

    showInput: function(selector) {
      $(selector).show();
    },

    hideAndClearInput: function(selector) {
      $(selector).hide();
      $(selector).find('input').val('');
    }
  };

  return Mirraw;
})(this, this.document, MR);

$(function() {
  MR.filters.init();
});
