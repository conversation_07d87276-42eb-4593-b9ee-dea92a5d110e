# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/
//= require jquery.validate.min.js

root = exports ? this

jQuery.extend jQuery.validator.messages,
  required: "Required."
  remote: "Please fix this field."
  email: "Please enter a valid email address."
  url: "Please enter a valid URL."
  date: "Please enter a valid date."
  dateISO: "Please enter a valid date (ISO)."
  number: "Please enter a valid number."
  digits: "Please enter only digits."
  creditcard: "Please enter a valid credit card number."
  equalTo: "Please enter the same value again."
  accept: "Please enter a value with a valid extension."
  maxlength: jQuery.validator.format("Please enter no more than {0} characters.")
  minlength: jQuery.validator.format("Please enter at least {0} characters.")
  rangelength: jQuery.validator.format("Please enter a value between {0} and {1} characters long.")
  range: jQuery.validator.format("Please enter a value between {0} and {1}.")
  max: jQuery.validator.format("Please enter a value less than or equal to {0}.")
  min: jQuery.validator.format("Minimum value {0}.")

addon_prod_time = 0
addon_id = 0
variant_block_class = '.variant_items'
design_quantity_field_id = '#design_quantity'
variants_present = 0

paramAddToCart = (design_id_val) ->
	type: "POST"
	data: 
	  design_id: design_id_val
	url: '/line_items'
	dataType: 'script'
	success: ->
	  $('.progress_img1').hide()
	  $('.product_discription_div').css('opacity', '1')
	error: ->
	  $('.progress_img1').hide()
	  $('.product_discription_div').css('opacity', '1')

get_breadcrumbs_array = () ->
  breadcrumb_array = []
  index = 0
  if $(".design-breadcrumb > ol > li > a").length > 0
    breadcrumbs = $(".design-breadcrumb > ol > li > a")
  else if $(".store-breadcrumb > ol > li > a").length > 0 
    breadcrumbs = $(".store-breadcrumb > ol > li > a")
  else
    breadcrumbs = []
  for breadcrumb in breadcrumbs
    if breadcrumb.text != '' 
      breadcrumb_text = breadcrumb.text.trim() 
      breadcrumb_array.push({title: breadcrumb_text})
  return JSON.stringify(breadcrumb_array)

paramAddToCartPlusAddons = (items) ->
  type: "POST"
  data:
    line_items: items
    breadcrumb: get_breadcrumbs_array
  url: '/line_items'
  datatype: 'script'
  success: ->
    $('.progress_img1').hide()
    $('.product_discription_div').css('opacity', '1')
    $('.catalog2_facet').css('opacity', '1')
    $('.cart_div .bx-wrapper .slide').each ->
      $(this).css('width', '200px')    
  error: ->
    $('.progress_img1').hide()
    $('.product_discription_div').css('opacity', '1')

getAddons = () ->
  addons_values = []
  if $('#stitching_radio_ui').length > 0
    $('.radio_addons, .length_option').each ->
      if ($(this).is(':checked') || $(this).is('select')) && !$(this).hasClass('standard-stitch-variant')
        addon_type_value_id = $(this).attr('value')
        addon_option_value_id = []
        $('.aov_atv_'+addon_type_value_id).each ->
          id=$(this).attr('id')
          if $(this).is(':checkbox') && $(this).is(':checked')
            addon_option_value_id.push($(this).val())
          else if ['render_plus_size_color','shapewear_color_parent_div'].includes(id)
            object_id = if id =='render_plus_size_color' then '.plus_size_fabric_selected' else '.shapewear_color_selected'
            addon_option_value_id.push($(object_id).attr('value')) if $(object_id).length != 0
          else if !$(this).is(':checkbox')
            addon_option_value_id.push($(this).val())
        addons_values.push({addon_type_value_id: addon_type_value_id, addon_option_value_id: addon_option_value_id})
    addons_values
  else if $('.addon_type').length > 0
    $('.addon_type').each ->
      addon_type_value_id = $(this).attr('value')
      addon_option_value_id = []
      $('.atv_'+ addon_type_value_id + '_option_values').each ->
        addon_option_value_id.push($(this).val())
      addons_values.push({addon_type_value_id: addon_type_value_id, addon_option_value_id: addon_option_value_id})
    addons_values
  else
    #not visible
    "NV"

$('.addon_type').on "change", ->
  addon_type_value_id = $(this).attr('value')
  $(this).find("option").each ->
    current_val = $(this).val()
    if current_val != addon_type_value_id
      $('.atv_' + current_val + '_option_types').hide()  
  $('.atv_' + addon_type_value_id + '_option_types').show()
  $('.atv_' + addon_type_value_id + '_option_values').css({'border-color':'', 'border-width':''})


updateAddonView = (atv_id) ->  
  if $('.'+atv_id+'_option_types').length > 0
    if $('#'+atv_id).is(':checked')
      $('.'+atv_id+'_option_types').slideDown()
    else
      $('.'+atv_id+'_option_types').slideUp()

$ ->
  $('.radio_addons').each ->
    updateAddonView($(this).attr('id'))
    if $(this).hasClass('custom-rd-stitching') && $(this).is(':checked')
      $('.custom-stitching-notice').slideDown()

$('.radio_addons').on "change", ->
  estd_delivery = []
  custom_stitching = false
  if $('#pre-order-check').length > 0 && $('#pre-order-check').is(':checked')
    return
  else
    date_to_select = 'delivery-date'
    $('.radio_addons').each ->
      updateAddonView($(this).attr('id'))
      if $(this).is(':checked')
        if $(this).hasClass('custom-rd-stitching')
          custom_stitching = true
        if $(this).hasClass('standard') && $('button.size.selected').length > 0
          size_selected = $('button.size.selected')
          if parseInt(size_selected.data('prod-time')) == 0
            date_to_select = 'rts-date'
        else
          size_selected = $(this)
        if size_selected.data(date_to_select) != undefined && size_selected.data(date_to_select).length > 0
          delivery_time = size_selected.data(date_to_select)
        else
          delivery_time = size_selected.data('delivery-date')
        estd_delivery.push([parseInt(size_selected.data('prod-time')),delivery_time,size_selected.data('ready-to-ship')])
    if custom_stitching == true
      $('.custom-stitching-notice').slideDown()
      if $('.variant_addons').length > 0
        $('.variant_addons').slideUp()
        change_displayed_design_price($(this).attr('data-old-price'),$(this).attr('data-price'),'')
    else
      $('.custom-stitching-notice').slideUp()
      if $('.variant_addons').length > 0
        $('.variant_addons').slideDown()
        change_displayed_design_price($('.standard-stitch-variant').attr('selected-variant-oldprice'), $('.standard-stitch-variant').attr('selected-variant-price'), 'Price shown is for the size selected')
    max_delivery_date = estd_delivery.sort().pop()
    if max_delivery_date[2] == true
      $('.rts-label, del.strike_old_date').show()
    else
      $('.rts-label, del.strike_old_date').hide()
    $('.prod_time').html(max_delivery_date[1])

$('.aov_select').on "change", ->
  if $(this).is(':visible') && $(this).val() != "0"
    $(this).css({'border-color':'', 'border-width':''})
    $(this).siblings('.form-control').slideUp()
    if $('#std-stitching').length > 0
      height = $('#'+ this.id + ' option:selected').text().match(/\d+/g).map(Number).slice(0,2)
      height_inch = height[0] * 12
      if height.length > 1
        height_inch += height[1]
      img = gon.anarkali_standard_sugguestion['default']
      $.each gon.anarkali_standard_sugguestion, (k,v) ->
        if k <= height_inch
          img = v
      top_style_element = ''
      if $('#std-stitching + span').length > 0
        top_style_element = $('#std-stitching + span')
      else
        top_style_element = $("<span style= 'font-size:11px;'></span>")
      status = top_style_element.text('For the selected size, kameez will fall ' + img.replace(/_/g, ' ') + ' area')
      $('#std-stitching').after(status)

      $('#std-stitching').css({'display':'block'})
      title = $('#std-stitching').attr('data-original-title').replace('block','none')
      $('#std-stitching').attr('data-original-title',title.replace(img+'" style="display:none',img+'" style="display:block'))
      $('#std-stitching').tooltip('show')
      setTimeout (->
        $('.tooltip').fadeOut();
      ), 3000
      $('.tooltip').css({'z-index': '990'})
      $('.tooltip-arrow').attr('style','border-right-color: #ffffff !important;')
      $('.tooltip-inner').css({'background-color': '#ffffff'})

checkVariantSelection = () ->
  $('.variant_text').removeClass 'shake-effect'
  $('.combo_variant_text').removeClass 'shake-effect'
  status = true
  size_modal = $('.size-modal-visible')
  design_id = $('.size-modal-visible').attr('value')
  # This checks to elements with class name variants exist or not
  if $('.variant').length > 0 && $('.combo_variant').length > 0 
    status = false
    if $('.variant.selected').length >= 1 && $('.combo_variant.selected').length >= 1
      status = true
  else if $('.variant').length > 0
    status = false
    if $('.variant.selected').length >= 1
      status = true
  else if $('.form-groups').is(':visible') 
    status=true
  else if $('.combo_variant').length > 0 && $('.size-chart').length > 0 && $('.size-chart').is(':visible')
    status = false
    if $('.size.selected').length == 1 && $('.combo_variant.selected').length >= 1
      status = true
  else if $('.combo_variant').length > 0
    status = false
    if $('.combo_variant.selected').length >= 1
      status = true
  else if $('.size-chart').length > 0 && $('.size-chart').is(':visible')
    status = false
    if $('.size.selected').length == 1
      status = true
  if size_modal.length == 1 && !size_modal.find('.variant_modal').hasClass('selected') || size_modal.find('.variant_modal').hasClass('variant-selected')
    $('#addon_product_'+design_id).prop 'checked' , false

  return status

change_displayed_design_price = (old_price,new_price,delivery_date=undefined,ready_to_ship=false,design_id=undefined) ->
  if design_id != undefined
    change_discounted_price = $('.new_price_label_'+design_id)
    change_wo_discounted_price = $('.old_price_label_'+design_id)
  else
    change_discounted_price = $('.new_price_label')
    change_wo_discounted_price = $('.old_price_label')
  if new_price != undefined && $('.new_price_label')[0].textContent != new_price
    $('.variant-min-price-text').css 'display' , 'none'
    change_discounted_price.text(new_price).addClass("shake-effect")
    change_wo_discounted_price.text(old_price)
    setTimeout (->
      $('.new_price_label').removeClass 'shake-effect'
    ), 750
  total = parseFloat($('.added-items-price').text()) + parseFloat($('.new_price_label').text().split(" ")[1])
  $('.total-price').text total.toFixed(2)
  if delivery_date != undefined
    if ready_to_ship == 'true'
      $('.rts-label, del.strike_old_date').show()
    else
      $('.rts-label, del.strike_old_date').hide()
    $('.prod_time').html(delivery_date)

getVariant = () ->
  if $('.variant').length > 0
    $('.variant.selected').attr('id')
  else if $('.custom-rd-stitching').is(':checked') && $('.variant_stitch').length > 0
    $('.custom-rd-stitching').attr('data-variant-id')
  else if $('.variant_stitch').length > 0
    $('.variant_stitch.selected').attr('id')
  else
    #not visible
    'NV'


getStandardSize = () ->
  $('.size.selected').attr('data-size-id')

checkExp = () ->
  if $('#getContexioEnrichedData').length > 0 && $('#product_desc').html() != '' then return 'contexio' else return 'mirraw'

checkPlusSize = (color_class) ->
  status=false
  if (($('.fabric_color_table').is(':visible') && $(color_class).attr('id') == 'render_plus_size_color' && $('.plus_size_fabric_selected').length == 0))
    status=true
  return status

checkAddons = () ->
  status = true
  $('.length_option').each ->
    if $(this).val() == ''
      status = false
      $(this).css({'border-color':'#7b0e1d', 'border-width':'2px'})
  if status == false
    alert 'Please select highlighted options'
  if $('#stitching_radio_ui').length > 0
    $('.radio_addons').each ->
      atv_id = $(this).attr('id')
      if $('.'+atv_id+'_option_types').length > 0 && $('.'+atv_id+'_option_types').is(':visible')        
        option_value_select = $('.aov_'+atv_id)
        option_value_select.each -> 
          if $(this).is(':visible') && $(this).val() == "0" || checkPlusSize(this) || (($(this).attr('id') == 'shapewear_color_parent_div' && $('.shapewear_color_selected').length == 0))
            status = false
            $('html, body').animate({scrollTop: $(this).parents("fieldset").offset().top}, 1000);
            $(this).css({'border-color':'#7b0e1d', 'border-width':'2px'})
            $(this).siblings().slideDown()
    return status
  else
    $('.addon_type').each ->
      addon_type_value_id = $(this).attr('value')
      if $('.atv_'+ addon_type_value_id + '_option_values').length > 0
        option_value_select = $('.atv_'+ addon_type_value_id + '_option_values')
        option_value_select.each ->
          if $(this).val() == "0"
            status = false
            return
        if status == false
          option_value_select.css({'border-color':'red', 'border-width':'3px'})
        else
          option_value_select.css({'border-color':'', 'border-width':''})
    if status == false
      alert 'Please select highlighted options'
    return status
  return status

rakhi_note = () ->
  status = false
  # This checks to elements with id pre-order-check exist or not
  if $('#pre-order-check').length > 0
    if $('#pre-order-check').is(':checked')
      status = true
    else
      status = false
  return status

$ ->
  if $('#quickCodOrderModal').length > 0
    $('#quick-cod-order-button').on 'click', ->
      $('#codFormSuccessButton').trigger('click')

    $('#order_billing_pincode').on 'input', ->
      $('#cod-not-avail-msg').hide()
      pincode = $(this).val()
      if (/\d{6}/g).test(pincode)
        $.ajax(paramFetchCityStateOptions(pincode, 'order_billing_city', 'order_billing_state'))
        $.ajax(paramCheckCashOptions($('#cart_final_id').val(), pincode))
      else
        $('#quick-cod-order-button').removeAttr('disabled').css('opacity', '1')

    $('#quickCodOrderModal').on 'hidden.bs.modal', ->
      $('#cod-otp-error-msg, #phone-incorrect-error, #cod-not-avail-msg').hide()
      $('#quick_cod_form').trigger("reset")
      $('#order_cod_charge').html('Yet to apply')
      $('#order-grand-total').html('')
      $("label.error").hide()
      enableQuickCodForm()
      line_item_id = $('.close_cod_modal').attr('id')
      if parseInt(line_item_id) > 0
        url = '/line_items/' + line_item_id
        $.ajax
          type: "POST"
          url: url
          dataType: 'script'
          data:
            _method: 'delete'
            quick_cod_request: 'true'

    $(document).on 'keydown', ->
      $('#phone-incorrect-error:visible, #cod-otp-error-msg:visible').hide()

    $('#quick-cod-form-link').on 'click', (e) ->
      if checkAddons()
        design_id = $(this).data('design-id')
        if checkVariantSelection() == true
          if $('.variant').length > 0
            $.ajax(paramAddToCartPlusAddonsDesignCod(design_id, getAddons(), getVariant(), rakhi_note(), false, checkExp()))
          else
            $.ajax(paramAddToCartPlusAddonsDesignCod(design_id, getAddons(), getVariant(), rakhi_note(), getStandardSize(), checkExp()))
        else
          if ('.variant').length > 0
            alert('Please select size option above buy this now button')

    $.validator.addMethod "regx", (value, element, regexpr) ->
      return regexpr.test(value);
    , "Please enter a valid value."

    $('#quick_cod_form').validate
      rules:
        "order[billing_email]":
          required:
            true
          email: true
        "order[billing_name]":
          required: true
          minlength: 3
        "order[billing_phone]":
          required: true
          regx: /^\+?[0-9|\-|\s]*$/
          minlength: 10
          maxlength: 15
        "order[billing_country]":
          required: true
        "order[billing_pincode]":
          required: true
          regx: /\d{6}/
        "order[billing_street_line_1]":
          required: true
        "order[billing_street_line_2]":
          required: true
        "order[billing_street]":
          required: true
        "order[billing_city]":
          required: true
        "order[billing_state]":
          required: true

    $('#quick_cod_form').submit (e, submit) ->
      if $(this).valid()
        if !submit && $('#otp-form').length
          phoneNo = $("#order_billing_phone").val()
          $.ajax(generateOtp(phoneNo, false))
          disableQuickCodForm()
          return false
        else
          $('#quick_cod_form select').removeAttr('disabled')
      else
        return false

    $('#phone-change-button').on 'click', ->
      enableQuickCodForm()
      $('#order_billing_phone').focus()

    $('#otp-form').on 'ajax:success', (evt,data) ->
      if data['verified']
        $('#quick_cod_form').trigger('submit', true)
        design_data = $('#codFormSuccessButton').data()
        ga('ec:addProduct', {'id' : design_data.id, 'name' : design_data.id+'_', 'category' : design_data.category, 'brand' : design_data.brand, 'price' : design_data.price, 'quantity' : '1'})
        ga('ec:setAction','checkout', {'step': 2})
        ga('send', 'event', 'UX', 'click', 'Place Order')
        ga('send', 'event', 'UX', 'click', 'Quick cod place order')
      else
        $('#cod-otp-error-msg').show()

    $('#otp-resend-button').on 'click', ->
      $(this).attr('disabled', true).css('opacity', 0.5)
      setTimeout ->
        $('#otp-resend-button').attr('disabled', false).css('opacity', 1)
      ,10000
      $.ajax(generateOtp($("#order_billing_phone").val(), true))

paramCheckCashOptions = (cart_id, pincode) ->
  type: 'GET'
  data:
    pincode: pincode
    cart_id: cart_id
  url: '/api/cod_cbd'
  datatype: 'JSON'
  success: (data, status, jqhxr) ->
    updateCashPaymentOptions(data, pincode)

updateCashPaymentOptions = (data, pincode) ->
  if data.cart_value < data.min_cart_value
    $('#msg').html("Cash on delivery is available for orders above "+data.symbol+" "+ data.min_cart_value + ". ")
    $('#cod-not-avail-msg').show()
    $('#quick-cod-order-button').attr('disabled', 'disabled').css('opacity', '0.5')
    $('#order_cod_charge, #order-grand-total').html('')
  else if data.cod
    $('#order_cod_charge').html(data.cod_c.replace('₹', 'Rs'))
    total = parseFloat(data.cart_value) + parseFloat(data.cod_charge) + parseFloat(data.shipping_charge)
    $("#order-grand-total").html('Rs ' + total.toFixed(2))
    $('#quick-cod-order-button').removeAttr('disabled').css('opacity', '1')
  else
    $('#msg').html("This Product is not available for Cash On Delivery to " +  pincode + ". ")
    $('#cod-not-avail-msg').show()
    $('#quick-cod-order-button').attr('disabled', 'disabled').css('opacity', '0.5')
    $('#order_cod_charge, #order-grand-total').html('')

enableQuickCodForm = () ->
  $('#otp-form-and-content').hide()
  $('#quick_cod_form input').not('#order_billing_country').removeAttr('readonly').css('opacity', '1')
  $('#quick_cod_form select').removeAttr('disabled').css('opacity', '1')
  $('#quick-cod-order-button').show()
  $('#quick-cod-order-button').removeAttr('disabled').css('opacity', '1')

disableQuickCodForm = () ->
  $('#otp-form-and-content').show()
  $('#quick_cod_form input').not('#order_billing_country').attr('readonly', 'true').css('opacity', '0.5')
  $('#quick_cod_form select').attr('disabled', 'disabled').css('opacity', '0.5')
  $('#quick-cod-order-button').hide()
  $('#phone-text-for-otp').html($("#order_billing_phone").val())

generateOtp = (phoneNo, resend) ->
  type: 'POST'
  data:
    phone:phoneNo
    resend:resend
  url: '/carts/generate_otp'
  dataType: 'json'

paramAddToCartPlusAddonsDesignCod = (design_id_val,addons,variant,rakhi_note,standard_size=false,uiExp='mirraw') ->
  type: "POST"
  data:
    design_id: design_id_val
    addons: addons
    variant: variant
    rakhi_note: rakhi_note
    standard_size: standard_size
    cart_upsell: uiExp
    quick_cod_request: 'true'
  url: '/line_items'
  dataType: 'script'
  beforeSend: () ->
    $('.product_discription_div').css('opacity', '0.5')
    $('.progress_img1').show()
  complete: () ->
    $('.progress_img1').hide()
    $('.product_discription_div').css('opacity', '1')
    pincode = $('#order_billing_pincode').val()
    if (/\d{6}/g).test(pincode)
      $.ajax(paramFetchCityStateOptions(pincode, 'order_billing_city', 'order_billing_state'))
      $.ajax(paramCheckCashOptions($('#cart_final_id').val(), pincode))

paramFetchCityStateOptions = (pincode, city_selector, state_selector) ->
  type: 'GET'
  data:
    pincode: pincode
  url: '/api/pincode_info'
  datatype: 'JSON'
  success: (data, status, jqhxr) ->
   updateCityandStateOptions(data, pincode, city_selector, state_selector)

updateCityandStateOptions = (data, pincode, city_selector, state_selector) ->
  if data.fCity == "NA" || data.fCity == "Nil"
    $("##{city_selector}").val(data.fDistrict)
  else
    $("##{city_selector}").val(data.fCity)
  if typeof data.fState != 'undefined'
    $("##{state_selector}").val(data.fState).prop('selected', true);

$(document).on 'click', '.combo_variant', ->
  $('.combo_variant').removeClass 'selected'
  $(this).addClass 'selected'
  selectVariant(this)

$ ->
  $(document).on('click', '.add_to_cart_link', (e) ->
    $("#video").modal('hide') if $('#video').length > 0
    $("#stitchingModal").modal('hide') if $('#stitchingModal').length > 0
    $("#variantModal").modal('hide') if $('#variantModal').length > 0 && $('.variant').hasClass('selected')
    if checkVariantSelection() == true
      $('.product_discription_div').css('opacity', '0.5')
      $('.catalog2_facet').css('opacity', '0.5')
      $('.progress_img1').css('opacity', '1')
      $('.progress_img1').show()
      if checkAddons()
        if $(this).attr('data-design-id') != undefined
          design_id = $(this).attr('data-design-id')
        else
          design_id = $(this).attr('id')
        items = []
        if $('.combo_variant').length > 0
          combo_variant_id = $('.combo_variant.selected').attr('id')
        design_hash = {'design_id': design_id,combo_variant: combo_variant_id, 'variant': getVariant(), 'addons': getAddons(), 'rakhi_note': rakhi_note(), 'cart_upsell': checkExp()}
        if $('.variant, .variant_stitch').length > 0
          design_hash['standard_size'] = false
        else
          design_hash['standard_size'] = getStandardSize()
        items.push(design_hash)
        $('.addon_product:checked').each ->
          design_hash = {}
          design_hash['design_id']  = this.value
          design_hash['pair_product']  = true
          if $('#variant_select_'+this.value).length > 0
            design_hash['variant'] = $('#variant_select_' + this.value + ' .variant_modal.selected').attr('id')
          items.push(design_hash)
        $.ajax(paramAddToCartPlusAddons(items))
      else
        $('.progress_img1').hide()
        $('.product_discription_div').css('opacity', '1')
    else
      if $('.size-chart-div').is(':visible') && ('.combo_variant').length > 0  && $('.combo_variant.selected').length == 0 && $('.size.selected').length == 0 
        $('html, body').animate({scrollTop: $("#stitching_radio_ui").offset().top} , 500, ->
          $(".stitched").css('border','solid 1px white')
          $(".size-chart").addClass 'shake-effect'
          $('.size_error').slideDown()
          setTimeout (->
            $('.size-chart').removeClass 'shake-effect'
        ), 1000
        );
        $('.combo_variant_text').css('color', '#303030').addClass 'shake-effect'
      else if $('.size-chart-div').is(':visible') && $('.size.selected').length == 0
        $('html, body').animate({scrollTop: $("#stitching_radio_ui").offset().top} , 500, ->
          $(".stitched").css('border','solid 1px white')
          $(".size-chart").addClass 'shake-effect'
          $('.size_error').slideDown()
          setTimeout (->
            $('.size-chart').removeClass 'shake-effect'
        ), 1000
        );
      else if ('.variant').length > 0  &&  ('.combo_variant').length > 0 && $('.variant.selected').length == 0 && $('.combo_variant.selected').length == 0 
        $('#variantModal').modal('show') if $('#variantModal').is(':hidden')
        $('.variant_text').css('color', '#303030').addClass 'shake-effect'
        $('.combo_variant_text').css('color', '#303030').addClass 'shake-effect'
      else if ('.combo_variant').length > 0 && $('.combo_variant.selected').length <= 0
        $('#variantModal').modal('show') if $('#variantModal').is(':hidden')
        $('.combo_variant_text').css('color', '#303030').addClass 'shake-effect'
      else if ('.variant').length > 0 || $('.length_option').val() == ''
        $('#variantModal').modal('show') if $('#variantModal').is(':hidden')
        $('.variant_text').css('color', '#303030').addClass 'shake-effect'
  )
$ ->
  counter = 0
  $('.unstitch').click ->
    if $('#stitchingModal').length > 0 && $(this).is(':checked') && counter == 0
      $('#stitchingModal').modal('show')
      counter = 1

$(document).on('click', '.stitch', (e) ->
  $("#stitchingModal").modal('hide')
  $(".standard").attr('checked', true)
  $(".stitched").css('display','block')
)

paramFollowDesigner = (designer_id_val) ->
	type: "POST"
	data: 
	  designer_id: designer_id_val
	url: '/line_items'
	datatype: 'script'     

selectVariant = (item) ->
  $(item).addClass('selected')
  $variant_id = $(item).attr('id')
  $('#'+$variant_id).addClass('selected')

clearVariantSelections = () ->
  $('.variant').removeClass('selected')

$ ->
  $('.variant, .variant_stitch').on "click", ->
    $('.variant-price-text').html 'Price shown is for the size selected'
    clearVariantSelections()
    if $('.variant').length > 0
      selectVariant(this)
    change_displayed_design_price($(this).attr('data-old-price'), $(this).attr('data-price'), $(this).attr('data-delivery-date'), $(this).attr('data-ready-to-ship'))
    if $('.variant_stitch').length > 0
      $('.standard-stitch-variant').attr('selected-variant-price', $('.new_price_label').text())
      $('.standard-stitch-variant').attr('selected-variant-oldprice', $('.old_price_label').text())


$(document).on 'click', '.variant_modal', (e) ->
  $this = $(this)
  $design_id = $this.attr('value')
  $parent = $this.parents('#variant_select_'+ $design_id)
  if $('.variant_modal').length > 0
    $parent.find('.variant_modal').removeClass 'selected variant-selected'
    $parent.find(this).addClass 'variant-selected'
  change_displayed_design_price($this.attr('data-old-price'),$this.attr('data-price'),$this.attr('data-delivery-date'), $this.attr('data-ready-to-ship'), $design_id)

$ ->
  setTimeout (->
    $('#line_items_count').fadeOut 500
  ), 10000
  $('.line_item_close').click ->
    $('#line_items_count').fadeOut 1000

  #$('.size-left').click ->
  #  $('.size-chart-div').animate { scrollLeft: '-=' + 150 }, complete: ->
  #    if $(this).scrollLeft() == 0
  #      $('.size-left').fadeOut 300

  #$('.size-right').click ->
  #  $('.size-chart-div').animate {scrollLeft: '+=' + 150 }
  #  $('.size-left').fadeIn 300

  $('button.size').click ->
    $('.form-groups').hide()
    $('.button').removeClass('plus_size_regular_btn')
    hide1 = $('select', '.form-groups')
    if hide1.length > 0
      hide1.each ->
        $(this).find('option:eq(0)').prop('selected', true)
    $('.checkmark').removeClass('plus_size_fabric_selected')
    if (message = $('#salwar_kameez_specific'))
      size_selected = parseInt($(this).text(),10)
      message.find('var:first').text(size_selected + 2)
      message.find('var:last').text(size_selected + 3)
      message.show()
      $('#salwar_kameez_default').hide()
    $('.salwar_standard_atv').slideDown 300
    $('.size_error').slideUp()
    $('button.size').removeClass 'selected'
    $(this).addClass 'selected'
    $('.std_size_chart tr').removeClass('highlight')
    $(".std_size_chart td:first-child:contains('"+$(this).text()+"')").parent('tr').addClass('highlight')
    if $(this).data('delivery-date').length > 0
      estd_delivery = []
      prod_time = parseInt($(this).data('prod-time'))
      if prod_time == 0
        date_to_select = 'rts-date'
      else
        date_to_select = 'delivery-date'
      estd_delivery.push([prod_time,$(this).data('delivery-date'),$(this).data('ready-to-ship')])
      $('.radio_addons').each ->
        if $(this).is(':checked') && !$(this).hasClass('standard')
          estd_delivery.push([parseInt($(this).data('prod-time')),$(this).data(date_to_select),$(this).data('ready-to-ship')])
      max_delivery_date = estd_delivery.sort().pop()
      if max_delivery_date[2] == true
        $('.rts-label, del.strike_old_date').show()
      else
        $('.rts-label, del.strike_old_date').hide()
      $('.prod_time').html(max_delivery_date[1])

  #$('#sizes_size-inches').click ->
  #  $('.table-cm').fadeOut 'slow','linear',->
  #    $('.table-inches').fadeIn 'slow'

  #$('#sizes_size-cm').click ->
  #  $('.table-inches').fadeOut 'slow','linear',->
  #    $('.table-cm').fadeIn 'slow'


$ ->
  $('#show_rts_products').change ->
    if $(this).is(':checked')
      $.each $(this).data('rts-sizes'), (index, size_button_id) ->
        $('#'+size_button_id).css('border','2px solid #7b0e1d')
    else
      $('button.size').each ->
        $(this).css('border','1px solid #aba9a4')
      
root.state_change = () ->
  if $('.state-change').length > 0
    paramStateChange = (state,design_id,current_row,notes = null) ->
      type: "POST"
      data:
        design_id: design_id
        state: state
        notes: notes
      url: '/event_trigger/trigger_event_for_design'
      success: (data) ->
        if $(current_row).attr('data-refresh') == '0'
          if data.status == 'ok'
            $('#'+design_id+'_state').html(data.state)
            $('#'+design_id+'_notes').html(data.notes) if data.notes != undefined
        else if $(current_row).attr('data-refresh') == '1'
          location.reload()
        
    
    $('.state-change').on('click', (e) ->
      e.preventDefault()
      id = $(this).attr('data-id')
      state = $(this).val()
      notes = ''
      if state == 'reject'
        notes = $('#reason').val()
        if notes == '' or notes == undefined
          notes = prompt('Enter reason for rejecting design/s')
      $.ajax(paramStateChange(state,id,this,notes))
      
      )
    $ ->
      $(".unpublish_designs_checkbox_all").click ->
        $("input").attr('checked', 'true')    

    $ ->
      $(".unpublish_designs_checkbox_none").click ->
        $("input").removeAttr('checked')

$ ->
  $(document).ready ->
    state_change()

$ ->
  if $('.remove-variants').length > 0
    paramRemoveVariants = (design_id,designer_id) ->
      type: 'GET'
      url: "/designers/"+designer_id+"/designs/"+design_id+"/remove_variants"
      success: (data, status, jqhxr) ->
        if data.errors != undefined && data.error_text
          new PNotify({text: data.error_text, styling: 'bootstrap3'})
        else
          new PNotify({text: data.message, type: 'success', styling: 'bootstrap3'})
        location.reload()
  
    $('.remove-variants').on('click', (e) ->
      e.preventDefault()
      design_id = $(this).attr('data-id')
      designer_id = $(this).attr('data-designer-id')
      $.ajax(paramRemoveVariants(design_id,designer_id))
    )

$ ->
  if $('.remove_single_variant').length > 0
    $('.remove_single_variant').on('click', (e) ->
      e.preventDefault()
      variant_id = $(this).attr('data-variant-id')
      $.post "/designers/"+$(this).attr('data-designer-id')+"/designs/"+$(this).attr('data-id')+"/variants/"+variant_id+"/remove_variants", {},
      (data) ->
        if data.errors != undefined && data.error_text
          new PNotify({text: data.error_text, type: 'error', styling: 'bootstrap3'})
        else
          $('#variants-panel #variant_row_'+variant_id).hide()
          $('#variants-panel #variant_row_'+variant_id).attr('class','new_variants')
          $('#variants-panel #variant_row_'+variant_id).removeAttr('id')
          $('#design_variant_quantity_'+variant_id).val(0)
          $('input[type="hidden"][value="'+variant_id+'"]').remove()
          new PNotify({text: data.message, type: 'success', styling: 'bootstrap3'})
    )

$ ->
  if $('.add-new-variants').length > 0
    $('.add-new-variants').on('click', (e) ->
      e.preventDefault()
      $('#variants-panel .new_variants').css('display','block')
      $('.add-new-variants').css('display','none')
    )

$ ->
  prod_id = $('#specif_product_id').data('productId')
  if (typeof prod_id != "undefined" && prod_id != null)
    ga_event_action = if $('.sold_div').length > 0 then 'out_of_stock_product_view' else 'in_stock_product_view'
    # ga('send', 'event', 'Product View', ga_event_action, prod_id, {'nonInteraction': 1})
  $('#design_discount_percent').keyup ->
    if $(this).val() != ''
      $('#new_price').val Math.floor(parseInt($('#design_price').val()) * (100 - parseInt($('#design_discount_percent').val())) / 100)
    else
      $('#new_price').val parseInt($('#design_price').val())
    return

  $(window).on 'load', ->
    if $('#all_designer_designs').length > 0
      design_id = $('#unbxd_data_div').attr('pid')
      path = window.location.pathname + '/more-designers'
      $.ajax
        url: path
        type: 'GET'
        datatype: 'script'
        data: id: design_id
        success: (data) ->
          data
          return
        error: (xhr, status, error) ->
          return

$('.collapse').on "click", ->
  if !$('.product_specif_detail').is ':visible'
    $('.add').html '-'
  else
    $('.add').html '+'
  $('.ui-tabs-panel').slideToggle()

$ ->
  if $('.variant_div').length > 0
    $('.variant').each ->
      if $('.new_price_label:eq(0)').text() == $(this).attr('data-price')
        $('.variant-price-text').html "The price shown is for size : "+ $(this).text()
        return false
$ ->
  $('.aov_select, .radio_addons, .addon_size').change ->
    $('#stitch_prefer_notice').hide()

$ ->
  if typeof($('[data-toggle=\"tooltip\"]').tooltip) == 'function'
    $('[data-toggle=\"tooltip\"]').tooltip()

openSizeModal = (size_modal) ->
  size_modal.addClass 'size-modal-visible'

changeItemTotal = do ->
  addon_total = 0
  ($this) ->
    #here $this is addon product checkbox
    $this_design_id = $this.attr('value')
    $parent_addon_block = $this.parents('.addon-block')
    $variant_modal = $('#variant_select_'+ $this_design_id)
    $design_price = parseFloat($('.original-product-price').text().split(" ")[1])
    $design_addon_price = parseFloat($parent_addon_block.find('.new_price_label_' + $this_design_id).text().split(" ")[1])
    $addon_total = parseFloat(addon_total.toFixed(2))
    $item_quantity = $('.addon_product:checked').length
    $item_count =  $item_quantity + 1
    if addon_total >= 0
      if $this.is(':checked')
        $parent_addon_block.find(".added-item-label").fadeIn()
        addon_total = $addon_total + $design_addon_price
      else
        addon_total = $addon_total - $design_addon_price
      total_amount = $design_price + addon_total #check design price change
    if addon_total <= 0
      $('.before-add-msg').show()
      $('.product-details').css 'display' , 'none'
      $('.add_multi_item').removeClass 'add-multi-item-enable'
      $('.item-count, .item-quantity').html ''
    else
      $('.before-add-msg').hide()
      $('.product-details').css 'display' , 'inline-flex'
      $('.added-items-price').text addon_total.toFixed(2)
      $('.total-price').text total_amount.toFixed(2)
      $('.add_multi_item').addClass 'add-multi-item-enable'
      $('.item-count').html $item_count + ' ITEMS'
      $('.item-quantity').html $item_quantity

closeSizeModal = (size_modal) ->
  $addon_product = $('#addon_product_'+ size_modal.attr('value'))
  if size_modal.length > 0 && !size_modal.find('.variant_modal').hasClass('selected') || size_modal.find('.variant_modal').hasClass('variant-selected')
    $addon_product.prop 'checked' , false
  $('.variant-bg').removeClass 'variant-bg-enable'
  size_modal.removeClass 'size-modal-visible'

$(document).on 'click', '.addon_product', (e) ->
  $this = $(this)
  $parent_addon_block = $this.parents('.addon-block')
  size_modal = $('#variant_select_'+this.value)
  if size_modal.length > 0
    if $this.is(':checked') # check if variant is present open selection modal
      if $('.size-modal-visible').length == 1
        design_id = $('.size-modal-visible').attr('value')
        $addon_product_block = $('#addon_product_'+design_id)
        $parent = $('#variant_select_'+ design_id)
        closeSizeModal($parent)
        if $parent.find('.variant_modal').hasClass('selected')
          changeItemTotal($addon_product_block)
      $parent_addon_block.children('.variant-bg').addClass 'variant-bg-enable'
      openSizeModal(size_modal)
  else
    ga 'send', 'event', 'complete_the_look', 'addon_product_view', this.value
  if !$this.is(':checked')
    $parent_addon_block.find(".added-item-label").hide() #remove 'item added' label if remove item clicked
  if size_modal.length <= 0 || (!$this.is(':checked') && size_modal.length > 0)
    changeItemTotal($this)

$ ->
  if $('.addon-block').length > 0
    $('.addon_product:checked').each ->
      $this = $(this)
      $this.find('.added-item-label').show()
      size_modal = $('#variant_select_'+this.value)
      if size_modal.length > 0
        $this.prop 'checked' , false
      else
        changeItemTotal($this)

$(document).on 'click', '.select-addon-variant', (e) ->
  $this = $(this)
  $this_design_id = $this.attr('value')
  $parent = $this.parents('#variant_select_'+ $this_design_id)
  $addon_product_block = $('#addon_product_'+$this_design_id)
  $parent.find('.variant_modal').each ->
    if $(this).hasClass('variant-selected')
      $parent.find('.variant_modal').removeClass 'selected variant-selected'
      $(this).addClass 'selected'
      ga 'send', 'event', 'complete_the_look', 'addon_product_view', $this_design_id
  closeSizeModal($parent)
  if $parent.find('.variant_modal').hasClass('selected')
    changeItemTotal($addon_product_block)

$(document).on 'click', '.variant-bg, .close-variant-select', (e) ->
  $this = $(this)
  $this_design_id = $this.attr('value')
  $addon_product_block = $('#addon_product_'+$this_design_id)
  $parent = $('#variant_select_'+ $this_design_id)
  closeSizeModal($parent)
  if $parent.find('.variant_modal').hasClass('selected')
    changeItemTotal($addon_product_block)

  $(document).on('click', '.loyalty-promo-tnc, .bmgnx-promo-tnc, .qpm-promo-tnc, .rts-promo-tnc', ->
    name = this.className
    animateTnc($(this))
    $('.' + name).children('.design_promo_tnc_in_detail').show()
  )
  if $('.available-offer-slider').length > 0 && $('.available-offer-slider')[0].scrollWidth > $('.available-offer-slider').width()
    $('span#scroll-btn-left').fadeIn 300
    $(document).on 'click','span#scroll-btn-left', ->
      $('.available-offer-slider').animate {scrollLeft: '+=' + 180}, complete: ->
        available_scroll = $('.available-offer-slider')[0].scrollWidth - $('.available-offer-slider').width()
        if available_scroll - $(this).scrollLeft() == 0
          $('span#scroll-btn-left').fadeOut 300
      setTimeout (->
        $('span#scroll-btn-right').fadeIn 300
      ), 100

    $(document).on 'click','span#scroll-btn-right', ->
      $('.available-offer-slider').animate {scrollLeft: '-=' + 180}, complete: ->
        if $(this).scrollLeft() == 0
          $('span#scroll-btn-right').fadeOut 300
      setTimeout (->
        $('span#scroll-btn-left').fadeIn 300
        ), 100


$(document).on 'click', '.button', (e) ->
  $('.form-groups,.salwar_standard_atv').show()
  $('.button').addClass('plus_size_regular_btn')
  $('.size').removeClass 'selected'  
  
$(document).on 'click', '.plus_size_custom_regular', (e) ->
  $('.dropdown-div[data-name="Select Plus Size"]').hide()
  $('.form-group2').hide()
  hide2 = $('select', '.dropdown-div,.form-group2')
  if hide2.length > 0
    hide2.each ->
      $(this).find('option:eq(0)').prop('selected', true)
  $('.checkmark').removeClass('plus_size_fabric_selected')
  $('.plus_size_custom_regular').toggleClass('selected_custom_plus_size').toggleClass('other_custom_plus_size')
  $('.plus_size_custom').toggleClass('selected_custom_plus_size').toggleClass('other_custom_plus_size')

$(document).on 'click', '.checkmark', (e) ->
  $('.checkmark').removeClass('plus_size_fabric_selected')
  $(this).addClass('plus_size_fabric_selected')

$(document).ready ->
  $('.dropdown-div[data-name="Select Plus Size"]').hide()
  $('.plus_size_custom').click ->
    $('.dropdown-div[data-name="Select Plus Size"]').show()
    $('.form-group2').show()
    $('.plus_size_custom_regular').toggleClass('selected_custom_plus_size').toggleClass('other_custom_plus_size')
    $('.plus_size_custom').toggleClass('selected_custom_plus_size').toggleClass('other_custom_plus_size')
    return
  return


