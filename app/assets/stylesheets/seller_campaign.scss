#campaigns-popup-modal {
  text-align: center;
  
  .modal-dialog {
    max-width: 100vw;
    overflow-x: hidden;
    margin: auto;
    display: flex;
    justify-content: center;
    min-height: calc(100% - 1rem);
  }

  .modal-content {
    width: 100%;
    margin: auto;
    background: rgba(217, 210, 210, 0.95);
    box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius:0px;
  }

  .modal-header {
    background: linear-gradient(to right, #67141A, #9B1D2C);
    color: white;
    padding: 15px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .modal-title {
      display:block;
      width:100%;
      font-size: 20px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 1px;
      text-align: center;
    }

    .close {
      background: #d092a7;
      color: #1f1d1e;
      border-radius: 50%;
      border: none;
      box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
      padding: 5px 9px;
      font-size: 16px;
      position: absolute;
      top: 14px;
      right:6px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease-in-out;
      
      &:hover {
        color: red;
        background: #fff;
        transform: scale(1.1);
      }
    }
  }

  .modal-body {
    padding: 25px;
    width: 100%;
  }

  .campaign-card {
    background: #EDEDED;
    padding: 5px;
    border-radius: 8px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    margin-bottom: 15px;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;

    &:hover {
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    }

    h3 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
      color: #333;
      text-transform: capitalize;
    }

    p {
      font-size: 14px;
      color: #666;
      margin-bottom: 0;
      line-height: 1.4;
    }

    .participateButton {
      width: 150px;
      height : 40px;
      transition: transform 0.3s ease-in-out; 
      cursor : pointer;
    }
  }
}

#campaigns-carousel-banner {
  width: 100%;
  height: 50px;
  position: relative;
  margin-top: -100px;
  margin-bottom: 10px;
  overflow: hidden;
  background: linear-gradient(270deg, #9e9ef4, #141c61, #ff4b1f, #737ee2);
  background-size: 300% 300%;
  opacity: 0;
  animation: gradientShift 6s infinite alternate, fadeIn 1s ease-in forwards;
  animation-timing-function: ease-in-out;

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .carousel-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .carousel-track {
    display: flex;
    height: 100%;
    width: 500%;
    animation: slide 25s infinite linear;
  }

  .carousel-item {
    flex: 0 0 20%;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    cursor: pointer;
  }

  .campaign-header {
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 20px;
    letter-spacing: 1px;
  }

  #closeBtn {
    position: absolute;
    top: -6px;
    right: -6px;
    background: none;
    border: none;
    color: rgb(219, 193, 193);
    font-size: 20px;
    cursor: pointer;
    z-index: 10;
    font-size: 18px;

    &:hover {
      color: rgb(255, 255, 255);
      transform: scale(1.1);
      transition: transform 0.2s ease-in-out;
    }
  }
}

@keyframes slide {
  0% { transform: translateX(0); }
  100% { transform: translateX(-80%); }
} 
