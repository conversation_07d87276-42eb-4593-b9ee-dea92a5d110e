@import 'jquery.fancybox';
@import 'jquery.fancybox-buttons';
@import 'jquery.fancybox-thumbs';
@import 'variables';
@import 'mixins';

$white_font: #fff;
$grey_border:#aba9a4;
$mobile_device:767px;
$link_color:#a6bbcc;

.info_message{
  border-radius: 50%;
  background-color: #303030;
  padding: 0px 6px;
  font-size: 12px;
  font-style: italic;
  font-weight: 700;
  font-family: Times New Roman;
  cursor: pointer;
  color: #ffffff !important;
}

table.table-bordered-radius{
  border-radius: 10px;
  box-shadow: 0px 0px 10px #666;
  tr {
    &:first-child{
      td,th{
        border-top:none;
        &:first-child{
          border-radius: 10px 0px 0px 0px;
        }
        &:last-child{
          border-radius: 0px 10px 0px 0px;
        }
      }
    }
    &:last-child{
      td,th{
        &:first-child{
          border-radius: 0px 0px 0px 10px;
        }
        &:last-child{
          border-radius: 0px 0px 10px 0px;
        }
      }
    }
    th{
      color: #333; 
    }
    td{
      color: #444;
      &:first-child{
        font-weight: bold;
        color: #333;
      }
    }
    td, th{
      text-align: center;
    }
  }
}
#quick-cod-form-link-text{
  font-size: 16px;
  letter-spacing: 1px;
  line-height: 40px;
  #quick-cod-form-link{
    display: inline;
    padding: 0;
    color: $light-red-text;
    background: 0;
    border: 0;
    font-weight: bold;
    text-decoration: underline;
  }
}
#quickCodOrderModal{
  background: #e4e1e15c !important;
  z-index: 10500;
  .modal-dialog{
    margin: 42px !important;
  }
  .modal-content{
    border-radius: 0px !important;
    padding-bottom: 3%;
    background: $white !important;
  }
  label.error{
  color: red;
  float: left;
  font-weight:  unset;
  width: 100%;
  margin-top: 1%;
  margin-bottom: 0%;
  text-align: left;
  }
  .modal-body{
    margin-top: 2%;
  }
  .modal-title{
    float: left;
    margin-left: 3%;
    .small-header-message{
      font-size: 0.9em;
      display: inline;
      color: #b3666d;
    }
  }
  #quick-cod-order-button{
    padding: 0;
    border: 0;
    margin: 0;
    background-color: inherit;
    margin-top: 29px;
    background: $shop-now-button-color;
    color: white;
    padding: 18px 36px;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 16px;
  }
  #codFormSuccessButton{
    display: none;
  }
  #cart-detail-for-quick-cod{
    margin-top: 12px;
    background: #eee;
    width: 100%;
    padding: 2%;
    border: 1px solid #bdb9b9;
    #design-image-quick-cod{
      padding: 1%;
    }
    #order-detail-quick-cod{
      #order-detail-heading-quick-cod{
        padding: 2%;
        text-align: center;
        font-size: 1.3em;
      }
      .order-detail-elements-quick-cod{
        padding: 1%;
        font-size: 1.1em;
        margin-bottom: 1%;
      }
      .order-detail-elements-quick-cod:last-child{
        font-size: 1.2em;
        font-weight: 600;
        color: $global-text-color;
        margin-top: 3%;
      }
    }
  }
  .modal-header{
    text-align: center;
  }

  #otp-sent-message{
    color: $global-text-color;
    margin-bottom: 7%;
    line-height: 1.3em;
  }
  .error-msg{
    color: red;
    margin: 1%;
    display: none;
  }
  #otp-form-and-content{
    margin: 7%;
    text-align: center;
    display: none;
    #phone-text-for-otp{
      display: inline;
    }
    #otp-val{
      text-align: center;
      margin-left: auto;
      margin-right: auto;
      width: 50%;
    }
    #quick-cod-otp-submit{
      width: 40%;
      font-size: 1.5em;
    }
  }
  #cod-not-avail-msg{
    color: #b3666d;
    line-height: 1.5em;
    padding: 2%;
    display: none;
  }
  input[type=text], select, input[type=email]{
    width: 85%;
  }
  .close_cod_modal{
    font-size: 2em;
    color: $global-text-color;
    opacity: 0.6;
  }
  .button-as-text{
    display: inline;
    background: 0;
    border: 0;
    text-decoration: underline;
    color: steelblue;
  }
  #cart-detail-content{
    border: $light-border-box;
    padding-bottom: 3%;
    padding-top: 1%;
    background: #eee;
  }
  #quick-cod-form-container{
    text-align: center;
    padding-bottom: 1%;
    margin-right: 2%;
    margin-left: 3%;
    padding-top: 2%;
    border: $light-border-box;
    #quick-design-cod-form{
    }
    #quick-design-cod-form{
    }
  }
  #quick-cod-form-submit{
  }
}
.thumb_images{
    margin-bottom: 5px;
    float: left;

    a{
        display: block;
     }
}
#play_video{
  .play_button{
    position: absolute;
    color: $white;
    bottom: 24px;
    font-size: 25px;
    background-color: rgba(71, 56, 56, 0.73);
    width: 74px;
    height: 74px;
    padding: 25px;
    left: 1px;
    top: 1px;
  }
}
.heading_heart{

    margin-bottom: 15px;
    h3{
        font-size: 30px;
        letter-spacing: 1px;
        color:$global-text-color ;
        line-height: 33px;
    }
}
/* shake effect */
.shake-effect {
  -webkit-animation-duration: 0.75s; 
  animation-duration: 0.75s; 
  -webkit-animation-fill-mode: none; 
  animation-fill-mode: none;
  -webkit-animation-name: shake; 
  animation-name: shake; 
  }
 @keyframes shake { 
    0%, 100% {transform: translateX(0);color: #ed8f03;} 
    20%, 60% {transform: translateX(-5px);color: #ed8f03;} 
    40%, 80% {transform: translateX(5px);color: #ed8f03;} 
  }
  @-webkit-keyframes shake {
    0%, 100% {-webkit-transform: translateX(0);color: #ed8f03;} 
    20%, 60% {-webkit-transform: translateX(-5px);color: #ed8f03;} 
    40%, 80% {-webkit-transform: translateX(5px);color: #ed8f03;} 
  }
.discount_old_price{

  margin-right: 20px;
}
.variant-price-text{
  margin-top: 10px;
}
.discount_percent{
background: $label_light_red;
font-size: 16px;
color: #fff;
padding: 7px;
border-radius: 3px;
margin-right:2px;
}

.old_price_label{
  text-decoration: line-through;
  font-size: 16px;
  letter-spacing: 1px;
  color: $global-text-color;
  margin-top: 6px;
  margin-right: 10px;
  line-height: 18px;
}
.old_price{
  // text-decoration: line-through;
  font-size: 16px;
  letter-spacing: 1px;
  color: $global-text-color;
  margin-top: 6px;
  margin-right: 5px;
  line-height: 18px;
}

.design-cert{
  width: 60px;
  height: 60px;
  background: inherit;
}
.heart_div{

    display:none;
}

.heart_div a,
.heart_div input[type="image"]{
    background:image-url('heart.png') no-repeat;
    height:40px;
    width:43px;
    display: block;
    display: inline-block;

}

.heart_div a:hover,
.heart_div input[type="image"]:hover{
    background:image-url('heart_fill.png') no-repeat;
}

.variant_text{
 font-size: 20px;
 color: $global-text-color;
 font-weight: bold;
}

.combo_variant_text{
  font-size: 20px;
  color: $global-text-color;
  font-weight: bold;
}

.variant_select{
  font-size: medium;
}

.variant_div ul li{
  padding: 8px 15px;
  border-color: #aba9a4;
  border-style: solid;
  border-width: 1px;
  cursor: pointer;
  display: inline-block;
  margin-right: 5px;
  margin-top: 7px;
  margin-bottom: 7px;
}

html.gecko .variant_div ul li{
   margin-right: 7px;
}

.variant_div ul li:hover{
  background: $light-red-background;
  color: $white;
}
.variant_div ul li.selected{
  background-color: $light-red-background;
  color: $white;
  border-color: $light-red-background;
}

.combo_variant .selected{
  background-color: $light-red-background;
  color: $white;
  border-color: $light-red-background;
}

.chosen-select{
    margin-bottom: 3px;
    width: 60% !important;
    line-height: 24px;
    height: 24px;
    font-size: 13px;
    color: black;
    margin-left: 5px;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
}

#collection_block{
  a{
    font-size: 14px;
    text-shadow: 0px 0px 2px #851522;
    color: $background-color !important;
    line-height: 30px;
    text-decoration: underline;
  }
}
.review-end-line{
  border: $light-border-box !important;
}

#dynamic-size-chart{
  display:block;
  margin:0 !important;
  clear: both;
}
.cod_availability_message{
  margin-top: 10px;
}
.check_cod_form{
  .input-group{
    width: 100%;
    #pincode{
      height: 28px;
      border-radius: 0px;
      line-height: 24px;
      letter-spacing: 1px;
    }
  }
  #check_for_cod{
    border: 0px;
    border-radius: 0px;
    background: #b8b5b6;
  }
}

.check_pdd_form{
  .input-group{
    width: 100%;
    #pincode{
      height: 28px;
      border-radius: 0px;
      line-height: 24px;
      letter-spacing: 1px;
    }
  }
  #check_for_pdd{
    border: 0px;
    border-radius: 0px;
    background: #b8b5b6;
  }
}

.estimated_delivery{
  font-size: 16px;
  line-height: 18px;
  color: rgb(48,48,48);
  .text{
    margin-left: 0px !important;
  }
  del.strike_old_date {
    color: gray;
  }
  .prod_time {
    margin-left: 5px;
    display: inline-block;
  }
  .buy_this_now_button{
    margin-top: 18px;
  }
  .pre-order{
    padding: 7px 10px;
    border: 1px dashed #c11d54;
    color: #dedede;
    font-size: 17px;
    .pre-order-date{
      label{
        font-size: 12px;
        font-weight: 100;
        color: #9da4a4;
      }
    }
  }
  .key_specifications{
    font-size: 14px;
    margin-top: 0px;
    margin-left: 0px !important;
    .text{
      font-weight: bold;
    }
    table{
      tr td{
        padding: 0.3rem;
        padding-bottom: 0.2rem;
      }
    }
    .more_specifications_link{
      margin-top: 0px;
      text-align: right;
      a{
        font-size: 14px;
        color: #e91e63 !important;
      }
    }
  }
}

.design_quantity .sold_div{
background-color: #474845;
display: block;
float: none;
padding: 5px;
width: 84%;
}

.design_quantity .sold_div h5{
  line-height: 25px;
  color: $white;
  text-align: center;
}

.request_item{
margin-top: 10px;
margin-bottom: 5px;
font-size: 1em;
border-radius: 5px 5px 5px 5px;
border: 1px dotted red;
display: block;

padding: 5px;
text-align: center;
width: 84%;

}

.email_div .email{
color:#fff;
text-align: center;
font-size: 19px;
margin-bottom: 5px
}

.email_div #email{
height: 23px;
border-radius: 4px;
border: 1px solid grey;
font-size: 13px;
padding: 0 5px;
margin-bottom: 15px;
width: 82%;
}

.request_item_button {
  margin-bottom: 5px;
}

.product_specifications_div{
    margin-top: 50px;
}

.product_specifications_div
{
     margin-top: 25px;
    .Product_Specifications{
        h5{
            color:$grey_border;
            font-size: 18px;
            font-weight: bold;
            font-weight: normal;
            padding-bottom: 5px;
        }

    }
    .divider{
        margin-top:5px;
        margin-bottom:5px;

    }
}

.design_specif_detail{
color: $grey_border;
padding-top: 10px;
font-size:13px;
}

table.product_specif_detail{
  background: white;
  margin: 0px;
  border: 1px solid #959595 !important;
  th,td{
    border: 1px solid #959595 !important;
  }
  th.head-left{
    width: 65%;
  }
  
  ol#addon-values-description {
    list-style-type: decimal;
    margin-left: 13px;
    
    li {
      color: #303030 !important;
      background-color: white;
    }
    
    li:hover {
      background-color: white;
    }
  }
}

ul.product_specif_detail{

    padding-bottom: 10px;

    li{
        margin-bottom: 5px;
        font-size: 14px;
        line-height: 18px;

        label{
          color: $grey_border;
          width: 105px;
          display: inline-block;
        }
        span{
           color: $grey_border;
           a{
            color: #a6bbcc !important;
            text-decoration: none;
           }
       }
    }
}

.recently_viewed {
    margin-top: 15px;
   .divider{
    margin-top: 5px;
    margin-bottom: 5px;
   }
}
.zoomContainer{
  z-index: 9;
}


#recently_viewed_box .bx-wrapper{
max-width: 985px !important;
margin-top: 15px !important;
}

#recently_viewed_box h5, .similar_items_images_div h5{
    //   font-size: 18px; ---- changed according to all other widgets
    // letter-spacing: 1px;
    // line-height: 14px;
    // color: $white;
    // font-family: "Lato";
    // font-weight: bold;
    // padding: 15px;
    // padding-left: 90px;

    padding: 10px;
    color: #303030;
    border: 1px solid #eeeeee;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
    margin-top: -40px;
    background: #ffffff;
    text-align: center;
    text-transform: uppercase;
    font-size: 16px;

  @media screen and (max-width: $mobile_device){
    font-size: 14px;
  }
}

.similar_items_images_div {
  padding-top:0px !important;
  margin-top:0px !important;
}

#recently_viewed_box #recently-viewed-product-carousel {
    background: none;
    // background: image-url('similar_bg.png') repeat; -- changed acco to all other widgets
    // background-color: #901824;
    // height:320px !important;
    // margin-top: 25px;

    @media screen and (max-width: $mobile_device){
        margin-bottom: 20px;
    }
}

#recently_viewed_box .bx-wrapper .bx-viewport{
  box-shadow:none;
  border:0px;
  left: 0px;
  background: none;
  img{
    border: $light-border-box;
  }
}

.slide a{
display: block;
}

// #recently_viewed_box .bx-wrapper .bx-controls-direction{ -- changed according to all other widgets
//   a{
//   z-index: 10 !important;
//   top:38% !important;
//   width: 34px !important;
//   height: 32px !important;
//   border: none;
//   }
//   .bx-prev{
//     background: image-url('prev_btn.png') no-repeat !important;
//     transform: none !important;
//     left: -98px !important;
//   }
//   .bx-next{
//     background: image-url('prev_btn.png') no-repeat !important;
//     transform: rotate(180deg);
//     right: -99px !important;
//   }
// }

#recently_viewed_box{
  padding-left: 0px;
  padding-right:0px;
}

#recently_viewed_box{
  .title{
    margin-top: 6px;
    letter-spacing: 1px;
    line-height: 18px;
    font-family: "Lato";
    @include truncate;
  }
  .title_wrap{
  font-size: 14px;
  margin-top: 6px;
  text-transform: capitalize;
  }
}

.wholesale_div h5{
  padding-bottom: 10px;
}

.wholesale_div{
    ul{
        margin-top: 20px;
    }
    h5{
        a{
           color:$link_color !important;
           text-decoration: none;
           font-size: 14px;
        }
    }
}

.wholesale_list{
    li{
        float:none;
        text-align: center;
    }
}
.custom_table {
  display: flex;
}
.custom_table table td {
  border: 2px solid;
}
.notice{
  background: #fff6bf;
  padding: 9px;
  border: 2px solid #ffd324;
  margin-top: 10px;
  margin-bottom: 10px;
}

.notice p{
  color: #514721;
  line-height: 20px;
}

.notice a{
  color: red !important;
  font-size: 16px;
}

.similar_items_images_div, .design_images_div{
    margin-top: 20px;

    h5{
        margin-bottom: 10px;

        a{
            text-decoration: none;
            color: $link_color !important;
            font-size: 18px;
        }
    }
    .similar_items_images{
        li{
            text-align: center;

            @media screen and (max-width: $mobile_device){
                float: left;
                width: 110px;
            }



            a{
                display: inline-block;

                img{
                    @media screen and (max-width: $mobile_device){
                        width: 100%;
                    }
                }

            }

        }
    }

    .design_images{
        li{
            margin-right: 10px;
            text-align: center;

            @media screen and (max-width: $mobile_device){
                float: left;
                width: 225px;
            }



            a{
                display: inline-block;

                img{
                    @media screen and (max-width: $mobile_device){
                        width: 100%;
                    }
                }

            }
            img{
                  @media screen and (max-width: $mobile_device){
                      width: 100%;
                  }
              }

        }
    }
}


@media screen and (max-width: 320px){
  .right_description_wrapper .buy_this_now_button .green_button{
    width: 230px !important;
  }
  .small_device_img {
    width: 188px !important;
    margin-bottom:40px;
  }
}

@media screen and (max-width: 760px){
  .big_button a,
  .big_button input[type="submit"]{
    background-position: 185px 14px;
    background-size: 26px;
  }
}

.similar_items{
    margin-top: 10px;
}

.similar_items_images{
    @media screen and (max-width: $mobile_device){
        width: 840px;
    }

    li{
        margin-bottom: 10px;
        a{
            img{
                margin-bottom: 2px;
            }
        }
        div{
            color:$grey_border;
            font-size: 14px;
        }
    }
}
.zoomWindow{
  border: 1px solid !important;
}
.fancybox-thumbs{
  height: 80px !important;
  img{
    background: $white !important;
    border: $light-border-box;
    padding: 12px;
    &:hover{
      border: 1px solid
    }
  }
}
.fancybox-thumbs.active{
  img{
    border: 1px solid;
  }
}
#fancybox-buttons ul {
width: 100px !important;
}

#fancybox-thumbs ul li.active {
opacity: 1 !important;
padding: 0;
border: 1px solid #FF0808 !important;
}

#fancybox-thumbs ul li {
    clear: both !important;
    margin-left: 10px !important;
    padding: 1px;
    opacity:1 !important;
    border: 1px solid #fff;
    margin-top:5px !important;
}


.addthis_toolbox {
    width: 30%;
    padding-left:15px;
    padding-top:3px;

    @media screen and (max-width: 1280px){
        width: 30%;
    }
    @media screen and (max-width: 1199px){
        width: 35%;
    }
    @media screen and (max-width: 991px){
        width: 50%;
    }
}
.head{
  padding: 0px !important;
  .heading{
    h1{
    font-size: 16px;
    line-height: 25px;
    color: $global-text-color;
    font-weight: bold;
    text-transform: uppercase;
    }
  }
}
/* modal designing */

#buy_wholesale{
  font-size: 14px;
  color: #fff;
  line-height: normal;

  .modal-dialog {
    width: 60%;
  }

  @media screen and (min-width: 768px) and (max-width: 1182px){
      .modal-dialog{
          width: 62%;
      }
  }

  .item_title {
    font-size: 24px;
    line-height: normal;
  }

  .input-group{
    width: 100% !important;

    label {
      display: inline-block;
      float: left;
      width: 65px;
      text-align: left;
      margin-right: 15px;
      margin-top: 6px;
      margin-bottom: 0;
      font-weight: normal;
    }

    input[type="text"] {
      float: left;
      height: 28px;
      width: 81%;
      background-color: #ccc;
      border: 0px;
      border-radius: 4px;
      color: #000;
      padding: 10px;
    }

    textarea {
      float: left;
      width: 81%;
      height: 100px;
      background-color: #ccc;
      border: 0px;
      border-radius: 4px;
      color: #000;
      padding: 10px;
    }

    input[type="submit"] {
      background-color: #4B4949;
      color: #A8A7A7 !important;
      padding: 10px;
      border: 0px;
      border-radius: 4px;
      font-size: 14px;
      display: inline-block;
    }

    input[type="submit"]:hover {
      background-color: #555454;
    }
  }

  .submit_button{
    margin-left: 80px;
  }
}

html.ie7 #buy_wholesale .input-group input[type="text"],
html.ie8 #buy_wholesale .input-group input[type="text"]{
  height: auto;
}


/* modal designing */

#add_to_favorite{
  font-size: 14px;
  color: #fff;
  line-height: normal;

  .modal-dialog {
    width: 60%;
  }

  @media screen and (max-width: 767px){
    .modal-dialog{
        width: 100%;
    }

    .modal-content .modal-header{
      border: 0;
      padding: 0 20px;
      text-align: left;

      div.title_favorite{
        text-align: left;
      }
    }

    .modal-content .modal-body{
      padding: 20px;
    }
  }

  .title_favorite{
    font-size: 24px;
    line-height: normal;
  }

  table{
    width: 100%;
  }

  table tr td{
    padding: 0;
    text-align: center;
    vertical-align: middle;

    @media screen and (max-width: 479px){
      float: left;
      clear: both;
      padding-bottom: 15px;    
    }
  }

  ul li{
    background: image-url('check.png') no-repeat left top;
    padding: 1px 0 10px 32px;
    display: block;
    font-size: 14px;
    color: #fff;
    text-align: left;
    line-height: normal;
  }

  ul li:last-child{
    padding-bottom: 0;
  }
}

.pluginFontHelvetica td{
  color: #fff !important;
}

@media screen and (max-width: 1200px){
  html.gecko .variant_div ul li{
    margin-right: 5px;
  }

  .variant_div ul li{
    padding: 3px 14px;
  }

  .big_button a,
  .big_button input[type="submit"]{
    background-position: 185px 14px;
    padding-left: 6px;
    width: auto;
  }

  .big_button input[type="submit"]{
    width: 245px;
  }

  .green_button a{
    display: block;
  }
  
}

@media screen and (max-width: 991px){
  html.gecko .variant_div ul li {
    margin-right: 5px;
  }

  .variant_div ul li {
    padding: 3px 15px;
  }
}

/*@media screen and (max-width: 767px){
  .right_description_wrapper{
    width: 245px;
  }

  .right_description_wrapper .chosen-select{
    width: 239px !important;
  }

  .right_description_wrapper .design_quantity .sold_div{
    width: 97% !important;
  }

  .right_description_wrapper .request_item{
    width: 97% !important;
  }

  .right_description_wrapper .green_button{
    width: 97% !important;
  }

  .right_description_wrapper .big_button a{
    background-position: 190px 14px;
    padding-left: 10px;
  }

  .right_description{
    padding-left: 0;
    padding-right: 0;
    min-width:240px !important;
  }

  .right_description_wrapper{
    float: none;
    margin: 0 auto;
  }
}*/

html.ie7 .right_description_wrapper .green_button{
  width:100%;
}


#fancybox-thumbs{
  // display: none !important;
  position:absolute;
  top:0px !important;
  left: 0px !important;
}


.fancybox-skin{
  padding-left:80px !important;
}

@media screen and (max-width: 767px){
  .right_description_wrapper .buy_this_now_button .green_button{
    width: 225px !important;
  }
  .small_device_img {
    width: 225px !important;
    margin-bottom:40px;
  }
}

.green_button{
  width: 247px !important;
}  

li.variant_block {
  border-style: none;
  background: inherit;
}

.addon_block{
  .list-group{
    margin-bottom: auto;
  }
}

.addon_block{

  .list-group-item{
    padding: 10px;
    background: inherit;
    border-style: none;

    .control-label{
      text-align: inherit;
    }
  }
}

.form-mod{
  font-size: 15px;
  color: #9c9c9c;
}

@media screen and (max-width: 1216px){
    .request_item,
    .design_quantity .sold_div{
        width: 96% !important;
    }
    .chosen-select{
        width: 90% !important;
    }
}

ul.wholesale_list li {
  padding-top:15px;
}

.fancybox-nav.fancybox-prev, .fancybox-nav.fancybox-next {
  z-index: 999999;
}

#fancybox-thumbs.bottom {
  height: 100%;
  margin-top: 0px;
}
.design_counts {
  margin: 10px auto auto;
  overflow: hidden;
  text-align: center;
  padding-right: 0px;
  padding-left: 0px;
  font-size: 14px;
  padding-bottom: 10px;
  min-width: 185px;
  font-weight: bold;
  line-height: 1.5;
  @media screen and (max-width: 765px) {
    width: 218px;
  }
  @media screen and (max-width: 320px) {
    width: 185px;
  }
}

.atv_option_type {
  padding-top:5px;
  border: 2px #444444 solid;
  padding-bottom:5px;
  margin-top:5px;
  margin-bottom:5px;
}

.atv_option_values {
  margin-left:5px;
  width:180px;
  padding:0;
  border-radius:6px;
}

label.headding_text {
  text-decoration:underline;
  width: 300px !important;
}

label.underline_text {
  text-decoration:underline;
}

#fancybox-thumbs ul {
  margin-top:10px !important;
}

.large_device_img_div {
  padding-right:10px;
  min-width:240px;
  .wishlist{
    background: image-url('wishlist.png') no-repeat;
    width: 23px;
    height: 20px;
    position: absolute;
    cursor: pointer;
    right: 15px;
    // z-index: 1029;
    top: 15px;
  }
  .img_text{
    width: 100%;
    font-style: italic;
    font-size: 12px;
    letter-spacing: 1px;
    color: $global-text-color;
  }
}
.large_img_div {
  min-height: 440px;
  border: $light-border-box;
  padding: 5px;
  margin-bottom: 10px;
  .large_img{
    width: 100%;
    text-align: center;
    .zoomWrapper{
      width: auto !important;
      margin: auto;
    }
    img{
      background: none !important;
      position: relative !important;
    }
    .wishlist-forms{
      position: absolute;
      right: 0;
      z-index: 10;
      top: 4px;
      font-size: 33px;
      color: $light-red-text;
      background: white;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: white;
      box-shadow: 0px 2px 8px 0px rgba(113, 103, 103, 0.75);
      right: 4px;
      .wishlist-heart-button{
        outline: none !important;
        padding: 0;
        margin: 0;
        background: none;
        margin-top: 5px;
        border: none;
        span{
          margin-right: 2px;
          margin-top: 3px;
        }
      }
    }
  }
}

.thumb_image_div {
  padding-top: 5px;
  padding-bottom: 15px;
  padding-left:0px;
  padding-right:0px;
}

.design_details {
  padding-top:50px;
  padding-right: 0px;
  padding-left: 0px;
  .collapse{
    cursor: pointer;
    display: block;
    color: white;
    width: 20px;
    height: 20px;
    text-align: center;
    padding: 4px;
    border-left: 1px solid #615a5a;
    border-bottom: 1px solid #615a5a;
    float: right;
    margin-top: -30px;
    .add{
      background: #615a5a;
      width: 20px;
      height: 20px;
      text-align: center;
      padding: 2px;
      margin-top: -8px;
    }
  }
  #specs{
  background-color: #ebebeb;
  }
  #tabs{
    border-radius: 0px !important;
    background: none;
    padding: 0px !important;
    border: 0px;
    ul{
      background: #615b5b;
      border-radius: 0px;
      border: 0px;
      padding: 0px;
    }
    .ui-state-active{
      background: $global-text-color !important;
    }
    li{
      background: none;
      border: 0px;
      color: $white !important;
      position: inherit;
      line-height: 17px;
      margin: 0px;
      padding: 3px 0px;
      border-radius: 0px;
      padding-left: 1px;
      &:hover{
        background-color: $global-text-color;
      }
      &:focus{
        background-color: $global-text-color;
      }
      a{
        color: $white !important;
      }
    }
    .table-responsive{
      color: $global-text-color !important;

      .brand-link {
        font-size: 14px;
      }
    }
  }
}

#return_policy_non_tabled{
  font-size:15px;
  div{
    padding: 0.2% 0%;
    .small-bullet{
      display: inline-block;
      background: none;
      width: 6px;
      height: 6px;
      border:black 1px solid;
      border-radius: 50%;
      margin: 0.2% 1%;
    }
  }
}

.design_image_cache_block{
  
}

.similar_items_outer_div {
  border: 1px solid #292828;
}
.rts-label{
  #ready_to_ship{
    background: image-url('RTS_logo_offers.png') no-repeat left top;
    display: inline-block;
    background-size: 39px;
    width: 42px;
    height: 15px;
    margin-right: 3px;
  }
}

.available-offer-section{
  .available-offer-label{
    margin-bottom: 8px;
    color: $global-text-color;
    font-size: 16px;
  }
  .available-offer-slider{
    white-space: nowrap;
    overflow: hidden;
    @include flex;
    .red-b1g1-label, .red-loyalty-label, .rts-label, .red-quantity-disc-promo-label{
      width: auto;
      .offer-container{
        @include flex;
        height: 35px;
        padding: 9px 0px 9px 5px;
        box-shadow: 0px 4px 20px 0 rgba(0, 0, 0, 0.08);
        margin-right: 10px;
        .offer-label{
          background: #d43153;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin: 3px 6px 3px 3px;        }
        .qpm-promo-tnc, .loyalty-promo-tnc, .rts-promo-tnc, .bmgnx-promo-tnc{
          display: inline-block;
          .design-promo-info-tnc, .loyalty-cashback-info-tnc{
            color: $background-color !important;
            padding: 0px 6px;
            font-size: 14px;
            font-style: italic;
            cursor: pointer;
            decoration: underline;
          }
        }
        .quantity_discount_message, .b1g1, .loyalty_cashback{
          font-size: 14px;
          display: inline;
        }
      }
      .tooltip-inner{
        width: 200px;
      }
    }
  }
  .scroll-button{
    position: absolute;
    height: 35px;
    background: #e4dede;
    cursor: pointer;
  }
  #scroll-btn-left{
    display: none;
    top: 25px;
    right: 0;
    width: 7%;
    box-shadow: -15px 5px 75px -4px rgba(0, 0, 0, 0.45);
    &:after{
      content: '';
      width: 10px;
      height: 10px;
      position: absolute;
      transform: rotate(-45deg);
      border-right: 2px solid $background-color;
      border-bottom: 2px solid $background-color;
      top: 12px;
      right: 10px;
    }
  }
  #scroll-btn-right{
    display: none;
    top: 25px;
    z-index: 1;
    width: 7%;
    box-shadow: 15px 5px 75px -4px rgba(0, 0, 0, 0.45);
    &:after{
      content: '';
      width: 10px;
      height: 10px;
      position: absolute;
      transform: rotate(-45deg);
      border-left: 2px solid $background-color;
      border-top: 2px solid $background-color;
      top: 12px;
      left: 10px;
    }
  }
}


.right_description{
  padding-right: 0px;
  .size-text{
    margin: 8px 0px;
    color: white;
    font-size: 17px;
    font-weight: 700;
    word-spacing: 5px;
  }
  #design_group{
    .design-group-wrapper{
      padding: 8px 0px;
      .design-group-title{
        /*font-weight: bold;*/
        font-size: 16px;
      }
      .design-group-by-color{
        display: inline-block;
        margin: 8px 5px 5px 5px;
        .sibling-design{
          cursor: pointer;
          text-align: center;
          margin: 0px 10px 0px 0px;
          border: 1px solid #eee;
          padding: 5px;
          display: inline-block;
          &:hover{
            background: #f4f4f4;
          }
          .sibling-design-color-title{
            font-size: 12px;
          }
        }
        .current{
          border: 1px solid #303030;
        }
      }
    }
  }
  .button-container{
      display: inline-flex;
      .add-to-cart{
        width: 232px;
        height: 56px;
        padding: 16px 0px;
        text-align: center;
        background-color: #670e19;
        margin-right: 15px;
        .add-to-cart-text{
          vertical-align: super;
          font-size: 14px;
          letter-spacing: 1px;
          color: $white;
          text-align: center;
        }
      }
      .add_to_cart_link{
        background: #b11f2c;
        width: 75%;
        margin-top: 10px;
      }
      .buy_this_now_button{
        height: 56px;
        padding: 10px 0px;
        text-align: center;
        border: 1px solid #670f19;
        .buy-this-now-text{
          vertical-align: super;
          font-size: 20px;
          letter-spacing: 1px;
          color: $white;
          text-align: center;
          font-weight: bold;
        }
      }
    }
  button.btn-view-size{
    background: $white;
    border: none;
    color: $label_light_red;
    margin: 5px 16px;
    line-height: 15px;
    letter-spacing: 1px;
    font-weight: bold;
    font-size: 14px;
    text-decoration: underline;
  }
  .size-chart-div{
    width: 100%;
    height: auto;
    overflow-x: hidden;
    overflow-y: hidden;
    margin: 0px 0px 10px 0px;
    padding: 0px;
    .size-chart{
      height: 40px;
      .size_error{
        color: #dc4545;
        margin-top: 8px;
        padding-left: 5px;
        font-style: italic;
        display: none;
      }
      button.size{
        width: 35px;
        border: 1px solid #aba9a4;
        color: #303030;
        margin-left: 8px;
        margin-top: 2px;
        float: left;
        padding: 6px;
        cursor: pointer;
        background: $white;
      }
      button.size:hover{
        background-color: $light-red-background;
        color: $white;
        border-color: $light-red-background;
      }
      button.selected{
        background-color: $light-red-background;
        color: $white;
        border-color: $light-red-background;
      }
    }
  }
  span.size-left,span.size-right{
    position: absolute;
    padding: 4px 8px;
    font-size: 18px;
    font-weight: 700;
    background-color: #d2d2d2;
    top: 38px;
    color: #2d2b2b;
    z-index: 2;
    cursor: pointer;
    border-radius: 50%;
  }
  span.size-left{
    left: -15px;
    display: none;
  }
  span.size-right{
    right: -18px;
    display: block;
  }
  span.size-left:hover, span.size-right:hover{
    background-color: #afafaf;
  }
  .salwar_standard_atv{
    border: 2px transparent solid;
    width: 65%;
    legend{
      font-size: 17px;
      font-weight: 700;
      word-spacing: 5px;
      margin-bottom: 5px;
    }
  }
  #shapewear-size-chart, #modal-size-chart{
    .modal-lg-size{
      width: 900px;
      .modal-content{
        background: #efeded;
        color: #191515;
        .modal-header{
          h4{
            color: #383434;
          }
        }
        .modal-body{
          .radio-buttons{
            margin: 10px 0px;
            #sizes_size-cm{
              margin-left: 30px;
            }
          }
          .table-size-bordered{
            border: 1px solid #4CAF50;
            th, td{
              border: 1px solid #4CAF50;
            }
          }
        }
      }
    }
  }
  #modal-dynamic-size-chart{
    .modal-dialog{
      min-width: 800px;
      width:75%;
      .modal-content{
        background-color: #fff;
        .modal-header{
          border-bottom: 1px solid #e5e5e5;
          .modal-title{
            color: black;
          }
        }
        .modal-header,.modal-body,.modal-footer{
          padding: 5px;
        }
      }
    }
  }
  #stitching_radio_ui{
    font-family: inherit;
    clear: both;
    .radio-stitching-panel{
      padding: 8px 0px;
      border-radius: 0px;
      .div{
        color: $global-text-color;
      }
      .addon-type-heading{
        margin-bottom: 8px;
        color: $global-text-color;
        font-size: 16px;
        a.stitching_info{
          border-radius: 50%;
          background-color: $global-text-color;
          padding: 0px 6px;
          font-size: 14px;
          font-style: italic;
          font-weight: 700;
          font-family: 'Times New Roman';
          cursor: pointer;
          color: $white !important;
        }
      }
      legend.free_stitching_legend{
        width: auto;
        color: white;
        font-size: 12px;
        text-transform: uppercase;
        padding: 5px;
        background: $light-red-background;
        position: absolute;
        right: 35%;
      }
      .atv-name{
        font-size: 13px;
        font-weight: 400;
        width:100%;
        .atv-name-value{
          width: 45%;
          display: inline-block;
        }
        span.price-s{
          color:$light-red-background;
        }
      }
      .select_div_addons{
        margin: 0px 0px 0px 10px;
      }
      .size-text{
        color: $global-text-color;
        font-size: 14px;
        font-weight: 400;
      }
      .size_error, .atv_error, .custom-stitching-notice{
        color: #dc4545;
        margin-top: 8px;
        padding-left: 5px;
        font-style: italic;
        display: none;
      }
      .salwar_standard_atv{
        legend{
          margin-top: 15px; 
          color: $global-text-color;
          font-size: 14px;
          font-weight: 400;
        }
        .aov_select{
          height: 25px;
          font-size: 13px;
          padding: 0px 6px;
          border-radius: 2px;
        }
      }
      #salwar_kameez_default,#salwar_kameez_specific{
        text-align: justify;
        line-height: 1.2em;
        color: #b11f2c;
        font-style: italic;
        font-size: 13px;
        font-family: Times new roman;
      }
    }
  }

  position: relative;

  .mirraw-recommended {
    position: absolute;
    top: -22px;
    left: 55px;

    border-left: 2px solid #8f8f8f;
    margin-left: 8px;
    padding-left: 8px;

    &::before, &::after {
      content: '';

      position: absolute;
      left: -2px;
      height: 18px;

      border-left: 2px solid white;
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }
    
    & > img {
      height: 54px;
      width: auto;

      background: none;
    }
  }
}

.design_msg {
  color:rgb(209, 75, 75);
  margin-top: 10px;
}

.del_variant_txt {
  color:#666;
}

.product_discription_div {
  padding: 0px;

  .design-addon-products{
    width: 80%;
    margin: 0 auto;
    .addon-product-header{
      padding: 0.4rem 0;
      h5{
        padding: 10px 30px;
        color: $global-text-color;
        border: $light-border-box;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
        margin-top: -40px;
        background: $white;
        text-align: center;
        text-transform: uppercase;
      }
    }
    .addon-product-body{
      width: 100%;
      margin: 12px 0;
      .addon-block{
        // border: 1px solid $dark-grey;
        width: 215px;
        height: 305px;
        position: relative;
        .variant-bg{
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 80%;
          background: rgba(0, 0, 0, 0.35);
          opacity: 0;
          -webkit-transition: opacity .3s cubic-bezier(0,0,.3,1);
          transition: opacity .3s cubic-bezier(0,0,.3,1);
          pointer-events: none;
        }
        &:not(:first-child):hover {
          .add-addon-button{
            -webkit-transition: all 0.5s ease-out;
            -o-transition: all 0.5s ease-out;
            -moz-transition: all 0.5s ease-out;
            transition: all 0.5s ease-out;
            display: block;
          }
          .added-item-label{
            display: none !important;
          }
        }
        &:not(:last-child):after {
          content: "\002b";
          font-size: 30px;
          margin: 0;
          color: #c2c2c2;
          position: absolute;
          top: 38%;
          display: inline-block;
          z-index: 1;
          left: 7.3em;
        }
        .added-item-label{
          position: absolute;
          bottom: 53px;
          padding: 7px 0;
          width: 100%;
          div{
            color: $light-red-bg;
            line-height: 2.9;
            text-align: center;
            background: rgba(255, 255, 255, 0.7411764705882353);
            font-size: 12px;
          }
        }
        .design-image{
          border: 1px solid $dark-grey;
        }
        .design-details{
          h5{
            line-height: 1.25;
            color: grey;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .old-price-label{
            font-size: 12px;
            letter-spacing: 0;
            line-height: 0;
            color: grey;
            text-decoration: line-through;
          }
          .new-price-label{
            font-size: 13px;
          }
          .discount_percent{
            background: none;
            color: $dark-red-text;
            letter-spacing: 0;
            word-spacing: 0;
            padding: 0;
            font-size: 12px;
            font-weight: bold;
          }
          padding: 5px;
          .discount_old_price{
            margin-right: 0;
          }
          .special-price-tag{
            font-size: 12px;
            color: $light-red-text;
            font-weight: bold;
            line-height: 1.5;
          }
        }
      }
      label{
        text-align: center;
        span{
          font-size: 0.750rem;
          margin: 0.3em;
        }
      }
      .add-addon-button{
          display: none;
          padding: 5px;
          bottom: 58px;
          position: absolute;
          width: 100%;
          background: $white;
          border: 1px solid $dark-grey;
        .addon_product{
          outline: none;
          appearance: none;
          -webkit-appearance: none;
          border: 1px solid $light-red-bg;
          width: 100%;
          height: auto;
          text-align: center;
          background: $white;
          color: $light-red-bg;
          line-height: 2.6;
          font-size: 12px;
          cursor: pointer;
          margin: 0;
          &:not(:checked){
            &:after {
              content: "+ ADD ITEM";
            }
          }
          &:checked {
            &:after {
              content: "REMOVE ITEM";
            }
          }
        }
      }
    }
    #add_more_items_button{
      width: 73%;
      float: right;
      border: 1px solid $dark-grey;
      margin-top: 8px;
      padding: 8px;
      .add_multi_item{
        width: 185px;
        height: 34px;
        cursor: not-allowed;
        float: right;
        background: $gray;
        padding: 10px 32px;
        border: 1px solid ;
        text-transform: uppercase;
        font-size: 12px;
        border-color: $gray;
        text-align: center;
        font-weight: bold;
        .buy_this_now_button{
          color: $white;
        }
      }
      .product-details{
        display: none;
        margin: 2px 0;
        font-size: 12px;
        .added-items, .original-product-item, .total-amount{
          padding: 0 12px;
          text-transform: uppercase;
          color: gray;
          div{
            line-height: 1.5;
            font-weight: bold;
            color: $global-text-color;
          }
        }
        .original-product-item{
          &:after {
            content: '+';
            position: absolute;
            left: 27%;
            top: 25%;
          }
        }
        .added-items{
          &:after {
            content: '=';
            position: absolute;
            left: 69%;
            top: 25%;
          }
        }
      }
    }
  }
}

.select-variant{
  .variant_selection{
    z-index: 1;
    width: 100%;
    max-width: 100%;
    position: absolute;
    bottom: 58px;
    left: 0;
    padding: 4px;
    background: white;
    visibility: hidden;
    margin-bottom: -40px;
    -webkit-transition: all 0.2s ease-in;
    -o-transition: all 0.2s ease-in;
    -moz-transition: all 0.2s ease-in;
    transition: all 0.2s ease-in;
    -webkit-transition: all 0.1s ease-out;
    -o-transition: all 0.1s ease-out;
    -moz-transition: all 0.1s ease-out;
    transition: all 0.1s ease-out;
    border: 1px solid $dark-grey;
    .content{
      .variant-header{
        .close-variant-select{
          font-size: 20px;
          float: right;
          cursor: pointer;
          color: $gray;
        }
        .text{
          .variant_div{
            width: 100%;
            .variant_text{
              display: none;
            }
            ul{
              li{
                padding: 5px 12px;
                font-size: 12px;
                margin-right: 4px;
                margin-top: 4px;
                margin-bottom: 4px;
                border-radius: 1px;
                border-color: #c8c8c8;
                color: grey;
                &:hover, &:focus {
                  background: #c8c8c8;
                }
              }
              .selected, .variant-selected {
                background-color: #c8c8c8;
              }
            }
          }
        }
      }
      .select-addon-variant{
        border: 1px solid $light-red-bg;
        width: 100%;
        height: auto;
        text-align: center;
        background: $white;
        -webkit-appearance: none;
        border-radius: 0;
        box-shadow: none;
        text-transform: uppercase;
        color: $light-red-bg;
        line-height: 1.7;
      }
    }
  }
}

#line_items_count {
  display: none;
  width: 255px;
  background-color: #cecece;
  color: #000000;
  text-align: center;
  padding: 16px;
  position: fixed;
  z-index: 1000;
  left: 15px;
  bottom: 15px;
  font-size: 14px;
  line-height: 21px;
  border: 2px solid #e29cb4;
  .line_item_close{
    float: right;
    text-align: right;
    position: relative;
    left: 10px;
    bottom: 8px;
    padding: 0px 5px;
    cursor: pointer;
    border-radius: 50%;
    background-color: #797979;
  }
  .line_items_count_text{
    span{
      color: $background-color;
      text-transform: uppercase;
      font-weight: 700;
    }
  }
}

.slider_image {

}

.google_adsense {
  padding-top:10px;
  padding-left: 0px;
}

.black_text {
  color:black;
}

#recently-viewed-product-carousel .bx-loading {
  display: none;
}
.notice_class {
  color: $background-color;
  font-size:12px;
  line-height:15px;
  font-style:italic
}

@font-face {
  font-family: 'boston';
  src:font-url('boston.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

#deal_ends{
  position: fixed;
  bottom: 0px;
  right: 45px;
  z-index: 10;
  width: 250px;
  // font-family: 'boston';
  padding: 19px 0px;
  margin: 10px 0 0 0; 
  border: none;
  background: #f7aa9b;
  background: -webkit-linear-gradient(to bottom, #f5dcdc , #fff);
  background: linear-gradient(to bottom ,#f5dcdc , #fff);
  #close_btn_deal{
    font-size: 20px;
    bottom: 12px;
    // font-family: monospace;
    right: 12px;
    color: $light-red-text;
    float: right;
    font-weight: bold;
    position: relative;
    cursor: pointer;
    display: inline-block;
  }
  #countdown{
    font-size: 25px;
    margin-top: 16px;
    color: $light-red-text;
    text-align: center;
    text-transform: uppercase;
  }
  #clock{
    display:inline-block;
    width: 200px;
    font-size: 35px;
    color: #9e3f3f;
    .deal_timer{
      // font-family: 'boston';
      font-size: 32px;
      display: inline-block;
      width: 40px;
      vertical-align: text-bottom;
    }
  }
  
}
.how_stitching_works {
    font-style: italic;
    text-decoration: underline;
      font-size: 15px;  
      color: white !important ;
  }

.how_stitching_works:hover {
  font-style: italic;
    font-size: 18px;
    color:#77d9f9 !important;
}
.customer_review{
      font-size: 20px !important; 
    }
.customer_review:hover{
      text-decoration: underline;
    }  

.stitching_offer{
   color: #EE9209;
   font-size: 18px;
   margin-top: 10px;
}

/*.design_tags{
  .label{
      display: inline-block;
      line-height: 1;
      vertical-align: baseline;
      margin: 0 .14285714em;
      background-color: #E8E8E8;
      background-image: none;
      padding: .5833em .833em;
      color: rgba(0,0,0,.6);
      text-transform: none;
      font-weight: 700;
      border: 0 solid transparent;
      border-radius: .28571429rem;
      -webkit-transition: background .1s ease;
      transition: background .1s ease
  }   

  .tag.label,.tag.labels .label {
      margin-left: 1em;
      position: relative;
      padding-left: 1.5em;
      padding-right: 1.5em;
      font-size: 13px;
      border-radius: 0 .28571429rem .28571429rem 0;
      -webkit-transition: none;
      transition: none;
      margin-top:5px;
  } 
  .tag.label:before,.tag.labels .label:before {
      position: absolute;
      -webkit-transform: translateY(-50%) translateX(50%) rotate(-45deg);
      transform: translateY(-50%) translateX(50%) rotate(-45deg);
      top: 50%;
      right: 100%;
      content: '';
      background-color: inherit;
      background-image: none;
      width: 1.56em;
      height: 1.56em;
      -webkit-transition: none;
      transition: none
  }
  .tag.label:after,.tag.labels .label:after {
      position: absolute;
      content: '';
      top: 50%;
      left: -.25em;
      margin-top: -.25em;
      background-color: #000!important;
      width: .5em;
      height: .5em;
      box-shadow: 0 -1px 1px 0 rgba(0,0,0,.3);
      border-radius: 500rem;
  }
  .label:hover,.labels .label:hover {
      background-color: #E0E0E0;
      border-color: #E0E0E0;
      background-image: none;
      color: rgba(0,0,0,.8)
  }   

  .pink.label,.pink.labels .label {
      background-color: #FF4081!important;
      border-color: #FF4081!important;
      color: #FFF!important
  } 
  .pink.labels .label:hover,.pink.label:hover {
      background-color: #EC407A!important;
      border-color: #EC407A!important;
      color: #FFF!important
  }   

  .green.label,.green.labels .label {
      background-color: #16be48!important;
      border-color: #16be48!important;
      color: #FFF!important
  } 
  .green.labels .label:hover,.green.label:hover {
      background-color: #00C853!important;
      border-color: #00C853!important;
      color: #FFF!important
  }
}
.hot-deal-tag{
  position: absolute;
  top: 291px;
  left: 162px;
  padding: 2px;
  width: 63px;
  height: 18px;
  font-weight: 600;
  color: #ffffff;
  border-radius: 4px;
  background-color: #FF4081;
}*/

.design_block_wrapper{
  margin: 15px 100px 15px 0px;
  .design_blocks{
    border: 1px solid #95a5a6;
    width:180px;
    height:180px;
    cursor:pointer;
    float:left;
    background-size:180px;
    margin:0px 35px 50px 0px;
  }
  .design_blocks:hover .design_block_off{
    opacity:0
  }
  .design_blocks:hover{
    background-repeat:no-repeat;
    background-size:185px;
  }
  .design_blocks:hover .design_block_buy{
    opacity:1
  }

  .design_block_off{
    width:60%;
    height:20%;
    background-color:#ecf0f1;
    top:80%;
    position:relative;
    opacity:1;
    margin-left: 20%;
    text-align:center;
    font-weight:bold;
    color:#000000;
    vertical-align:middle;
    font-size: 14px;
  }
  .design_block_buy{
    width:60%;
    height:20%;
    opacity:0;
    background-color: #444444;
    top:60%;
    position:relative;
    margin-left: 20%;
    opacity:0;
    text-align:center;
    font-weight:bold;
    color:white;
    vertical-align:middle;
  }

  .middle{
    top:30%;
    position:relative;
    .text{
      font-size: 14px;
    }
  }
  .design_block_price{
    border-right: 2px solid #95a5a6;
    border-left: 2px solid #95a5a6;
    border-bottom: 2px solid #95a5a6;
    padding-bottom:5px;
    height:25%;
    top:60%;
    position:relative;
    opacity:1;
    text-align:center;
    color:#ffffff;
    overflow:hidden;
  }
  .design_block_designer_name{
    font-size: 15px;
    text-align: center;
    margin-top:-5px;
    font-weight:bold;
  }

  .design_block_price_text{
    font-size: 13px;
    text-align: left;
    margin:5px 0px 0px 15px;
  }
  .design_block_rating{
    float:right;
    margin-right: 10%;
  }
}
.small_image{
  margin-left:15px;
}

// stitching conformation popup modal css
#stitchingModal{
  background: url(/assets/overlay_grey.png) repeat !important;
  #stitching-confirmation-message{
    color: #808080;
  }
  .modal-header{
    padding:0px;
    font-size: 15px;
    #stitching-confirmation-message{
          color: white;
          background-color: grey;
          padding: 11px;
    }
    button{
      padding:10px;
    }
  }
  .modal-content{
    padding: 0px;
    width: 500px;
    height: 100%;
    margin: 0% auto auto;
    background-color: #191919;
    color: white;
    font-size: 1.25em;
    line-height: 1.5;
    border-radius:0px;
  }
    .close{
      color: #d9d9d9;
      opacity: 1;
      margin: 0px;
      font-size: 17px !important;
      font-weight:normal;
    }
  .modal-footer{
      text-align: center;
      padding: 2% 0 8%;
      .btn{
        padding: 7px !important;
        width: 42%;
        font-size: 12px !important;
      }
      .stitch{
        &:hover{
          color:white;
        }
      }
      .stitch{
        background: #008a32;
      }
      .add-cart{
        background: #d8d7d7;
        color:#2d2d2d !important;
      }
    }
  .modal-body{
    text-align: center;
    padding:20px;
    font-size: 14px;
    .modal-message{
      padding-top:20px;
    }
  }
}

.label_for_image_box {
  position: absolute;
  top: -5px;
  width: 110px;
  height: 110px;
  overflow: hidden;
}

.label_text {
  position: relative;
  display: none;
  left: -11px;
  top: 24px;
  width: 158px;
  padding: 10px 0;
  font-size: 15px;
  text-align: center;
  color: #fff;
  background-color: $light-red-text;
  -webkit-transform: rotate(45deg) translate3d(0, 0, 0);
  -moz-transform: rotate(45deg) translate3d(0, 0, 0);
  -ms-transform: rotate(45deg) translate3d(0, 0, 0);
  transform: rotate(45deg);
  z-index: 1;
  &:before{
    content: '';
    position: absolute;
    bottom: -4px;
    border-top: 4px solid #a71c26;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    left: 2px;
  }
  &:after{
    content: '';
    position: absolute;
    bottom: -4px;
    border-top: 4px solid #a71c26;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    right: 2px;
  }
}


//design_page_video_modal css
#video{
  background: rgba(132, 132, 132, 0.4);
  .modal-dialog{
    width: 731px;
  }
  .modal-content{
    border-radius: 5px;
    padding: 0px 0px 0px 15px;
    margin-left: auto;
    margin-right: auto;
    .modal-header{
      margin-right: 4px;
      margin-bottom: 3px;
      .close{
        outline: none;
        color: black;
        opacity: 0.9;
        margin-bottom: -1px;
        font-size: 32px;
        font-weight: lighter;
        line-height: 1;
        font-weight: light;
        filter: alpha(opacity=20)
      }
    }
    .iframe_video{
      width: 700px;
      height: 649px;
      background-color: black
    }
    .modal-footer{
      padding-top: 1px;
      padding-bottom: 7px;
      margin-top: 10px;
      .discount_percent{
        font-size: 14px;
        padding: 4px;
      }
      .old_price_label{
        margin-top: 2px;
        font-size: 14px;
      }
      .add_to_cart_video_button{
        left: 103px;
        width: 220px !important;
        a{
          height: 43px;
          padding: 11px 25px;
          text-align: center;
          border: 1px solid $dark-red-text;
        }
      }
      h3{
        font-weight: bold;
        font-size: 18px;
      }
    }
  }
}
.price_mismatch {
  &:hover {
    .price_mismatch_description {
      display: block;
    }
  }
  .price_mismatch_description {
    display: none;
    background: #c7c7c7;
    color: black;
    padding: 15px;
    float: left;
    margin-left: -100px;
    z-index: 1;
    position: absolute;
    margin-top: 5px;
    width: 250px;
  }
}#fc_frame{
  z-index: 999 !important;
}
.std_addon_info{
  float: right;
  font-size: 14px;
  line-height: 2.2;
}

.highlight{
  background: #d3d3d3
}
.design-breadcrumb {
  @include flex;
  margin-bottom: 10px;
  .design-info{
    width: 250px;
    @include truncate;
    vertical-align: bottom;
  }
  li:not(:first-child):before {
    content: '/';
    margin-left: 0px;
    margin-right: 0px;
  }
  li.arrow:not(:first-child):before {
    content: '>';
  }
  a {
    font-size: 12px !important;
    &:hover{
      color: $light-red-bg !important;
    }
  }
  li {
    font-size: 12px !important;
    display:inline-block;
  }
  .final{
    display: inherit;
    font-weight: bold;
    width: 250px;
    @include truncate;
    vertical-align: bottom;
  }
}
.content-equal-space{
  @include flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -moz-box-orient: horizontal;
  -moz-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -moz-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: stretch;
  align-content: stretch;
  margin: 0 -10px 0 3px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -moz-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

#variantModal{
  .modal-dialog{
    width: 50%;
    .modal-content{
      border-radius: 0;
      .modal-body{
        .modal-text{
          display: flex;
        }
        .variant_div{
          height: 86%;
          .variant_text{
            font-size: 14px;
          }
          .combo_variant_text{
            font-size: 14px;
          }
          .btn-view-size{
            display: none;
          }
        }
        .addon-block{
          border: 1px solid $dark-grey;
          .design-image{
            border-bottom: 1px solid $dark-grey;
          }
          .design-details{
            h5{
              line-height: 1.5;
              color: grey;
              @include truncate;
            }
            .old-price-label{
              font-size: 12px;
              letter-spacing: 0;
              line-height: 0;
              color: grey;
              text-decoration: line-through;
            }
            .new-price-label{
              font-size: 13px;
            }
            .discount_percent{
              background: none;
              color: $dark-red-text;
              letter-spacing: 0;
              word-spacing: 0;
              padding: 0;
              font-size: 12px;
              font-weight: bold;
            }
            padding: 5px;
            .discount_old_price{
              margin-right: 0;
            }
          }
        }
        }
      .add_to_cart_link{
        width: 170px;
        height: 34px;
        float: left;
        background: $white !important;
        padding: 10px 32px;
        border: 1px solid $global-text-color !important;
        text-transform: uppercase;
        font-size: 12px;
        text-align: center;
        color: $global-text-color !important;
      }
    }
  }
}

.size-modal-visible{
  visibility: visible !important;
  margin-bottom: 0 !important;
}
.add-multi-item-enable{
  cursor: pointer !important;
  border-color: $light-red-bg !important;
  background: $shop-now-button-color !important;
}
.variant-bg-enable{
  pointer-events: auto !important;
  opacity: 1 !important;
}

.box_ai_widget.design_widget_div{
  margin-top: 15px;
}
.effect-1{height:35px ;background-color : white; display: inline-flex;border: 0; padding: 0px; border-bottom: 1px solid #ccc;}
.effect-1 ~ .focus-border{position: absolute; bottom: 0; left: 0; width: 0; height: 0; background-color: #3399FF; transition: 0.4s;}
.effect-1:focus{ border-color:#cd3232; }
.pincode_input{
  border-width:0px;
  position:relative; 
  left:3px;
  padding: 2%;
  outline: none;
  font-size: 15px;
}
.cod_pdd_check_button{
  cursor: pointer;
  padding: 2%; 
  position:relative; 
  right:12px; 
  color: #2874f0 !important; 
  
}
.location_logo{
  color: #2874f0; 
  position:relative; 
  top:4px; 
  left:5px; 
  padding: 2%;
  font-size:16px
}
.cod_availability_message{
font-size: 16px;
line-height: 18px;
color: #303030;
}
.button{
  border: 1px solid #d8d8d8;
  color: black;
  background-color: white;
  font-weight: 700;
  margin-left: 6px;
  margin-top: 23px;
  float: left;
  border-radius: 1%;
  padding: 5px;
  cursor: pointer;
  display: inline-table;
  line-height: normal;
}
.plus_size{
  margin-top: -20px;
}

.plus_size_regular_btn {
  background-color:#9a9999;
}

.fabric_color_table {
  padding-top: 10px;
}
.shapewear_color_table {
  justify-content: left;
  margin-bottom: 0px;
  background: none;
  border: none;
}
.checkmark {
  display: inline-block;
  text-align: center;
  height: 25px;
  width: 25px;
  border-radius: 50%;
  cursor: pointer;
}

.shapewear_color {
  display: inline-grid;
  grid-template-columns: auto auto auto auto auto;
  // padding: 10px;
}
.shapewear_color_radio_button {
  border: 1px solid rgba(0, 0, 0, 0.8);
  text-align: center;
  cursor: pointer;
  height: 25px;
  width: 25px;
  border-radius: 50%;
  border: 0.1px solid black;
  margin: 15px;
}

table tbody tr td {
  display: table-cell;
  line-height: 1.125rem;
}

.plus_size_fabric_selected {
  box-shadow: 0 0 0 4px #fff, 0 0 0 6px #8F1B1D;
}

.fabric_color_name{
  margin-left: -3px;
}

.plus_size_buttons{
  padding-top: 5px;
  padding-bottom: 5px;
}
.plus_size_custom_regular{
    border: 1px solid #d8d8d8;
    color: black;
    font-weight: 700;
    margin-left: 6px;
    margin-top: 3px;
    float: left;
    border-radius: 1%;
    padding: 5px;
    cursor: pointer;
    display: inline-table;
    line-height: normal;
}
.plus_size_custom{
    border: 1px solid #d8d8d8;
    color: black;
    background-color: white;
    font-weight: 700;
    margin-left: 6px;
    margin-top: 2px;
    float: left;
    border-radius: 1%;
    padding: 5px;
    cursor: pointer;
    display: inline-table;
    line-height: normal;
}
.other_custom_plus_size {
  background-color: white;
  border-style: solid;
  border-width: 1px;
  color: black;
}
.selected_custom_plus_size {
  background-color: #D6D6D6;
  color: black;
}
.form-group2{
  display: none;
}
.dropdown-div{
  padding-top: 5px;
}
.plus_size_icon{
  display: flex;
  align-items: center;
}
.plus_size_icon_data{
  margin-top: -23px;
  margin-left: 210px;
}
.plus_size_icon_info{
  margin-top: -15px;
  margin-left: 4px;
}
.form-groups{
  margin-top: -20px;
  margin-left: -15px;
  padding-bottom: 15px;
  padding-top: 10px;
}
#render_plus_size_color{
  line-height: 3;
}
#info_message{
  margin-top: 100px;
}
.shapewear_color_name {
  margin-top: 3.5vh;
  margin-left: -3px;
}

.shapewear_color_selected {
  box-shadow: 0 0 0 4px #fff, 0 0 0 6px #8F1B1D;
}
#master{
  max-width: 100%;
  max-height: 100%;
}

.uk-text{
  color: #636466;
  font-size: 15px;
}

.atv_option_types.col-sm-11.select_div_addons.stitched {
  position: relative;
}

.select_div.col-sm-12 .info_message_for_icon {
  position: absolute;
  right: 20%;
  margin-right: -8px;
  bottom: 63%;
}

.form-group2 .info_message_for_icon {
  position: absolute;
  right: 47%;
  top: 53%;
}
.table-container {
  display: flex;

  .custom-table {
    width: 100%;
    font-size: 1.2em;
    table-layout: fixed;
    text-align: center;

    .table-row {
      height: 50px;
      vertical-align: middle;

      .table-cell {
        vertical-align: middle;
        border: 1px solid;

        strong {
          line-height: 1;
        }
      }
    }
  }
}

@import 'unbxd_recommendation.scss'
