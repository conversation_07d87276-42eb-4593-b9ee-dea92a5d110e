class SingleVideoUploadJob
    include Sidekiq::Worker
    sidekiq_options queue: 'low', retry: false
  
    def perform(email, design_id, link)
      begin
        service = SingleVideoUploadService.new(email, design_id, link)
        service.run
      rescue => exception
        DesignerMailer.send_progress_notification("Designs Video Upload Failed for #{design_id} due to #{exception.message}",email).deliver_now!
      end
    end
end
  