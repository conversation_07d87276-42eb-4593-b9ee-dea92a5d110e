class GenerateMonthlyCommissionReportJob
    include Sidekiq::Worker
    sidekiq_options retry: false
    def perform(run_task,einv_date,payout_date,invoice_date,start_date,end_date)
        if Time.current.day == 1 || run_task == 'true'
            invoice_ids = []
            errors_hash = {}
            irn_error_hash, irn_request = '',''
            irn_alert_email_array = ['<EMAIL>','<EMAIL>', DEPARTMENT_HEAD_EMAILS['accounts']]
            time_period = start_date.to_datetime..end_date.to_datetime
            igst,sgst,cgst = (IGST.to_f/100),(SGST.to_f/100),(CGST.to_f/100)
            designer_ids = DesignerOrder.unscoped.select('designer_orders.id,designer_id').joins(:order).where(confirmed_at: time_period).where(order: {country: 'India'}).where('invoice_id IS NULL and (ship_to is null OR ship_to = ?)','customer').uniq.group_by(&:designer_id)
            sales_header = ['Vendor Id','Vendor GSTIN','Order Id', 'Sales Invoice Number', 'Sales Invoice Date', 'Description', 'LineItem Quantity', 'SKU Code', 'Discounted Order', 'Sales Price', 'Tax Rate', 'HSN Code', 'Taxable Value', 'CGST', 'SGST','IGST', 'Total', 'State of customer', 'State of Vendor'].freeze
            sales_return_header = ['Vendor Id','Vendor GSTIN','Order Id', 'Credit Invoice Number','Credit Invoice Date','Reference Number', 'Reference Date', 'Description', 'LineItem Quantity', 'SKU Code', 'Discounted Order', 'Sales Price', 'Tax Rate', 'HSN Code', 'Taxable Value', 'CGST', 'SGST','IGST', 'Total', 'State of customer', 'State of Vendor'].freeze
            sales_file = '/tmp/combined_sales_report.csv'
            CSV.open(sales_file, "wb", {:col_sep => "\t"}) do |csv|
              csv << sales_header
            end
            sales_return_file = '/tmp/combined_sales_return_report.csv'
            CSV.open(sales_return_file, "wb", {:col_sep => "\t"}) do |csv|
              csv << sales_return_header
            end
            designer_ids.each_slice(100).each do |des_id|
              all_orders = LineItem.unscoped.where(designer_order_id: des_id.collect(&:last).flatten.compact.collect(&:id)).joins(:designer_order,:designer).preload(:payment_order, rack_lists_warehouse_line_items: [item: :warehouse_order], design: [:designer,:categories,property_values: :property]).where('designer_orders.invoice_id IS NULL and (designer_orders.ship_to is null OR designer_orders.ship_to = ?)','customer').select('designer_orders.invoice_number,designer_orders.confirmed_at,designer_orders.completed_at,designer_orders.pickup,designer_orders.created_at,designer_orders.designer_id,designer_orders.id,designer_orders.total as dos_total,designer_orders.payout,designer_orders.transaction_rate,designer_orders.discount as dos_discount,designer_orders.state,designers.gst_no,designers.business_name,designers.name as designer_name,designers.state as designer_state,designers.business_state as designer_business_state, designers.email as designer_email, line_items.design_id,line_items.quantity,line_items.snapshot_price,line_items.vendor_selling_price, line_items.available_in_warehouse, line_items.purchase_hsn_code,line_items.purchase_gst_rate,line_items.status, line_items.return_image_file_name, line_items.designer_order_id,line_items.id,designer_orders.order_id, designer_orders.designer_payout_status').group_by(&:designer_id)
        
              reverse_commissions = ReverseCommission.joins(:order).where('lower(orders.country)  = ? ',  'india').where(created_at: '2017-09-01'..end_date.to_date.strftime('%Y-%m-%d'),invoice_id: nil,designer_id: des_id.collect(&:first)).preload(element: :payment_order).group_by(&:designer_id)
        
              #adjustments         = Adjustment.select('*,amount * -1 as amount').where("amount is not null and return_designer_order_id is null and (order_id is not null OR notes = 'Negative Commission') and commission_invoice_id is null and notes not like '%Canceled after payout%' and notes not like '%Refund for%' and notes not like 'Buyer Return for%' and notes not like '%after payout in order%' and notes not like 'Order marked sane%' and notes not like 'Shipping Cost for Order%'").preload(:payment_order).where(created_at: '2017-09-01'..args[:end_date].to_date.strftime('%Y-%m-%d'),designer_id: des_id.collect(&:first)).group_by(&:designer_id)
              s_clause = "designer_orders.designer_id as designer_id, vendor_payouts.id, vendor_payouts.shipment_id, designer_orders.confirmed_at as order_confirmed_date, designer_orders.created_at as order_created_date, designer_orders.pickup as order_pickup_date, designer_orders.completed_at as order_complete_date, orders.number as order_number, designer_orders.invoice_number as order_invoice_number, vendor_payouts.charge as order_shipping_charge, orders.buyer_state as customer_state, designer_orders.transaction_rate as transaction_rate "
              vendor_payouts = VendorPayout.select(s_clause).joins(shipment: {designer_order: :order}).where(status: 'passed', created_at: time_period, invoice_id: nil).where('vendor_payouts.shipment_id IS NOT NULL AND designer_orders.designer_id IN (?)', des_id.collect(&:first)).group_by(&:designer_id)
        
              all_orders.each do |designer_id,all_items|
                begin
                  total_cgst,total_sgst,total_igst,total_amount,total_commission,cgst_sgst,total_tcs_cgst,total_tcs_sgst,total_tcs_igst = 0.0,0.0,0.0,0.0,0.0,true,0.0,0.0,0.0
                  order_adjustments, other_adjustments, tcs_report, sales_report,sales_return_report= [],[],[],[],[],[]
                  supplier_name = (all_items.first.business_name.presence || all_items.first.designer_name)
                  gst_no = all_items.first.try(:design).try(:designer).try(:gst_no)
                  vendor_state = all_items.first.try(:designer_business_state).try(:upcase) || all_items.first.try(:designer_state).try(:upcase)
                  if vendor_state.blank?
                    designer = Designer.find designer_id
                    vendor_state = designer.try(:business_state).try(:upcase) || designer.try(:state).try(:upcase)
                  end
                  cgst_sgst = false if vendor_state != SHIPPER_STATE.upcase
                  file=CSV.generate(headers: true) do |csv|
                    csv << ['Vendor Id','Vendor GSTIN','Order Confirmed Date','Order Dispatch Date','Order Cancel Date','Order Id','Sale Invoice No','Vendor Selling Price','Transaction Rate','Commission','Commission Taxable Value','Commission CGST','Commission SGST','Commission IGST','Product Taxable Value','Product CGST','Product SGST','Product IGST','TCS-CGST','TCS-SGST','TCS-IGST','State of Customer', 'State of Vendor']
        
                    all_items.each do |item|
                      next if item.status.present?
                      hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
                      name         = item.design.categories.first.name.gsub('-', ' ').camelize
                      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
                      #vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                      designer = Designer.find designer_id
                      if designer.is_transfer_model?
                        vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*((100-item.transaction_rate.to_f)/100.0).round(2)
                      else
                        vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                      end
                      vendor_selling_price = vendor_selling_price* item.quantity
                      
                      commission = if item.transaction_rate.to_f > 0
                          (vendor_selling_price * item.transaction_rate / 100).round(2)
                        else
                          (vendor_selling_price - item.vendor_selling_price).to_f.round(2)
                        end
                      commission_tax_value = (commission * 100/118).round(2)
                      _,gst_rate = item.find_hscode_gst(vendor_selling_price, hsn_code.to_s, false)
                      product_taxable_value = (vendor_selling_price/(1+gst_rate/100.0)).round(2)
                      product_gst = product_taxable_value*(gst_rate/100.0).round(2)
                      tcs_value = (product_taxable_value/100.0).round(2)
                      cgst_sgst_p = item.payment_order.try(:buyer_state).to_s.downcase == vendor_state.to_s.downcase
                      cgst_rate,igst_rate,cgst_tcs,igst_tcs,cgst_product,igst_product = 0,0,0,0,0,0
                      if cgst_sgst
                        cgst_rate = ((commission - commission_tax_value).to_f/2).round(2)
                      else
                        igst_rate = (commission - commission_tax_value).to_f.round(2)
                      end
                      if cgst_sgst_p
                        cgst_tcs = (tcs_value.to_f/2).round(2)
                        cgst_product = (product_gst.to_f/2).round(2) 
                      else
                        igst_tcs = tcs_value.to_f.round(2)
                        igst_product = product_gst.to_f.round(2)
                      end
                      
                      item_desc = item.design.title + " - " + item.design.categories.first.name.singularize
                      item_sku = item.design.design_code.present? ? item.design.design_code : ''
                      is_discounted_order = item.dos_discount.to_f > 0  ? true : false  
                      product_total = product_taxable_value+cgst_product+cgst_product+igst_product
        
                      sales_report << [designer_id,gst_no,item.payment_order.try(:number), item.invoice_number,(item.confirmed_at.presence || item.created_at).try(:strftime,'%d/%m/%Y'), item_desc, item.quantity, item_sku, is_discounted_order, vendor_selling_price, gst_rate, hsn_code, product_taxable_value, cgst_product, cgst_product, igst_product,product_total, item.payment_order.try(:buyer_state),vendor_state ]
        
                      csv << [designer_id,gst_no,(item.confirmed_at.presence || item.created_at).try(:strftime,'%d/%m/%Y'),(item.pickup.presence || item.completed_at.presence || '').try(:strftime,'%d/%m/%Y'),'',item.payment_order.try(:number), item.invoice_number, vendor_selling_price,item.transaction_rate, commission, commission_tax_value, cgst_rate, cgst_rate, igst_rate,product_taxable_value, cgst_product, cgst_product, igst_product,cgst_tcs,cgst_tcs,igst_tcs,item.payment_order.try(:buyer_state),vendor_state]
                      
                      total_commission+=commission_tax_value; total_cgst+=cgst_rate; total_sgst+=cgst_rate; total_igst+=igst_rate;
                      total_tcs_cgst += cgst_tcs; total_tcs_sgst += cgst_tcs; total_tcs_igst += igst_tcs;
                    end
        
                    reverse_commissions[designer_id].to_a.each do |rev_com|
                      if (r_order = rev_com.element.try(:payment_order)).present?
                        element_li = rev_com.element.is_a?(LineItem) ? [rev_com.element] : rev_com.element.line_items
                        is_discounted_order = element_li.first.designer_order.try(:discount).to_f > 0  ? true : false
                        element_li.each do |item|  
                          hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
                          #vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.designer_order.transaction_rate.to_f)/100.0).round(2))
                          #vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                          designer = Designer.find designer_id
                          if designer.is_transfer_model?
                            vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*((100-item.designer_order.transaction_rate.to_f)/100.0).round(2)
                          else
                            vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                          end  
                          vendor_selling_price = vendor_selling_price* item.quantity
                          _,gst_rate = item.find_hscode_gst(vendor_selling_price, hsn_code.to_s, false)
                          rev_tax_val = (vendor_selling_price/(1+gst_rate/100.0)).round(2)
                          product_gst = (rev_tax_val*(gst_rate/100.0)).round(2)
                          
                          commission = if item.designer_order.transaction_rate.to_f == 0
                            (vendor_selling_price - item.vendor_selling_price).to_f.round(2)
                          else
                            (vendor_selling_price * item.designer_order.get_commission_factor).to_f.round(2)
                          end
                          commission_tax_value = (commission * 100/118).round(2)
                          cgst_sgst_p = r_order.try(:buyer_state).to_s.downcase == vendor_state.to_s.downcase
                          cgst_rate,igst_rate,cgst_tcs,igst_tcs,cgst_product,igst_product = 0,0,0,0,0,0
                          if cgst_sgst
                            cgst_rate = ((commission - commission_tax_value).to_f/2).round(2)
                          else
                            igst_rate = (commission - commission_tax_value).to_f.round(2)
                          end
                          if cgst_sgst_p
                            cgst_tcs = -(rev_tax_val / 200.0).round(2)
                            cgst_product = (product_gst.to_f/2).round(2) 
                          else
                            igst_tcs = -(rev_tax_val / 100.0).round(2)
                            igst_product = product_gst.to_f.round(2)
                          end
        
                           item_desc = item.design.title + " - " + item.design.categories.first.name.singularize
                           item_sku = item.design.design_code.present? ? item.design.design_code : ''
        
                          total = rev_tax_val + cgst_product + cgst_product + igst_product
                          sales_return_report << [designer_id,gst_no,r_order.number, item.designer_order.invoice_number.to_s+'_C',rev_com.created_at.strftime('%d/%m/%Y'),item.designer_order.invoice_number,(r_order.confirmed_at.presence || r_order.created_at).strftime('%d/%m/%Y'), item_desc, item.quantity, item_sku, is_discounted_order, vendor_selling_price,gst_rate,hsn_code, rev_tax_val, cgst_product, cgst_product, igst_product, total,r_order.buyer_state,vendor_state]
                          
                          csv << [designer_id,gst_no,(r_order.confirmed_at.presence || r_order.created_at).strftime('%d/%m/%Y'),'',rev_com.created_at.strftime('%d/%m/%Y'),r_order.number,item.designer_order.invoice_number,-1 * vendor_selling_price,item.designer_order.transaction_rate,-1*commission, -1*commission_tax_value,-1*cgst_rate,-1*cgst_rate,-1*igst_rate,-1*rev_tax_val,-1*cgst_product, -1*cgst_product, -1*igst_product,cgst_tcs,cgst_tcs,igst_tcs,r_order.buyer_state,vendor_state]
        
                          total_commission+=-commission_tax_value; total_cgst+=-cgst_rate; total_sgst+=-cgst_rate; total_igst+=-igst_rate;
                          total_tcs_cgst += cgst_tcs; total_tcs_sgst += cgst_tcs; total_tcs_igst += igst_tcs;
                        end
                      end
                    end
                    vendor_payouts[designer_id].to_a.each do |vendor_payout|
                      commission = (vendor_payout.order_shipping_charge * vendor_payout.transaction_rate.to_f/100.0).round(2)
                      amount_payable = vendor_payout.order_shipping_charge.to_f.round(2)
                      net_commission,cgst_tax,sgst_tax,igst_tax = PayoutManagement.get_gst_tax(cgst_sgst,cgst,sgst,igst,amount_payable)
                      csv <<[designer_id,gst_no,(vendor_payout.order_confirmed_date.presence || vendor_payout.order_created_date).try(:strftime,'%d/%m/%Y'),(vendor_payout.order_pickup_date.presence || vendor_payout.order_complete_date).try(:strftime,'%d/%m/%Y'),'', vendor_payout.order_number,vendor_payout.order_invoice_number,'',vendor_payout.transaction_rate,commission,net_commission,cgst_tax,sgst_tax,igst_tax,amount_payable,'','','','',vendor_payout.customer_state,vendor_state]
                      total_commission+=net_commission; total_cgst+=cgst_tax; total_sgst+=sgst_tax; total_igst+=igst_tax;
                    end
                  end
                  filename  = 'CommissionReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
                  AwsOperations.create_aws_file(filename, file, false)
                  download_url = AwsOperations.get_aws_file_path(filename)
                  filename  = 'SalesReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
                  file_s = CSV.generate(headers: true) do |csv|
                    csv << sales_header
                    sales_report.each {|row| csv << row}
                  end
                  CSV.open(sales_file, "a", {:col_sep => "\t"}) do |csv|
                    sales_report.each {|row| csv << row}
                  end
                  AwsOperations.create_aws_file(filename, file_s, false)
                  sales_url = AwsOperations.get_aws_file_path(filename)
        
                  filename  = 'SalesReturnReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
                  file_sr = CSV.generate(headers: true) do |csv|
                    csv << sales_return_header
                    sales_return_report.each {|row| csv << row}
                  end
                  CSV.open(sales_return_file, "a", {:col_sep => "\t"}) do |csv|
                    sales_return_report.each {|row| csv << row}
                  end
                  AwsOperations.create_aws_file(filename, file_sr, false)
                  sales_return_url = AwsOperations.get_aws_file_path(filename)
        
                  commission_invoice = CommissionInvoice.create(designer_id: designer_id, commission: total_commission.round(2),total: total_amount, cgst: total_cgst.round(2),igst: total_igst.round(2),sgst: total_sgst.round(2), tcs_cgst: total_tcs_cgst.round(2), tcs_sgst: total_tcs_sgst.round(2), tcs_igst: total_tcs_igst.round(2), commission_report_url: download_url, tcs_report_url: nil, sales_report_url: sales_url, sales_return_report_url: sales_return_url)
                  invoice_ids << commission_invoice.id
                  DesignerOrder.unscoped.where(id: all_items.map(&:designer_order_id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)
                  ReverseCommission.where(id: reverse_commissions[designer_id].to_a.collect(&:id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)
                  #Adjustment.where(id: (other_adjustments + order_adjustments)).update_all(commission_invoice_id: commission_invoice.id)
                  VendorPayout.where(id: vendor_payouts[designer_id].to_a.collect(&:id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)
        
                  irn_number, irn_barcode, error_msg, items = nil, nil, nil, []
                  # if DISABLE_ADMIN_FUCTIONALITY['irn_feature'] && (designer = Designer.where(id: designer_id).last).present? && designer.gst_no?
                  #   items =[{:name=>"Online Marketing Commission", :quantity=>1, :total_price=> total_commission.round(2), igst: total_igst.round(2), cgst: total_cgst.round(2), sgst: total_sgst.round(2), :hsn_code=>"996211", :gst_rate=>18.00}]
                  #   payout_invoice_id = commission_invoice.id + PAYOUT_INVOICE_ID
                  #   begin
                  #     irn_request  += "CommissionInvoiceId: #{commission_invoice.id}<br>Invoice Number: #{payout_invoice_id}<br>Items: #{items}<br><br>"
                  #     generate_irn = GenerateIrnNumber.new(Order.new, items, commission_invoice, 1.0, 'INR', designer, payout_invoice_id, args[:einv_date])
                  #     response = generate_irn.generate_forward_irn
                  #     if response[:error] == false
                  #       irn_number, irn_barcode = response[:irn_number], response[:irn_barcode]
                  #       commission_invoice.update_columns(irn_number: irn_number, irn_barcode: irn_barcode)
                  #     else
                  #       error_msg  = response[:error_msg]
                  #     end
                  #   rescue => e
                  #     error_msg = "#{e.message} ===> #{e.backtrace}"
                  #   end
                  #   irn_error_hash += "CommissionInvoiceId: #{commission_invoice.id}<br>Invoice Number: #{payout_invoice_id}<br>Items: #{items}<br>Reason: #{error_msg}<br><br>" if error_msg.present?
                  # end
        
                  if total_commission.to_f > 0
                    method_args = [payout_date,invoice_date]
                    method_args << [irn_number, irn_barcode] if irn_number.present? && irn_barcode.present?
                    commission_invoice.generate_payout_invoice(*method_args.flatten)
                  else
                    Adjustment.where(amount: (-1*total_commission).round(2),designer_id: designer_id,notes: 'Negative Commission',status: 'unpaid').first_or_create
                  end
                rescue => e
                  errors_hash[designer_id] = "#{e.message}#{e.backtrace}"
                end
              end
            end
            filename  = 'combinedSalesReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
            AwsOperations.create_aws_file(filename, File.open(sales_file))
            sales_download = AwsOperations.get_aws_file_path(filename)
            
            filename  = 'combinedSalesReturnReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
            AwsOperations.create_aws_file(filename, File.open(sales_return_file))
            sales_return_download = AwsOperations.get_aws_file_path(filename)
            
            OrderMailer.report_mailer("Combined_Sales_And_Return_Report - #{Date.today.strftime('%b%Y')}","Combined Sales Report - #{sales_download} <br> Combined Sales Return Report - #{sales_return_download}",{'to_email'=> irn_alert_email_array.third,'from_email_with_name'=>'<EMAIL>'},{}).deliver
            ReverseCommission.mail_invoice_and_report_links(invoice_ids,irn_alert_email_array.third).deliver
            if errors_hash.present?
              ExceptionNotify.sidekiq_delay.notify_exceptions("Commission Report Error" ,"CommissionReport could not be generated for following designers.",{params: errors_hash}) 
            end
            OrderMailer.report_mailer("All B2B IRN Request #{Date.today.strftime('%b%Y')}","<br>#{irn_request}",{'to_email'=> irn_alert_email_array.first,'from_email_with_name'=>'<EMAIL>'},{}).deliver
            OrderMailer.report_mailer("Error While Creating B2B IRN Invoice","Failed to generate IRN number due to below reason <br>#{irn_error_hash}",{'to_email'=> irn_alert_email_array,'from_email_with_name'=>'<EMAIL>'},{}).deliver if irn_error_hash.present? 
            ExceptionNotify.sidekiq_delay.notify_exceptions('B2B IRN NUMBER Generation Error','B2B IRN NUMBER Generation Error',{ errors: irn_error_hash}) if irn_error_hash.present?
          end
    end
end