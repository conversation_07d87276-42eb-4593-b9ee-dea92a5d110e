class DeleteProcessingSkusJob
    include Sidekiq::Worker

    LOCK_TIMEOUT = 3600
    sidekiq_options queue: 'high'
  
    def perform(designer_batch_id, user_email)
        lock_key = "lock:delete_designs:#{designer_batch_id}"
        if acquire_lock(lock_key)
            begin
              delete_processing_designs_for_batch(designer_batch_id)
              deletion_info = {
                batch_id: designer_batch_id,
                deleted_at: Time.current,
                deleted_by: user_email
              }
              MirrawAdminMailer.processing_skus_notification(deletion_info).deliver_now
            ensure
              release_lock(lock_key)
            end
        end
    end

    private
    def acquire_lock(lock_key)
        Redis.current.set(lock_key, "locked", nx: true, ex: LOCK_TIMEOUT)
    end
    
    def release_lock(lock_key)
        Redis.current.del(lock_key)
    end

    def delete_processing_designs_for_batch(designer_batch_id)
        designer_batch = DesignerBatch.find(designer_batch_id)
        designer_batch.designs.where(state: :processing).each do |design|
            design.skip_after_save_callback=true
            design.destroy
        end
        designer_batch.failed_designs.destroy_all
        designer_batch.version_failed_designs.destroy_all
        if !designer_batch.designs.exists?
            designer_batch.destroy
        end
    end
  end
