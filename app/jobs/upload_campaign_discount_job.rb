class UploadCampaignDiscountJob
    include Sidekiq::Worker
    sidekiq_options queue: 'critical', retry: false
  
    def perform(csv_file_path, email, seller_campaign_id, designer_id)
        begin
            service = UploadCampaignDiscountService.new(csv_file_path, email, seller_campaign_id, designer_id)
            service.run
        rescue => exception
            DesignerMailer.send_progress_notification("Failed to process sheet #{design_id} due to #{exception.message}",email).deliver_now!
        end
    end
  end
  