class DesignVideoUploadJob
    include Sidekiq::Worker
    sidekiq_options queue: 'critical', retry: false
  
    def perform(email, csv_file_path)
      begin
        service = VideoUploadService.new(email, csv_file_path)
        service.run
      rescue => exception
        DesignerMailer.send_progress_notification("Design Video Upload Failed due to #{exception.message}",email).deliver_now!
      end
    end
end