class BetaBoostProductJob
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: 3

  def perform
    upcoming_boost_ids = BoostedDesign.scheduled.order(:created_at).pluck(:id)

    errors = []
    upcoming_boost_ids.each do |boost_id|
      begin
        VendorBoostProduct::BoostProductService.new(boost_id).call
      rescue => e
        Rails.logger.error "[BoostProductJob] Failed for boost_id=#{boost_id}: #{e.class} - #{e.message}"
        errors << { boost_id: boost_id, error: "#{e.class}: #{e.message}" }
      end
    end
    BatchIndexJob.perform_async

    if errors.any?
      error_message = "[BoostProductJob] Errors occurred during boost processing:\n" +
                      errors.map { |err| "Boost ID #{err[:boost_id]}: #{err[:error]}" }.join("\n")

      ExceptionNotify.sidekiq_delay.notify_exceptions('Boost Product Job failed', error_message, {})
    end
  end
end
