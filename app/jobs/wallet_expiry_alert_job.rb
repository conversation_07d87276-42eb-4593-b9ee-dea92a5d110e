class WalletExpiryAlertJob
  include Sidekiq::Worker
  sidekiq_options queue: :default

  def perform(days_before_string)
    days_before_list = (days_before_string || "").split(' ').map(&:to_i)

    days_before_list.each do |day_before|
      target_date = Date.today + day_before
      range = target_date.beginning_of_day..target_date.end_of_day

      Wallet.includes(:user).where(referral_expires_at: range).find_each do |wallet|
        send_alert_email(wallet, day_before)
      end
    end
  end

  private

  def send_alert_email(wallet, day_before)
    if day_before == 0
      WalletMailer.expired_alert(wallet.user, wallet).deliver_later
    else
      WalletMailer.prior_expiry_alert(wallet.user, wallet, day_before).deliver_later
    end
  end
end
