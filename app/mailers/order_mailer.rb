class OrderMailer < ActionMailer::Base
  track user: lambda { Account.find_by email: message.to.try(:first) }

  helper :designer_orders,:orders
  helper <PERSON>Helper
  # Folks who abandon cart
  #

  include FestPromotions

  def send_mail_to_customer_regarding_po_box_issue(user_name,email)
    @name = user_name
    mail(to: email , from: NO_REPLY_EMAIL , subject: 'Request for address update')
  end

  def admin_category_stats_mailer(start_date,end_date,email)
    if start_date > end_date
      start_date,end_date = end_date,start_date
    end
    @start_date = start_date
    @end_date = end_date
    parent_categories = {}
    PARENT_CATEGORIES.each do |category|
      parent_categories[category.titleize] = Category.names_to_ids(category).values
    end
    data = Admin.get_category_data(start_date,end_date,parent_categories)
    stats = data[:stats].sort_by{|k,v| -v["total_revenue"].to_f}.to_h
    parent_stats = data[:parent_stats].sort_by{|k,v| -v["total_revenue"].to_f}.to_h
    sku_count = Admin.set_categorywise_sku_count
    categories = Category.get_name_to_ids
    path = "/tmp/"
    filename = 'parent_stats.csv'
    filepath = path + filename
    CSV.open(filepath, "wb", {col_sep: "\t"}) do |csv|
      csv << ['SKU Count','Category','International Revenue','International Orders','Domestic Revenue','Domestic Orders','Total Revenue','Total Orders','Conversion Rate (Mobile)','Conversion Rate (Desktop)']
    end
    parent_data_buffer=[]
    for k,v in parent_categories
      id = v[0]
      parent_data_buffer << [sku_count.values_at(*v).map(&:to_i).sum, k, parent_stats[id]['mobile_&_apps_international_revenue'].to_f + parent_stats[id]['desktop_international_revenue'].to_f, parent_stats[id]['mobile_&_apps_international_orders'].to_i + parent_stats[id]['desktop_international_orders'].to_i, parent_stats[id]['mobile_&_apps_domestic_revenue'].to_f + parent_stats[id]['desktop_domestic_revenue'].to_f, parent_stats[id]['mobile_&_apps_domestic_orders'].to_i + parent_stats[id]['desktop_domestic_orders'].to_i, parent_stats[id]['total_revenue'].to_f, parent_stats[id]['total_orders'].to_i, parent_stats[id]['mobile_conversion_rate'].to_f, parent_stats[id]['desktop_conversion_rate'].to_f]
    end
    parent_data_buffer = parent_data_buffer.sort{|a,b| b[6]<=>a[6]}
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      parent_data_buffer.each do |data|
        csv << data
      end
    end
    attachments['parent_stats.csv'] = {mime_type: 'text/csv',content: File.read(filepath) }
    data_buffer=[]
    filename = 'stats.csv'
    filepath = path + filename
    CSV.open(filepath, "wb", {col_sep: "\t"}) do |csv|
      csv << ['SKU Count','Category','International Revenue','International Orders','Domestic Revenue','Domestic Orders','Total Revenue','Total Orders','Conversion Rate (Mobile)','Conversion Rate (Desktop)']
    end
    for k,v in categories
      data_buffer << [sku_count[k].to_i, v, stats[k]['mobile_&_apps_international_revenue'].to_f+stats[k]['desktop_international_revenue'].to_f, stats[k]['mobile_&_apps_international_orders'].to_i + stats[k]['desktop_international_orders'].to_i, stats[k]['mobile_&_apps_domestic_revenue'].to_f + stats[k]['desktop_domestic_revenue'].to_f, stats[k]['mobile_&_apps_domestic_orders'].to_i + stats[k]['desktop_domestic_orders'].to_i, stats[k]['total_revenue'].to_f, stats[k]['total_orders'].to_i, stats[k]['mobile_conversion_rate'].to_f, stats[k]['desktop_conversion_rate'].to_f]
    end
    data_buffer = data_buffer.sort{|a,b| b[6]<=>a[6]}
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << data
      end
    end
    attachments['stats.csv'] = {mime_type: 'text/csv',content: File.read(filepath) }
    mail(:from => NO_REPLY_EMAIL,
     :to => email,
     :subject => 'Please Find Attached CSV containg Category Stats Report'
     )
  end

  def cart_abandonment_notification(cart_id)
    @cart = Cart.preload(line_items: [design: [:designer,:images,:categories]]).where(id: cart_id).first
    from_email_with_name = @cart.is_luxe_cart?  ? LUXE_EMAIL : NO_REPLY_EMAIL
    to_email = @cart.email
    template = @cart.is_luxe_cart? ? 'cart_abandonment_notification_luxe' : 'cart_abandonment_notification'
    if (@line_items_count = @cart.line_items.size) != 0
      country_code = @cart.line_items.last.get_country_code
      @currency_symbol = CurrencyConvert.currency_convert_memcached.find{|x| x.country_code==country_code}.try(:symbol) || 'USD'
      subject = if @line_items_count > 0
        "You have #{@line_items_count} item(s) left in your cart."
      else
        'It seems like You were interested!'
      end

      mail(:from => from_email_with_name,
       :to => to_email,
       :subject => subject,
       :template_name => template
       )
    end
  end

  def cart_sold_out(cart_id)
    @cart = Cart.preload(line_items: [design: [:designer,:images,:categories]]).where(id: cart_id).first
    from_email_with_name = NO_REPLY_EMAIL
    to_email = @cart.email
    if (@line_items_count = @cart.line_items.size) != 0
      country_code = @cart.line_items.last.get_country_code
      @currency_symbol = CurrencyConvert.currency_convert_memcached.find{|x| x.country_code==country_code}.try(:symbol) || 'USD'
      subject = if @line_items_count > 0
        "You have #{@line_items_count} item(s) left in your cart."
      else
        "Get it before it's gone."
      end

      mail(:from => from_email_with_name,
       :to => to_email,
       :subject => subject 
       )
    end
  end

  #
  # Whose order got canceled
  #
  
  def convey_cancel_update_to_buyer(order_id)
    @order = Order.find order_id
    unless @order.predict_good_orders?
      
      from_email_with_name = NO_REPLY_EMAIL
      to_email_with_name =  "\"#{@order._billing_name}\" <#{@order._billing_email}>"
      
      if @order.gharpay_available?
        @gharpay_available = true
      end
      
      if @order.gharpay?
        @gharpay = true
        subject = "#{@order.billing_name} - Your Cash Before Delivery Order #{@order.number} has been canceled."
      else
        @gharpay = false
        subject = "#{@order.billing_name} - Order #{@order.number} has been canceled - Retry Now"
      end
      @designs_suggestion = @order.complete_the_look_suggestions.presence || Bestseller.bestseller_designs(3)

      sent_count = (@order.cancel_mail_sent_count || 0).next
      @order.update_columns(:cancel_mail_sent_at => DateTime.now, :cancel_mail_sent_count => sent_count)
      
      mail(:from => from_email_with_name,
        :to => to_email_with_name,
        :subject => subject)
    end
  end

  #
  #  Mailers to Buyers.
  #

  def mail_order_details_to_buyer(order)
    self.ahoy_options = AhoyEmail.default_options.merge({click: false, message: false})
    @order = order
    @fest = fest_info["date"]
    @fest_content = fest_info["content"]
    from_email_with_name = order.is_luxe_order?  ? LUXE_EMAIL : NO_REPLY_EMAIL
    to_email_with_name = "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    designer_ids = DesignerOrder.where(order_id: order.id).pluck(:designer_id)
    if designer_ids.uniq.count <= ESSENTIAL_DESIGNERS['designer_ids'].count && (ESSENTIAL_DESIGNERS['designer_ids'].map(&:to_i) & designer_ids.uniq) == designer_ids.uniq
      @essentials_delivery_date = Time.now.advance(days: ESSENTIAL_DESIGNERS['max_eta']).strftime('%d %b %y')
      @essentials_delivery_show = true
    end
    @designs_suggestion = order.complete_the_look_suggestions.presence || Bestseller.bestseller_designs(3)
    
    subject = "Order Confirmation #{order.number}"
    # if order.cod? && @order.pending?
    #   subject = "Order Acknowledgment #{order.number} | Pending confirmation"
    # end
    template = order.is_luxe_order? ? 'mail_order_details_to_buyer_luxe' : 'mail_order_details_to_buyer' 
    mail(:from => from_email_with_name,
     :to => to_email_with_name,
     :bcc => "<EMAIL>",
     :subject => subject,
     :template_name => template
    )
  end
  
  def mail_tracking_info_to_buyer(order, designer_order)
    @designer_order = designer_order
    @order = order
    @designer = @designer_order.designer
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    @tracking_mail = true
    @count_of_boutiques = @order.designer_orders.count
    
    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => "Tracking information for Order #{order.number}.")
  end

  def mail_late_delivery_wallet_credit(order_id, reward, currency_symbol)
    @order = Order.find order_id
    @reward = reward
    @currency_symbol = currency_symbol

    mail(from: NO_REPLY_EMAIL,
         to: "\"#{@order._billing_name}\" <#{@order._billing_email}>",
         subject: "Your Mirraw wallet is credited with #{currency_symbol} #{reward}")
  end
  
  def mail_tracking_info_to_international_buyer(order,after_partial_dispatch=false)
    @order = order
    @after_partial_dispatch = after_partial_dispatch
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    @tracking_mail = true
    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => "Tracking information for Order #{order.number}.") 
  end

  def mail_partial_dispatch_tracking_info_to_international_buyer(order,shipment)
    @order = order
    @shipment = shipment
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "\"#{order._billing_name}\" <#{order._billing_email}>"
    @tracking_mail = true
    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => "Partial Dispatch Tracking information for Order #{order.number}.")
  end
  
  def gharpay_payment_confirmed(order)
    @order = order
    
    from_email_with_name = NO_REPLY_EMAIL   
    to_email_with_name = "#{@order._billing_name} <#{@order._billing_email}>"
    
    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => "Thank you: Payment Received for Order #{order.number}")
  end
  
  def pickedup_notification_to_user(order, designer_order)
    @designer_order = designer_order
    @order = order
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "\"#{@order.billing_name}\" <#{@order.billing_email}>"

    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => "Order Processing #{@order.number} ")
  end

  def dispatch_order_to_designer(order, designer_order)
    @designer_order = designer_order
    @order = order    

    @designer = @designer_order.designer
    @edit_invoice_link = 'http://seller.mirraw.com/designers/edit_invoice/' + Base64.urlsafe_encode64("#{order.id}/#{designer_order.id}")
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @designer_dispatch = 1
    
    if @order.state == "new" || @order.state == "pending"
      subject = "Order #{order.number} - Pending Payment. Do NOT dispatch until confirmation mail."
    else
      subject = "Order #{order.number} has been confirmed."
      @show_invoice_pdf = true
      attachments[@order.number + ".pdf"] = WickedPdf.new.pdf_from_string(
       render_to_string(:pdf => @order.number, :template => 'designer_orders/show.html.haml')
       )
      self.instance_variable_set(:@lookup_context, nil)
    end
    
    # Designer Order count
    designer_order_count = @order.designer_orders.count
    
    @ship_to_mirraw = true
    
    mail(:from => from_email_with_name,
     :to => @designer_order.designer.email,
     :subject => subject)
  end

  def convey_status_update_to_designer(order, designer_order)
    @designer_order = designer_order
    @order = order
    
    @designer = @designer_order.designer
    from_email_with_name = "Mirraw.com <<EMAIL>>"

    mail(:from => from_email_with_name,
         :to => @designer_order.designer.email,
         :subject => "Order #{order.number}: Status Update - " + designer_order.state.upcase)    
  end

  def convey_cancel_update_to_designer(designer_order, line_item = nil)
    @line_item = line_item
    @designer_order = designer_order
    @order = @designer_order.order
    from_email_with_name = "Mirraw.com <<EMAIL>>"

    if @order.gharpay?
      @gharpay = true
    else
      @gharpay = false
    end

    if @designer_order.pickup.present? && @designer_order.ship_to == 'mirraw'
      @designer_order.generate_credit_note
      attachments["Credit_note_#{@order.number}.pdf"] = {
        mime_type: 'application/pdf',
        content: open(@designer_order.credit_note_url).read
      } if @designer_order.credit_note_url.present?  
    end

    mail(:from => from_email_with_name,
         :to => @designer_order.designer.email,
         :subject => "Order #{@order.number} has been canceled.")
  end

  def sendout_pending_order_notifications(designer, designer_orders)
    @designer = designer
    @designer_orders = designer_orders
    @dispatched_designer_orders = @designer.designer_orders.where('created_at > ? AND state = ? AND pickup IS NOT NULL', 1.month.ago, 'dispatched')
    @critical_orders = @designer.designer_orders.where('state = ?', 'critical')
    
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    
    mail(:from => from_email_with_name,
         :to => @designer.email,
         :subject => @designer_orders.count.to_s + ' order(s) needs to be dispatched.')
  end
  
  def cancel_order_update_to_user(order)
    @order = order
    ActiveRecord::Associations::Preloader.new.preload(@order, {line_items: {design: [:designer, :images]}})
    @from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name =  "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    subject = "#{order.billing_name} - Your Order #{order.number} has been canceled."
    mail(:from => @from_email_with_name,
         :to => to_email_with_name,
         :subject => subject)
  end

  
  def send_fraud_alert_to_designers(order, designer_order)
    @order = order
    
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    
    mail(:from => from_email_with_name,
     :to => designer_order.designer.email,
     :subject => "Fraud Alert: Order #{order.number}")
  end
  
  def send_fraud_alert_to_buyer(order)
    @order = order                      

    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "#{@order._billing_name} <#{@order._billing_email}>"
    mail(:from => from_email_with_name,
     :to => to_email_with_name,
     :subject => "SOR tag debug #{order.number}")
  end

  #
  # Notifications to admins
  #
  
  def pending_notifications_to_admin()
    designer_orders = DesignerOrder.where('created_at > ? AND state = ?', 1.month.ago, 'pending')
    @order_hash = designer_orders.group_by {|d| d.designer }

    from_email_with_name = "Mirraw.com <<EMAIL>>"   
    
    mail(:from => from_email_with_name,
         :to => '<EMAIL>',
         :subject => 'List of pending orders.')
  end
  
  def dispatch_notifications_to_admin()
    designer_orders = DesignerOrder.where('created_at > ? AND state = ?', 1.month.ago, 'dispatched')
    @order_hash = designer_orders.group_by {|d| d.designer }

    from_email_with_name = "Mirraw.com <<EMAIL>>"   
    
    mail(:from => from_email_with_name,
         :to => '<EMAIL>',
         :subject => "List of dispatched Orders")
  end
  
  def send_wholesale_request(wholesale)
    @wholesale = wholesale
    mail(:from => @wholesale.email,
         :to => '<EMAIL>',
         :subject => "Wholesale Enquiry")
  end
  
  def ship_direct(order)
    @order = order
    @designer = order.designer_orders.first.designer
    
    mail(:from => "Mirraw.com <<EMAIL>>",
         :to => @designer.email,
         :subject => "Please ship #{@order.number} directly")
  end
  
  def send_csv_to_aashi(time_orders_path, all_orders_path)
    attachments["aashi_recent_orders.csv"] =  File.read(time_orders_path)
    attachments["aashi_all_orders.csv"] =  File.read(all_orders_path)
    mail(:from => '<EMAIL>',
         :to => '<EMAIL>',
         :cc => ['<EMAIL>, <EMAIL>'],
         :subject => "Mirraw.com: Pending Order list")    
  end
  
  def send_mail_for_refund_completed(order, type = nil)
    @order = order
    @type = type
    from_email_with_name = NO_REPLY_EMAIL
    to_email = order.billing_email
    subject = "Refund Completed For Order No : " + order.number + " - Mirraw.com"
    mail(:from => from_email_with_name,
         :to => to_email,
         :subject => subject)
  end
  
  def send_mail_for_min_req(order)
    @order = order
    from_email_with_name = NO_REPLY_EMAIL
    to_email = order.billing_email
    subject = 'Minimum requirement for Order No : '+ order.number + " - Mirraw.com"
    mail(:from => from_email_with_name,
         :to => to_email,
         :subject => subject)
  end

  def send_mail_ccavenue_payments_status(orders)
    @orders = orders
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "<EMAIL>"
    subject = "Details of CCANVENUE Based Orders For Yesterday : " + Date.yesterday.strftime('%a, %d %b') 
    mail(:from => from_email_with_name,
         :to => to_email_with_name,
         :subject => subject)
  end

  def request_cod_confirmation(order)
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "#{order._billing_name} <#{order._billing_email}>"
    subject = 'COD Order verification request'
    @order = order
    mail(:to => to_email_with_name,
         :from => from_email_with_name,
         :subject => subject)
  end
  
  def order_processing_notification(order_id, flag_24_hrs=nil)
    @order = Order.find_by_id(order_id)
    if flag_24_hrs.present?
      @order.email_logs.create!(:email_type => 'order_processing_notification_24_hrs')
    else
      @order.email_logs.create!(:email_type => 'order_processing_notification')
    end

    to_email_with_name = "\"#{@order.name}\" <#{@order.email}>"
    from_email_with_name = NO_REPLY_EMAIL

    options = {:from => from_email_with_name, :to => to_email_with_name, :subject => "Mirraw: Order #{@order.number} Status"}

    bcc_mail_flag = SystemConstant.get('BCC_MAIL_ORDER_PROCESSING_NOTIFICATION')
    bcc_mail_id = SystemConstant.get('BCC_MAIL_ID')
    
    options[:bcc] = bcc_mail_id if bcc_mail_flag.present? && bcc_mail_flag == '1' && bcc_mail_id.present?

    mail(options)
  end

  def stitching_measurement(id)
    self.ahoy_options = AhoyEmail.default_options.merge({click: false, message: false})
    @order = Order.find_by_id(id)

    @order.email_logs.create!(:email_type => 'stitching_followup')
    @fest = fest_info["date"]
    @fest_content = fest_info["content"]

    to_email_with_name = "\"#{@order.name}\" <#{@order.email}>"
    from_email_with_name = "Mirraw.com Stitching Team <<EMAIL>>"
    cc_email_with_name = "Mirraw.com Stitching Team <<EMAIL>>"

    options = {from: from_email_with_name, to: to_email_with_name, cc: cc_email_with_name, subject: "ACTION REQUIRED: Please provide Stitching Measurements for order #{@order.number} on Mirraw.com"}

    bcc_mail_flag = SystemConstant.get('BCC_MAIL_ORDER_STITCHING_FOLLOWUP')
    bcc_mail_id = SystemConstant.get('BCC_MAIL_ID')

    # options[:bcc] = bcc_mail_id if bcc_mail_flag.present? && bcc_mail_flag == '1' && bcc_mail_id.present?

    mail(options)
  end

  def stitching_summary(summary, date)
    @summary = summary
    to_email =  SystemConstant.get('STITCHING_SUMMARY_MAIL_ID')
    from_email_with_name = NO_REPLY_EMAIL
    @subject = "Stitching Summary :  #{date.strftime('%d-%m-%Y')}"
    mail(to: to_email,
         from: from_email_with_name,
         subject: @subject)
  end

  def report_download_link(link, email)
    @link = link
    to_email =  email
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @subject = "Download Report Link "
    mail(to: to_email,
         from: from_email_with_name,
         subject: @subject)
  end

  def send_feedback_form(order)
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "\"#{order._billing_name}\" <#{order._billing_email}>"
    subject = if order.notification_count.to_i <= 1
                "Feedback form - #{order.try(:name).to_s.split(' ')[0]} please provide your feedback regarding order (#{order.number})"
              elsif order.notification_count.to_i == 2
                "We’d love to hear from you! Please provide your feedback regarding order (#{order.number})"
              elsif order.notification_count.to_i == 3
                "Are you satisfied with your order (#{order.number})? Please share your feedback with us"
              else
                "Give us a chance to serve you better! Please share your feedback with us"
              end
    @order = order
    # Halted for later release
    # @order.delay.send_feedback_notification
    mail(:to => to_email_with_name,
         :from => from_email_with_name,
         :subject => subject)
  end

  def multiple_payment_invoice(order,ccavenue_invoice_number="")
    @order=order
    if @order.international? || @order.billing_international?
      @paypal_link = pay_by_paypal_url(number: order.number)
    else
      @payu_link = order.payu_billing_page_url_non_seamless(order_payu_response_url)
      @ccavenue_link = "http://www.ccavenue.com/mer_register/sendInvoice/sendInvoiceRedirect_ccav.jsp?inv=#{ccavenue_invoice_number}&pass=21501"
      if order.cod_available
        @cod_link = order_retry_cod_url(id: order.number)
      end
    end
    to_email_with_name = "#{@order.billing_name} <#{@order.billing_email}>"
    from_email_with_name = NO_REPLY_EMAIL
    mail(to: to_email_with_name, from: from_email_with_name, subject: "Complete your transaction").deliver
  end

  def send_feedback_mail(name, feedback)
    subject = "Hi! There's a paypal transaction feedback from " + name
    @feedback = feedback
    from_email_with_name = NO_REPLY_EMAIL
    to_email_with_name = "<EMAIL>"
    mail(:from => from_email_with_name,
      :to => to_email_with_name,
      :subject => subject).deliver
  end

  def items_not_received_yet(order_id)
    @order = Order.where(id: order_id).first
    subject = "Mirraw.com Order Number #{@order.number} Updated Status"
    from_email_with_name =  NO_REPLY_EMAIL
    to_email_with_name =  "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    mail(to: to_email_with_name, from: from_email_with_name, subject: subject)
  end

  def all_line_items_notification(order_id,type)
    @order = Order.where(id: order_id).first
    @type = type
    subject = "Mirraw.com Order Number #{@order.number} Updated Status"
    from_email_with_name =  NO_REPLY_EMAIL
    to_email_with_name =  "\"#{@order._billing_name}\" <#{@order._billing_email}>"
    mail(to: to_email_with_name, from: from_email_with_name, subject: subject)
  end

  def list_items_not_received_yet(orders)
    subject = "List of Orders with items not received since 5 Days!"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    to_email = "<EMAIL>"
    attachments["ORDER_LIST_#{Time.zone.now.strftime('%v')}.csv"] =
      CSV.generate do |csv|
        csv << ['number']
        orders.each do |order_number|
          csv << [order_number]
        end
      end
    bcc = ['<EMAIL>','<EMAIL>','<EMAIL>']
    mail(to: to_email, from: from_email_with_name, bcc: bcc ,subject: subject)
  end

  def send_feedback_form_stitching(user, stitching_order_count)
    subject = "Hi! There's a Stitching feedback from "
    @stitching_order_count = stitching_order_count
    from_email_with_name = NO_REPLY_EMAIL
    mail(from: from_email_with_name, to: user.email , subject: subject)
  end

  def send_stitching_info_label(order_id)
    @order = Order.where(id: order_id).first
    subject = "Hi! Please check the stitching summary"
    @stitching_measurements = @order.stitching_measurements.includes(design: [:designer,:images])
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(from: from_email_with_name,to: @order.email, subject: subject)
  end


  def payout_reports(link,filename,email,other_data=nil)
    @faulty_data = other_data[:faulty_data] if other_data.present?
    @link = link
    to_email =  email
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    @subject = filename
    attachments["Adjustment Report.csv"] = other_data[:attachment] if (other_data.present? && other_data[:attachment].present?)
    mail(to: to_email,
         from: from_email_with_name,
         subject: @subject)
  end

  def report_mailer(subject,content,emails,other_data={})
    to_email =  emails['to_email']
    from_email_with_name = emails['from_email_with_name']
    cc = emails['cc_email']
    other_data.each do |filename,file|
      attachments[filename] = file if file.present?
    end
    @content = content  
    mail_params = {to: to_email,from: from_email_with_name,subject: subject}
    mail_params.merge!({cc: cc}) if cc.present?
    mail(mail_params)
  end

  def send_addons_invoice(order_id,additional_payment_id)
    @order= Order.find_by_id(order_id)
    if @order.international? || @order.billing_international?
      @paypal_link = pay_by_paypal_url(number: @order.number,addons: additional_payment_id)
    else
      @payu_link = @order.payu_billing_page_url_non_seamless(order_payu_response_url,false,additional_payment_id)
    end
    to_email_with_name = "#{@order.billing_name} <#{@order.billing_email}>"
    from_email_with_name = NO_REPLY_EMAIL
    mail(to: to_email_with_name,cc:'<EMAIL>', from: from_email_with_name, subject: "#{@order.number} Complete your Addon/Stitching Transaction")
  end

  def send_addon_payment_success_mail(order_id)
    @order = Order.find_by_id(order_id)
    to_email_with_name = "#{@order.billing_name} <#{@order.billing_email}>"
    from_email_with_name = NO_REPLY_EMAIL
    mail(to: to_email_with_name, from: from_email_with_name, subject: "#{@order.number} Transaction Success")
  end

  def send_potential_orders_for_dispatch(email,order_type)
    to_email = email
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    case order_type
    when 'dispatch_potential'
      subject = "Potential Order #{Time.zone.now.strftime('%a, %d %b %r')}"
      potential_orders = Order.get_potential_order_list
    when 'tailoring_potential'
      subject = "Tailoring Potential Order #{Time.zone.now.strftime('%a, %d %b %r')}"
      potential_orders = Order.get_tailoring_potential_order_list
    else
      subject = 'Not Found'
    end
    if potential_orders.present? && to_email.present?
      attachments["potential_order.csv"] = potential_orders 
      mail(to: to_email, from: from_email_with_name, subject: subject)
    end
  end

  def send_issue_qcfail_mail_to_user(order_id, issue, line_item_id: nil)
    @order = Order.where(id: order_id).first
    @issue = issue
    @item = LineItem.find_by_id(line_item_id) if line_item_id.present?
    subject = "Hi! There's issue in your order #{@order.number}"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(from: from_email_with_name, to: @order.email, subject: subject, cc: '<EMAIL>')
  end

  def send_measurement_rejected_with_suggestions_mail_to_user(measurement_id)
    @stitching_measurement = StitchingMeasurement.preload(:order,design: :designer).where(id:measurement_id)
    @order = @stitching_measurement.first.order
    subject = "ACTION REQUIRED: Your Stitching Measurement require changes on Mirraw.com.Please check the suggested measurements"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(from: from_email_with_name, to: @order.email, subject: subject, cc:'<EMAIL>')
  end

  def invalid_address_mail(order_id)
    @order = Order.where(id: order_id).first
    subject = "Hi! There's issue in your shipping address"
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(from: from_email_with_name, to: @order.email, subject: subject)
  end

  def shipment_notification_for_delivered(designer_order)
    @designer_order = designer_order
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(:from => from_email_with_name,
         :to => @designer_order.designer.email,
         :bcc => "<EMAIL>",  #this is for testing purpose will remove it after two days  
         :subject => "#{@designer_order.order.number}: Buyer Return Delivered") 
  end

  def shipment_notification_for_rto_delivered(designer_order)
    @designer_order = designer_order
    from_email_with_name = "Mirraw.com <<EMAIL>>"
    mail(:from => from_email_with_name,
         :to => @designer_order.designer.email,
         :subject => "#{@designer_order.order.number}: RTO Delivered")    
  end

end