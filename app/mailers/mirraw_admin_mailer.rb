class MirrawAdminMailer < ActionMailer::Base
    default from: '<EMAIL>'
    def failed_order_notification(body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['cod_orders_panel'],
            subject: "Re <> Failed orders while update"
        }
        mail(mail_params)
    end

    def send_updated_data_status(subject,body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['discount_update_notifier'],
            subject: "Re <> #{subject}"
        }
        mail(mail_params)
    end

    def help_center_query_notification_email(subject, body, from_email)
        @content = body
        mail_params = {
          to: '<EMAIL>', 
          subject: subject,
          from: from_email 
        }
        mail(mail_params)
    end
    
    def send_payload_and_res(payload,response)
        @payload = payload
        @response = response
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['mail_payload'],
            subject: "Re <>  payload and response"
        }
        mail(mail_params) 
    end

    def processing_skus_notification(deletion_info)
        @deletion_info = deletion_info
        mail_params = {
            to: deletion_info[:deleted_by],
            subject: "Re <> Processing SKUs for batch #{deletion_info[:batch_id]}"
        }
        mail(mail_params)
    end

    def send_progress_notification(body)
        @content = body 
        mail_params = {
            to: ACCESSIBLE_EMAIL_ID['manual_grading_panel_access'],
            subject: "Re <> Design Grading Status"
        }
        mail(mail_params)
    end
end
