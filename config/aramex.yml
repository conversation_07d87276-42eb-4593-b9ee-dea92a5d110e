test: &default
  user_name: '<EMAIL>'
  password: 'R123456789$r'
  account_number: '********'
  account_pin: '331431'
  version: 'v1.0'
  account_entity: 'BOM'
  account_country_code: 'IN'
  grant_type: 'password'
  login_user_name: '<PERSON><PERSON><PERSON><PERSON>@aramex.com'
  csb_auth_token_url: 'https://export.in.aramex.net/devwebapi/AramexOtuboundToken'
  csb_url: 'https://export.in.aramex.net/Devwebapi/api/CSB/ManageCSB_V_RelatedInfo'

development:
  <<: *default

staging:
  <<: *default


production: &live
  user_name: '<EMAIL>'
  password: 'Abhishek@123'
  export_user_name: '<EMAIL>'
  export_password: 'omegaalpha'
  account_number: '********'
  account_pin: '443543'
  version: 'v1.0'
  account_entity: 'BOM'
  account_country_code: 'IN'
  grant_type: 'password'
  login_user_name: '<EMAIL>'
  csb_auth_token_url: 'https://export.in.aramex.net/webapi/AramexOtuboundToken'
  csb_url: 'https://export.in.aramex.net/webapi/api/CSB/ManageCSB_V_RelatedInfo'

admin:
  <<: *live

seller:
  <<: *live