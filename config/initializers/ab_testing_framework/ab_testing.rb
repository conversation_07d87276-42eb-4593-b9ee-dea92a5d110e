# Enjoy Experimentations!!
module AbTesting
  # Tester start and create experiment
  class Tester
    attr_accessor :validator, :moderator
    delegate :evaluate, to: :validator
    delegate :old?,:start, :current_experiments, :set_session, to: :moderator
    INVALID = 'Could not validate'.freeze

    # I decrypt session values of a user to experiment names.
    # I help Google Analytics.
    def decryptor experiment_id, type=nil
      if type.present?
        _session_mapper[type].find{|key, id| key if id == experiment_id}.try(:first) ||  ''.freeze
      else
        _session_mapper.each do |name, value|
          if (id = value.find{|key, id| key if id == experiment_id}).present?
            return id.first
          end
        end
        ''.freeze
      end
    end

    # I will set Experiments if you give me right ingredients
    def set name, options = {}
      _experiments[name].keys.each do |experiment_name|
        _set_attributes name, experiment_name, options
        if (evaluate @type, _validation_params(options)) && (old?(@type, @alias_id) || start(@type, _moderation_params(options)))
          return _response :result
        end
      end
      false
    end

    # Did pass some junk from your @<your-experiment-type>_testing.rb, access them here.
    def attribute_response type, key
      @attribute_response ||= Hash.new{|h,k| h[k] = {}}
      @attribute_response[type][key] ||= if (experiment = decryptor(current_experiments[type], type)).present?
        _set_attributes(type, experiment)
        _response key
      else
        false
      end
    end

    # I will tell you whats going on
    def running type
      moderator.send type
    end

    # They hate me because I destroy
    def remove_current_experiment type
      current_experiments[type] = nil
    end

    private

    ################## DO NOT LOOK FOR US, WE ARE SCARCE AND SCARED #####################
    def initialize(country_code, user_id,
                   experiments=nil, c_experiments=nil)
      experiments &= _valid_experiments if experiments.present?
      self.validator = Validator.new country_code
      self.moderator = Moderator.new user_id, experiments, c_experiments
    end

    def _response key
      @attributes[:moderations][key]
    end

    def _set_attributes type, experiment, params = {}
      @attributes = _dynamic_conditions[type][experiment]
      @type = type
      @alias_id = _experiment_hash experiment
    end

    def _validation_params options = {}
      options.merge(conditions: @attributes[:validations])
    end

    def _moderation_params options = {}
      options.merge(
      @attributes[:moderations]).merge(
      { experiment_id: @alias_id })
    end

    def _experiment_hash experiment
      _session_mapper[@type][experiment]
    end

    def _valid_experiments
      @@valid_experiments ||= _session_mapper.collect{|experiment, values| values.collect{|k,v| v}}.compact.flatten
    end

    def _session_mapper
      @@session_mapper ||= Rails.cache.fetch('RANDOMISE_EXPERIMENT_CONSTANT_AT_BOOT', expires_in: 24.hours) do
        AbTesting::Experiments.mapper(_experiments)
      end
    end

    def _experiments
      @@experiments||=AbTesting::Experiments.experiments
    end

    def _dynamic_conditions
      @@dynamic_conditions||=AbTesting::Experiments.prepared_conditions(_experiments)
    end
  end
end