require 'carrierwave'
# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = "https://beta.mirraw.com"
SitemapGenerator::Sitemap.sitemaps_host = "https://beta.mirraw.com"
SitemapGenerator::Sitemap.public_path = 'tmp/'
SitemapGenerator::Sitemap.sitemaps_path = 'sitemaps/'
SitemapGenerator::Sitemap.adapter = SitemapGenerator::WaveAdapter.new
SitemapGenerator::Sitemap.create_index = true
SitemapGenerator::Sitemap.namer = SitemapGenerator::SimpleNamer.new(:sitemap, :zero => '_index')
MOBILE_DOMAIN = "https://beta-mobile.mirraw.com"
SitemapGenerator::Sitemap.create do
  # Put links creation logic here.
  #
  # The root path '/' and sitemap index file are added automatically for you.
  # Links are added to the Sitemap in the order they are specified.
  #
  # Usage: add(path, options={})
  #        (default options are used if you don't specify)
  #
  # Defaults: :priority => 0.5, :changefreq => 'weekly',
  #           :lastmod => Time.now, :host => default_host
  #
  # Examples:
  #
  # Add '/articles'
  #
  #   add articles_path, :priority => 0.7, :changefreq => 'daily'
  #
  # Add all articles:
  #
  #   Article.find_each do |article|
  #     add article_path(article), :lastmod => article.updated_at
  #   end
  #
  # rake sitemap:refresh
  #
      @seo_list = SeoList.where('category_id IS NOT NULL')
      binding_table = Hash.new
      @seo_list.each do |seo|
        binding_table[seo.category.try(:name).try(:downcase)] = seo.label
      end

      url_table = RedirectRule.all.pluck('category_kind, route').to_h

      color_swatch = Hash.new
      categories = {'wedding-sarees' => '/store/wedding-sarees','cotton-sarees' => '/store/cotton-sarees'}
      colors = ['red','blue','black','green','cream','magenta','multicolor','beige','orange','yellow','maroon','white','brown','pink']
      url_append = '/colour-'
      categories.each do |k,v|
        colors.each do |c|
          color_swatch[k+'-'+c]=v+url_append+c
        end
      end

      group(:sitemaps_path => 'sitemaps/', :filename => :category) do
        Category.roots.each do |node|
          node.self_and_descendants.each do |category|
            category_name = category.name.try(:downcase)
            if binding_table[category_name].present?
              add store_search_path(binding_table[category_name]), :priority => 0.9, :changefreq => 'daily'
            elsif url_table[category_name].present?
              add url_table[category_name], :priority => 0.9, :changefreq => 'daily'
            else
              add store_search_path(category_name), :priority => 0.9, :changefreq => 'daily'
            end
          end
        end
      end

      color_swatch.each do |k,v|
        add v,:priority => 0.7, :changefreq => 'daily'
      end

      add page_path("about")
      add "/support/solutions", host: 'https://mirraw.freshdesk.com'
      add page_path("sell")

      add "/horoscopes", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/aries", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/taurus", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/gemini", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/cancer", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/leo", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/virgo", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/libra", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/scorpio", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/sagittarius", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/aquarius", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/capricorn", priority: 0.7, changefreq: 'daily'
      add "/horoscopes/daily/pisces", priority: 0.7, changefreq: 'daily'

      group(:sitemaps_path => 'sitemaps/', :filename => :products) do
        Design.preload(:designer,:images).published.find_each do |design|
          if design && design.images.present? && design.designer.present?
            images = Array.new
            design.images.each do |image|
              desc = design.description.present? ? design.description.truncate(140) : ""
              images << {:loc => image.photo.url(:zoom), :title => design.title, :caption => desc}
            end
            add(designer_design_path(design.designer, design), :images => images, :priority => 0.7, :changefreq => 'weekly')
          end
        end
      end

      group(:sitemaps_path => 'sitemaps/', :filename => :designers) do
        Designer.where(state_machine: ['approved', 'review', 'vacation']).find_each do |designer|
          if designer.present?
            add designer_path(designer), :priority => 0.8, :changefreq => 'daily'
            #add designer_coupons_path(designer)
          end
        end
      end

      group(:sitemaps_path => 'sitemaps/', :filename => :collections) do
        ActsAsTaggableOn::Tag.includes(:taggings).where('taggings.context' => 'collections').find_each do |tag|
          add store_collection_path(:collection => tag.name.downcase), :priority => 0.6, :changefreq => 'weekly'
        end
      end

      # group(:sitemaps_path => 'sitemaps/', :filename => :tags) do
      #   ActsAsTaggableOn::Tag.includes(:taggings).where('taggings.context' => 'tags','taggings.taggable_type' => 'Design').find_each do |tag|
      #     add search_tag_path(:q => tag.name), :priority => 0.6, :changefreq => 'weekly'
      #   end
      # end

      group(:sitemaps_path => 'sitemaps/', :filename => :fashion_updates) do
        FashionUpdate.find_each do |post|
          add fashion_update_path(post), :changefreq => 'weekly',:priority => 0.6
        end
      end

      # User.preload(:slugs).find_each do |user|
      #   add user_path(user) if user.image_url.present?
      # end
end
