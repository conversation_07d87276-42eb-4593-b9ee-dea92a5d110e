source 'http://rubygems.org'
ruby "2.2.4"

gem 'rails', '4.2.5'
# Use thin as the web server
gem 'puma'
gem 'jbuilder'
# gem 'scout_apm'
gem 'google-api-client', '~> 0.9'
gem 'thor', '~> 0.19'
gem 'sprockets'
gem "haml-rails"
gem 'TableCSV'
gem 'spreadsheet', '~> 1.0', '>= 1.0.8'
gem 'roo', '~> 2.1.0'

# See https://github.com/sstephenson/execjs#readme for more supported runtimes
# gem 'therubyracer', platforms: :ruby

# Use ActiveModel has_secure_password
# gem 'bcrypt-ruby', '~> 3.0.0'

# Used to manipulate images
gem 'mini_magick'
#razorpay
gem 'razorpay',git: 'https://github.com/guinex/razorpay-ruby.git'
#for connecting to mongodb
gem 'mongo',require: false
gem 'groupdate'
gem 'stripe'
# url shortner
gem 'shortener'

# hstore gem instead of serialize column

# gem 'activerecord-postgres-hstore'

# ckeditor for blog editing
gem 'ckeditor', require: true

# Remove for rails 5
gem 'multi_fetch_fragments'

#mimemagic : dependency for paperclip
gem 'mimemagic', git: 'https://github.com/mimemagicrb/mimemagic.git', ref: 'a4b038c6c1b9d76dac33d5711d28aaa9b4c42c66'
 
# Install paperclip
gem "paperclip"
# To Fetch Image Data
gem 'fastimage'

gem "delayed_paperclip"
gem 'fcm'
gem "curb"
# Used to logically delete the null orders
gem "paranoia", "~> 2.0"

# gem 'aws-sdk', "~> *******"
#gem 'aws-sdk', path: '/home/<USER>/Mirraw/aws-sdk-ruby'
gem 'aws-sdk',  git: 'https://<EMAIL>/blacklife/aws-sdk-ruby.git', branch: 'aws_sdk_1.8.1'
# gem 'aws-s3'
# Added gem for gati api integration.
gem 'gatikwe_api'

# Courier gems
gem 'ship_delight_api', git: 'https://<EMAIL>/blacklife/ship_delight.git', branch: 'tracking_url_changed'
#countries
gem 'iso_country_codes', '~> 0.7.2'
# For nice looking urls
gem "friendly_id", "~> 5.1.0"
# gem 'rack'
#gem 'delayed_task'

# Install awesome nested set to express hierarchical relationship
gem 'awesome_nested_set'
gem 'paypal-sdk-invoice'
gem 'paypal-express', git: 'https://<EMAIL>/blacklife/paypal-express.git', branch: 'capture_invoice_number'
gem "browser"

# Makes authentication with external providers easy
gem 'omniauth-facebook', '~> 5.0'
gem 'devise'
#gem 'safe_yaml', '0.6.3'
# gem "safe_yaml"
gem 'rails_admin', '~> 1.4.3'

# Handle authorization
gem 'cancancan', '~> 2.0'
# Handle pagination
gem "will_paginate", '~> 3.0'

# Nested forms
gem 'nested_form', :git => 'https://github.com/ryanb/nested_form.git'

gem 'pg'
gem 'airbrake', '~> 7.2'
gem 'paper_trail'
gem 'state_machines-activerecord'
gem 'wicked_pdf'
gem 'combine_pdf'
gem 'ruby-measurement', git: 'https://<EMAIL>/blacklife/ruby-measurement.git'

gem 'sitemap_generator'
gem 'carrierwave', '~> 1.0', require: false
gem 'fog'
gem 'net-sftp'
gem 'rqrcode'

gem 'geocoder'
gem 'chartkick'
# Adding tags
gem 'acts-as-taggable-on'
# gem 'mail_form'

gem 'httparty'
# gem 'gharpay', :git => 'https://github.com/shaileshjain/gharpay.git'

# html emails
# gem 'roadie'

# Meta store caching
gem 'rack-cache'
gem 'memcachier'
gem 'dalli'
gem 'connection_pool'
gem 'request_store'
gem 'phonelib'
#gem 'kgio'

gem 'rack-timeout'

# performance management

# father of lazy loading
gem 'promise'

# data grid
gem 'gdata_19', "~> 1.1.3"

gem "acts_as_follower", '~> 0.2.1'
gem 'sunspot_rails', git: 'https://bitbucket.org/blacklife/sunspot.git', branch: 'grouping_in_solr_with_master'
#gem 'sunspot_solr'
# gem "rsolr", "<2"
gem 'rsolr', '~> 2.1'
gem 'sunspot_index_queue', git: 'https://github.com/guinex/sunspot_index_queue.git'
gem 'rest-client'
# gem 'pry-debugger'

#active-record import
gem 'activerecord-import'

gem 'google_currency', '~> 3.4.0'
gem 'bootstrap3-datetimepicker-rails', '~> 4.17.37'
# # Asset template engines
gem 'jquery-rails'
gem 'bourbon'
gem 'bootstrap-sass', '3.1.1.0'
gem 'chosen-rails'
gem 'sass-rails', '~> 5.0'
gem 'coffee-rails', '~> 4.2.2'
gem 'uglifier', '>= 1.3.0'
gem 'turbolinks', '~> 5.0.0'
gem 'daemons', '~> 1.2', '>= 1.2.3'
group :development do
  gem 'sqlite3'
  gem 'heroku_san'
  gem 'ruby-graphviz', :require => 'graphviz' # Optional: only required for graphing
  # gem 'rails-footnotes', '>= 3.7.5.rc4'
  gem 'sunspot_solr' # optional pre-packaged Solr distribution for use in development
  gem "rails_best_practices"
  gem 'rubocop', require: false
  gem "better_errors"
  gem "binding_of_caller"
  gem 'meta_request'
  gem 'bullet'
  gem 'lol_dba'
  gem "oink"
  #gem "unicorn-rails"
  gem 'pry'
  gem 'annotate' #, :git => 'git://github.com/ctran/annotate_models.git'
  #gem "daemons"
  gem "awesome_print"

  # Access an IRB console on exception pages or by using <%= console %> in views
  gem 'web-console', '~> 2.0'
  gem 'spring'
end
gem "aws-ses", "~> 0.6.0", :require => 'aws/ses'

# Helpful
gem "scoped_search"
gem 'plupload-rails'
gem 'mechanize'

gem 'protected_attributes'

gem "recaptcha", "= 4.9.0"

#gem 'delayed_job_active_record'
#gem "delayed_job_web"

gem 'hirefire-resource'
#gem "timeline_fu" #, :git => 'git://github.com/shaileshjain/timeline_fu.git'
gem "koala"#, "~> 1.10.0rc"
gem 'gon'
#gem 'pubnub',require: false
gem 'json'
gem "squeel"
gem 'test-unit', '~> 3.0'
group :development, :test do
  gem 'rspec-rails', '3.0'
  gem 'factory_girl_rails', '4.1.0'
  gem 'byebug'
  # Helpful library to create fake data.
  gem 'faker'
  gem "mail_safe"
end

group :staging, :production, :admin, :seller do
  # Enable gzip compression on heroku
  # gem 'heroku-deflater'
  gem 'wkhtmltopdf-binary', '~> 0.12.3'
  gem "mail_safe"
end

group :test do
  # Pretty printed test output
  gem 'turn', :require => false
  gem 'capybara'
  gem 'launchy'
  gem 'machinist', '>= 2.0.0.beta2'
  #gem 'debugger'
  gem 'selenium-webdriver'
  #gem 'pry-debugger'
  gem "capybara-webkit"
  gem 'database_cleaner'

  gem 'shoulda-matchers', '~> 4.0', '<= 4.0.1'
  gem 'pdf-reader'
end

gem 'maxminddb', '0.1.7'
# Shipment Gems
gem 'bluedart' , :git => 'https://<EMAIL>/blacklife/bluedart.git' , :branch => 'bluedart_https_bug'
#gem 'ups'
gem 'shadowfax_api'

gem 'omniauth-google-oauth2', '~> 0.6.0'
gem 'omniauth-paypal-oauth2'

#barcode gem
gem 'has_barcode'

# Font gem to avoid CORS Issue in non web-kit browsers (like firefox, IE)
gem 'font_assets'

# gem 'informant-rails'

# ssl
gem 'rack-ssl-enforcer'

# SOAP Request
gem 'savon', '>= 2.9'
gem 'httpclient'
gem 'rubyntlm', '>= 0.4'
gem 'soap4r-ng'
# Alternative for mailcatcher
gem 'rack-cors', group: [:development, :staging, :seller]

# Image compression
gem 'paperclip-optimizer'
gem 'image_optim_pack'

gem 'devise-async'
# For Paypal Rest API
gem 'paypal-sdk-rest', :git => 'https://gitlab.com/mirraw-gems/paypal-ruby-sdk.git'
gem "puma_worker_killer"
gem 'simplecov', :require => false, :group => :test

# string matcher
gem 'fuzzy_match'

gem 'rails-observers'
gem 'actionpack-page_caching'
gem 'actionpack-action_caching'
gem 'activerecord-deprecated_finders'
# gem 'strong_parameters'
#gem 'skylight', '1.7.0'
gem 'ss2', :git => 'https://github.com/ShieldSquare/SS_Connector_RoR', :branch => 'ror_rel_v2.1.0'

gem 'ahoy_email', '~> 1.0', '>= 1.0.2'

gem 'wisper', '~> 2.0'
gem 'wisper-activejob', '~> 1.0'
gem 'rack-attack', '~> 5.4.2'
# gem 'redis-rails'

gem 'sidekiq', '= 5.0.0'
gem 'sidekiq-scheduler'
gem 'sidekiq-failures'

gem 'exception_notification'

gem 'omniauth-paypal-oauth2'

gem 'newrelic_rpm'

gem "lograge"
gem "rubyXL", "~> 3.4"


gem 'down'
gem 'streamio-ffmpeg'
